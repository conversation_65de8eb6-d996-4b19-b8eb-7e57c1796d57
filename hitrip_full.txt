皇包⻋开放API 9⽉
1.概述
本⽂档为渠道（即“合作伙伴”，下⽂简称“渠道”）接⼊皇包⻋系统接送机及⽇租包⻋服务涉及到
的技术相关⼯作。
如果已建⽴合作关系，密钥、验签、参数传递、返回值、请求协议、URL等参考线上版本，若发现本
⽂档与线上版本不⼀致的情况，请尽快确认，以免对接出错。
1.1专⽤术语
渠道：合作伙伴的简称
当地⼈：司机兼导游，可对应渠道的司机或导游。因此推送司机信息、推送导游信息，皇包⻋称为推
送当地⼈信息。
1.2请求与响应3.8
所有接⼝采⽤UTF-8编码，请求⽅式均为POST，参数通过REQUEST BODY提交。上线前须提供贵公司
线上IP，否则线上会有403错误。
请求参数传递采⽤JSON格式，特殊接⼝需要DESC加密。响应格式为JSON。
1.3时间规范
本⽂档中所涉及的时间为发⽣地时间，格式为yyyy-MM-dd HH:mm:ss（除特殊说明外）。例如：在巴
黎的⽤⻋时间是巴黎当地时间，但创建订单时间固定为北京时间。
1.4地址链接与密匙
测试环境地址：https://api-gw.test.huangbaoche.com/
联调环境：12345678
⽣产环境：https://api-gw.huangbaoche.com/
签名、DES加密使⽤同⼀密匙
1.5签名算法
1:对所有API请求参数（包括公共参数和业务参数，但除去sign参数），根据参数名称的ASCII码表的顺
序排序；
2:将排序好的参数名和参数值拼装在⼀起，并在拼装的字符串后加上渠道的secretKey；
3:把拼装好的字符串采⽤utf-8编码，使⽤签名算法对编码后的字节流进⾏摘要；
4:将摘要得到的字节流结果使⽤⼗六进制表⽰（⼩写）。
⽰例（以查询航班信息接⼝为例）
请求参数
业务参数："flightDate": "2020-04-16","flightNo": "CA181"
公共参数："cid": "1234567890","timestamp": "2020-01-14 10:57:00"
参数转化
cid1234567890flightDate2020-04-16flightNoCA181timestamp2020-01-08 10:57:0012345678
MD5签名
签名结果：412a4d874f61262b23ac7c767be59b47
签名⽰例代码：
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cglib.beans.BeanMap;
import java.io.UnsupportedEncodingException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
public class SecurityUtil {
public static void main(String[] args) {
String secretKey="12345678";
Map<String,String> params = new HashMap<>();
params.put("mobile","12345678");
params.put("areaCode","12345678");
String text = buildParamStr(params);
text = text + secretKey;
System.out.println("验签结果字符串: "+ text);
String mysign = DigestUtils.md5Hex(getContentBytes(text, "utf-8"));
System.out.println("参数加密签名"+mysign);
}
private static byte[] getContentBytes(String content, String charset) {
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
if (charset == null || "".equals(charset)) {
return content.getBytes();
}
try {
return content.getBytes(charset);
} catch (UnsupportedEncodingException e) {
throw new RuntimeException("MD5签名过程中出现错误,指定的编码集不对,您⽬
前指定的编码集是:" + charset);
}
}
public static String buildParamStr(final Map<String, String> params) {
// 1：检查参数是否已经排序
String[] keys = params.keySet().toArray(new String[0]);
Arrays.sort(keys);
// 2：把所有参数名和参数值串在⼀起
StringBuilder query = new StringBuilder();
for (String key : keys) {
String value = params.get(key);
// 剔除sign以及空值
if ("sign".equals(key) || StringUtils.isEmpty(value)) {
continue;
}
query.append(key).append(value);
}
return query.toString();
}
/**
* 对象转map
* @param bean
* @return
* @param <T>
*/
public static <T> Map<String, String> beanToStringMap(T bean) {
Map<String, String> beanMap = Maps.newHashMap();
if (bean != null) {
BeanMap tempMap = BeanMap.create(bean);
for (Object key : tempMap.keySet()) {
if (Objects.equals(key, "primaryKey")) {
continue;
}
Object o = tempMap.get(key);
if (null != o) {
beanMap.put(String.valueOf(key), objectToString(o));
}
}
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
}
return beanMap;
}
/**
* 将对象转换为String
* 若对象为集合，则转换为json
*
* @param object
* @return
*/
public static String objectToString(Object object) {
if (null == object) {
return "";
}
return !(object instanceof String)
? JSONObject.toJSONString(object) : object + "";
}
}
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
1.6 DES加密
模式：ECB
PKCS5Padding
使⽤base64编码
⽰例：
参数：{"a":"123","b":"456"}，秘钥：12345678
加密结果：bh9ldnZ6iTTszhIOYXy3OKzx8R9NpERd
2.准备接⼊
2.1皇包⻋提供基础数据
渠道与皇包⻋后续需要进⾏数据同步，包含城市信息（⽇租业务才有）、机场三字码、⻋型信息。皇
包⻋会提供基础数据接⼝，推荐使⽤该接⼝对接。如果渠道不⽀持定时拉取数据，可由皇包⻋提供静
态数据。
2.2渠道接⼊流程
⽬前皇包⻋“渠道开通”采⽤双向配合模式，即渠道接⼊前需要把第四步的配置参数告诉皇包⻋，经
皇包⻋操作开通后渠道才能正常使⽤。
2.2.1 渠道准备阶段
渠道需要提供下列参数给皇包⻋，皇包⻋开通后即可正常接⼊，如有疑问可与皇包⻋技术沟通。
• 渠道给皇包⻋提供的域名：必选
• 渠道给皇包⻋提供的ID：可选
• 渠道给皇包⻋提供的密钥： 可选，推荐使⽤皇包⻋API相同的密钥（32位）
• 渠道给皇包⻋提供的API版本号：可选
• 回调函数之确认订单URL路径：可选，当不提供时默认使⽤皇包⻋标准接⼝
• 回调函数之推送当地⼈URL路径：可选，当不提供时默认使⽤皇包⻋标准接⼝
• 回调函数之订单完成URL路径：可选，当不提供时默认使⽤皇包⻋标准接⼝
2.3获取token
获取token是调⽤皇包⻋API接⼝的第⼀步，相当于创建了⼀个登录凭证，其它的业务API接⼝，都需要
依赖于token来鉴权调⽤者⾝份。
因此在使⽤业务接⼝前，要使⽤正确的token。
请求⽅式： GET（HTTPS）
请求地址：GET https://api-gw.huangbaoche.com/otaorder-api/access/getAccessToken?cid=
{cid}&secretKey={secretKey}
注意事项：
获取到的token有效期为1个⼩时需要缓存token，⽤于后续接⼝的调⽤（注意：不能频繁调⽤
gettoken接⼝，否则会受到频率拦截）。当token失效或过期时，需要重新获取。
返回结果：
{
"data": "token",
"status": 200,
"success": true,
"traceId": "123"
}
1
2
3
4
5
6
7
参数说明：
3.皇包⻋提供的API
3. 1公共参数
调⽤任何⼀个API都必须传⼊的参数，⽬前⽀持的公共参数有：
请求参数与返回字段
参数名称 是否必须 类型 说明
cid true Stirng 渠道商⼾唯⼀ID
sign true Stirng 签名
timestamp true Stirng 时间戳，格式为yyyy￾MM-dd HH:mm:ss，时区
为GMT+8
encryptBody false Stirng 加密报⽂，是否需要加
密，请查看各API的说明
⽂档
返回字段
status true Integer 状态码
message false Stirng 描述
data false T 返回体
success true Boolean 请求是否成功
3.2机场服务
3.2.1查询机场列表
接⼝地址 /otaorder-api/common/v1.0/airport/listByNameAndSpell?token=ACCESS_TOKEN
请求参数：
参数名称 说明 是否必须 类型
keyword 关健字 false string
响应参数说明:
参数名称 说明 类型
airportName 机场名称 string
cityId 城市id int32
code 机场code string
⽰例请求报⽂：
{
"cid":"18",
"sign":"1234",
"timestamp":12345678,
"keyword":"成⽥"
}
1
2
3
4
5
6
相应参数⽰例：
{
"data": [
{
"airportName": "成⽥国际机场",
"cityId": 217,
"code": "NRT"
}
],
"status": 200,
"success": true
}
1
2
3
4
5
6
7
8
9
10
11
3.2.2查询机场列表并按⾸字⺟分组
接⼝地址 /otaorder-api/common/v1.0/airport/listGroupByNameAndSpell?token=ACCESS_TOKEN
请求参数：
参数名称 说明 是否必须 类型
keyword 关健字 false string
响应参数说明:
参数名称 说明 类型
分组map 分组map Map<String,List<
BasedataAirportDto>>
BasedataAirportDto
参数名称 说明 类型
airportName 机场名称 string
cityId 城市id int32
code 机场code string
⽰例请求报⽂：
{
"cid":"18",
"sign":"1234",
"timestamp":12345678,
"keyword":"成⽥"
}
1
2
3
4
5
6
响应报⽂
{
"data": {
"D": [
{
"airportName": "成⽥国际机场",
"cityId": 217,
"code": "NRT"
}
]
},
"status": 200,
"success": true
}
1
2
3
4
5
6
7
8
9
10
11
12
13
3.3城市服务
3.3.1查询城市列表
接⼝地址 /otaorder-api/common/v1.0/cities/listByNameAndSpell?token=ACCESS_TOKEN
请求参数：
参数名称 说明 是否必须 类型
keyword 关健字 false string
响应参数说明:
参数名称 说明 类型
cityId 城市ID int32
cityName 城市名称 string
cityNameEn 英⽂名称 string
continentId ⼤洲ID int32
continentName ⼤洲名称 string
countryId 国家ID int32
countryName 国家名称 string
countryNameEn 国家英⽂名称 string
spellInitial 城市拼⾳⾸字⺟ string
⽰例请求报⽂：
{
"cid":"18",
"sign":"1234",
"timestamp":12345678,
"keyword":"东京"
}
1
2
3
4
5
6
⽰例响应报⽂
{
"data": [
{
"cityId": 211,
"cityName": "吉隆坡",
"cityNameEn": "Kuala Lumpur",
"continentId": 6,
"continentName": "亚洲",
"countryId": 57,
"countryName": "⻢来西亚",
"spellInitial": "J"
},
{
"cityId": 276,
"cityName": "台北",
"cityNameEn": "Taipei",
"continentId": 6,
"continentName": "亚洲",
"countryId": 70,
"countryName": "中国台湾",
"spellInitial": "T"
}
],
"status": 200,
"success": true
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
} 26
3.3.2查询城市分组列表
接⼝地址 /otaorder-api/common/v1.0/cities/listGroupByNameAndSpell?token=ACCESS_TOKEN
请求参数：
参数名称 说明 是否必须 类型
keyword 关健字 false string
响应参数说明:
参数名称 说明 类型
分组map 分组map Map<String,List<
BasedataCitiesDto>>
BasedataCitiesDto
参数名称 说明 类型
cityId 城市ID int32
cityName 城市名称 string
cityNameEn 英⽂名称 string
continentId ⼤洲ID int32
continentName ⼤洲名称 string
countryId 国家ID int32
countryName 国家名称 string
countryNameEn 国家英⽂名称 string
spellInitial 城市拼⾳⾸字⺟ string
请求⼊参⽰例：
{
"cid":"18",
"sign":"1234",
"timestamp":12345678,
"keyword":"东京"
}
1
2
3
4
5
6
⽰例响应报⽂
{
"data": {
"A": [
{
"cityId": 14,
"cityName": "奥克兰",
"cityNameEn": "Auckland",
"continentId": 1,
"continentName": "⼤洋洲",
"countryId": 2,
"countryName": "新西兰",
"spellInitial": "A"
}
],
"B": [
{
"cityId": 138,
"cityName": "巴黎",
"cityNameEn": "Paris",
"continentId": 5,
"continentName": "欧洲",
"countryId": 36,
"countryName": "法国",
"spellInitial": "B"
}
]
},
"status": 200,
"success": true
}
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
3.4⻋型服务
3.4.1 查询⻋型列表
接⼝地址：/otaorder-api/common/v1.0/vehicleType/pageList?token=ACCESS_TOKEN
请求参数：
返回参数
请求⽰例
{
"cid":"1909498346",
"sign":"1234",
"timestamp":12345678
}
1
2
3
4
5
返回⽰例
{
"data": {
1
2
"listCount": 39,
"listData": [
{
"countryType": 2,
"countryVos": [
{
"countryId": 54,
"countryName": "韩国"
}
],
"guestNum": 5,
"id": 2,
"luggageNum": 5,
"pics": "https://fr-internal-new.oss-cn￾hangzhou.aliyuncs.com/production/FTH_wKoB-A0",
"seatNum": 6,
"vehicleTypeFullName": "经济6座(6-5-5)",
"vehicleTypeName": "经济6座",
"vehicleTypeRank": 1,
"vehicleTypeRankName": "经济"
},
{
"countryType": 2,
"countryVos": [
{
"countryId": 36,
"countryName": "法国"
},
{
"countryId": 49,
"countryName": "意⼤利"
},
{
"countryId": 70,
"countryName": "中国台湾"
},
{
"countryId": 60,
"countryName": "⽇本"
}
],
"guestNum": 7,
"id": 4,
"luggageNum": 4,
"pics": "https://fr-internal-new.oss-cn￾hangzhou.aliyuncs.com/production/FmmDmtQhCA0",
"seatNum": 8,
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
"vehicleTypeFullName": "经济8座(8-7-4)",
"vehicleTypeName": "经济8座",
"vehicleTypeRank": 1,
"vehicleTypeRankName": "经济"
}
]
},
"status": 200,
"success": true
}
48
49
50
51
52
53
54
55
56
57
3.4.2 查询全部有效⻋型
接⼝地址：/otaorder-api/common/v1.0/vehicleType/queryAllValid?token=ACCESS_TOKEN
请求参数：默认
返回参数
参数名称 说明 类型
guestNum 可乘⼈数 int32
id 主键id int32
luggageNum ⾏李数 int32
pics ⻋型图⽚(全路径) 逗号分隔 string
seatNum 座位数 int32
vehicleTypeFullName ⻋级名称, 规则：⻋型级别+座位数+座+⾏李 string
vehicleTypeName vehicleTypeName string
referenceCarModels 参考⻋型 string
请求⽰例
{
"cid":"1909498346",
"timestamp":12345678
}
1
2
3
4
返回⽰例
{
"data": [
{
"guestNum": 4,
"id": 1,
"luggageNum": 2,
"pics": "https://fr-internal-new.oss-cn￾hangzhou.aliyuncs.com/production/E1O_ZDq2oA0",
"seatNum": 5,
"vehicleTypeFullName": "经济5座(5-4-2)",
"vehicleTypeName": "经济5座"
}
],
"status": 200,
"success": true
}
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
3.4.3 查询国家下参考⻋型
接⼝地址：/otaorder-api/common/v1.0/vehicleType/referenceCarModelsByCountryId?
token=ACCESS_TOKEN
请求参数：
参数名称 说明 是否必须 类型
countryId 国家ID true Int32
返回参数
参数名称 说明 类型
id 主键id int32
referenceCarModels 参考⻋型 string
请求⽰例
{ 1
"cid":"1909498346",
"timestamp":12345678，
"sign": "1"
}
2
3
4
5
返回⽰例
{
"data": [
{
"id": 11,
"referenceCarModels": "奔驰GLE350/雷克萨斯NX350h或同级别⻋型"
}
],
"status": 200,
"success": true
}
1
2
3
4
5
6
7
8
9
10
11
3.5报价服务
3.5.1查询接机报价
接⼝地址 /otaorder-api/common/v1.0/quoteprice/pickup?token=ACCESS_TOKEN
请求参数：
参数名称 说明 是否必须 类型
airportCode 机场三字码 true string
airportName 机场名称 true string
carModelId ⻋型ID（可指定⻋型报
价，多个以逗号隔开）
false string
endAddress ⽬的地地址 true string
endDetailAddress ⽬的地详细地址 true string
endLocation ⽬的地坐标点 true string
serviceCityId 服务城市ID true int32
serviceCityName 服务城市名称 true string
serviceTime 服务时间 true string
startAddress 起始地址 true string
startDetailAddress 起始详细地址 true string
startLocation 起始坐标点 true string
响应参数说明:
参数名称 说明 类型 schema
carPriceInfoList ⻋型分组 list ⻋型分组报价信息
estDistance 预估距离（⽶） int32
estTime 预估时间（分钟） int32
lastOfferLimit 提前预定期 int32
noneCarsReason 没有报价原因 string
noneCarsState 没有报价状态（1，
正常报价）
int32
⻋型分组报价信息
参数名称 说明 类型 schema
carPrices ⻋型报价列表 list ⻋型报价信息
carTypeDesc ⻋型级别描述 string
seatNum ⻋座数 int32
⻋型报价信息
参数名称 说明 类型 schema
additionalPrices 增值服务费列表 list 增值服务费信息
capOfLuggage ⾏李数 int32
capOfPerson 乘坐⼈数 int32
carModelId ⻋型ID int32
carModelName ⻋型名称 string
carPictures ⻋型图⽚列表 array
expiredTime 报价有效时间 date-time
priceChannel ⻋型渠道价格（分） int32
priceMark 报价唯⼀标识 string
priceMarket ⻋型市场价格（分） int32
quoteCityId 报价来源城市ID int32
quoteCityName 报价来源城市名称 string
reconfirmFlag ⼆次确认标⽰（0，否；
1，是）
int32
reconfirmTip ⼆次确认提⽰ string
referenceCarModels 参考⻋型 string
urgentFlag 是否急单（0，否；1，
是）
int32
增值服务费信息
参数名称 说明 类型
additionalType 增值服务费类型 int32
additionalTypeDesc 增值服务费描述 string
additionalTypeName 增值服务费类型名称 string
priceChannelAdditional 增值服务费价格（分） int32
⽰例请求报⽂：
{
"cid":"1909498346",
1
2
"sign":"1234",
"timestamp":12345678,
"airportCode":"HND",
"airportName":"成⽥国际机场",
"carModelId":"1,2,3,4",
"endAddress":"东京希尔顿酒店",
"endDetailAddress":"东京希尔顿酒店⻔⼝",
"startLocation":"35.549441,139.779791",
"endLocation":"35.7110245884332,139.773156568408",
"serviceTime":"2024-06-20 09:00:00",
"serviceCityId":"217",
"startAddress":"成⽥国际机场",
"startDetailAddress":"成⽥国际机场"
}
3
4
5
6
7
8
9
10
11
12
13
14
15
16
⽰例响应报⽂
{
"data": {
"carPriceInfoList": [
{
"carPrices": [
{
"additionalPrices": [
{
"additionalType": 1,
"additionalTypeDesc": "⼉童座椅占1.5个位置",
"additionalTypeName": "⼉童座椅",
"priceChannelAdditional": 2000,
"priceChannelAdditionalSingle": 2000
},
{
"additionalType": 2,
"additionalTypeName": "举牌接机",
"priceChannelAdditional": 6000,
"priceChannelAdditionalSingle": 6000
},
{
"additionalType": 4,
"additionalTypeName": "协助办理⼊住",
"priceChannelAdditional": 6000,
"priceChannelAdditionalSingle": 6000
}
],
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
"capOfLuggage": 2,
"capOfPerson": 4,
"carModelId": 1,
"carModelName": "经济5座",
"carPictures": [
"https://fr-internal-new.oss-cn￾hangzhou.aliyuncs.com/production/E1O_ZDq2oA0"
],
"expiredTime": "2024-06-15 18:59:00",
"priceChannel": 38900,
"priceMark": "f1f90000-a82f-4da0-bbcb-
3455512df19e1718441940969",
"priceMarket": 38900,
"quoteCityId": 217,
"quoteCityName": "东京",
"reconfirmDetail": "",
"reconfirmFlag": 0,
"reconfirmTip": "",
"referenceCarModels": "丰⽥Venza（威飒）或同级别⻋型",
"settleBonus": 3200,
"settleBonusRate": 10,
"urgentFlag": 0,
"vehicleTypeRank": 1
}
],
"carTypeDesc": "5座",
"seatNum": 5
},
{
"carPrices": [
{
"additionalPrices": [
{
"additionalType": 1,
"additionalTypeDesc": "⼉童座椅占1.5个位置",
"additionalTypeName": "⼉童座椅",
"priceChannelAdditional": 2000,
"priceChannelAdditionalSingle": 2000
},
{
"additionalType": 2,
"additionalTypeName": "举牌接机",
"priceChannelAdditional": 6000,
"priceChannelAdditionalSingle": 6000
},
{
"additionalType": 4,
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
"additionalTypeName": "协助办理⼊住",
"priceChannelAdditional": 6000,
"priceChannelAdditionalSingle": 6000
}
],
"capOfLuggage": 2,
"capOfPerson": 6,
"carModelId": 3,
"carModelName": "经济7座",
"carPictures": [
"https://fr-internal-new.oss-cn￾hangzhou.aliyuncs.com/production/IX0w65FQdg0"
],
"expiredTime": "2024-06-15 18:59:00",
"priceChannel": 44800,
"priceMark": "f1f90000-a82f-4da0-bbcb-
3455512df19e1718441940969",
"priceMarket": 44800,
"quoteCityId": 217,
"quoteCityName": "东京",
"reconfirmDetail": "",
"reconfirmFlag": 0,
"reconfirmTip": "",
"referenceCarModels": "尼桑Presage或同级别⻋型",
"settleBonus": 3700,
"settleBonusRate": 10,
"urgentFlag": 0,
"vehicleTypeRank": 1
}
],
"carTypeDesc": "7座",
"seatNum": 7
},
{
"carPrices": [
{
"additionalPrices": [
{
"additionalType": 1,
"additionalTypeDesc": "⼉童座椅占1.5个位置",
"additionalTypeName": "⼉童座椅",
"priceChannelAdditional": 2000,
"priceChannelAdditionalSingle": 2000
},
{
"additionalType": 2,
"additionalTypeName": "举牌接机",
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
"priceChannelAdditional": 6000,
"priceChannelAdditionalSingle": 6000
},
{
"additionalType": 4,
"additionalTypeName": "协助办理⼊住",
"priceChannelAdditional": 6000,
"priceChannelAdditionalSingle": 6000
}
],
"capOfLuggage": 4,
"capOfPerson": 7,
"carModelId": 4,
"carModelName": "经济8座",
"carPictures": [
"https://fr-internal-new.oss-cn￾hangzhou.aliyuncs.com/production/FmmDmtQhCA0"
],
"expiredTime": "2024-06-15 18:59:00",
"priceChannel": 45600,
"priceMark": "f1f90000-a82f-4da0-bbcb-
3455512df19e1718441940969",
"priceMarket": 45600,
"quoteCityId": 217,
"quoteCityName": "东京",
"reconfirmDetail": "",
"reconfirmFlag": 0,
"reconfirmTip": "",
"referenceCarModels": "丰⽥TownAce或同级别⻋型",
"settleBonus": 3700,
"settleBonusRate": 10,
"urgentFlag": 0,
"vehicleTypeRank": 1
}
],
"carTypeDesc": "8座",
"seatNum": 8
}
],
"estDistance": 20310,
"estTime": 22,
"lastOfferLimit": 1,
"noneCarsReason": "报价成功",
"noneCarsState": 1,
"priceDescriptionList": [
[
{
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
"priceDescDetail": [
"接机订单以航班到达时间为准，免费等待90分钟"
],
"priceDescFee": "90",
"priceDescFeeName": "",
"priceDescType": 5,
"priceDescTypeName": "免费等待90分钟",
"priceDescUnit": "分钟"
}
],
[
{
"priceDescDetail": [
"当地⼈服务费、⻋辆服务费、⼩费、油费、过路费、⾼速费、停⻋
费、进城费、空驶费、接送机夜间费、包⻋餐补、跨天包⻋住宿补贴"
],
"priceDescFeeName": "⽤⻋基础服务费",
"priceDescType": 4,
"priceDescTypeName": "费⽤包含"
}
],
[
{
"priceDescDetail": [
"超时等待费按照30分钟为⼀档收取(不满30分钟按30分钟计算)"
],
"priceDescFee": "¥100/30分钟",
"priceDescFeeName": "超时⻓费",
"priceDescType": 7,
"priceDescTypeName": "费⽤不含",
"priceDescUnit": "30分钟"
},
{
"priceDescDetail": [
"⾏程中产⽣额外费⽤请线下⽀付给当地⼈"
],
"priceDescFeeName": "",
"priceDescType": 99,
"priceDescTypeName": "费⽤不含"
}
]
],
"priceStockCancelRule": {
"cancelRuleDesc": [
"[当前时间]距离[服务时间]⼩于12⼩时，1⼩时⽆⼈接单退款。",
"[当前时间]距离[服务时间]为12~24⼩时，4⼩时⽆⼈接单退款。",
"[当前时间]距离[服务时间]为24~48⼩时，8⼩时⽆⼈接单退款。",
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
"[当前时间]距离[服务时间]为48~72⼩时，24⼩时⽆⼈接单退款。",
"[当前时间]距离[服务时间]为72~120⼩时，48⼩时⽆⼈接单退款。",
"[当前时间]距离[服务时间]⼤于120⼩时，72⼩时⽆⼈接单退款。"
],
"currentRule": "H_RANGE_72_TO_120",
"currentRuleName": "[当前时间]距离[服务时间]为72~120⼩时，48⼩时⽆⼈接
单退款。",
"refundTime": 48,
"refundTimeName": "48⼩时",
"remainTime": "111"
},
"priceTagList": [
{
"tagDetail": "⼀名中⽂当地⼈全程为您服务",
"tagName": "中⽂当地⼈",
"tagType": 0
},
{
"tagDetail": "⾏程开始前24⼩时内，司机会通过APP、短信、电话等与您联
系，确定您的时间、⼈数及⾏李数、联系⽅式、接送位置等",
"tagName": "⾏前联系",
"tagType": 0
},
{
"tagDetail": "从航班实际降落后开始时间计算，免费等待90分钟，超时请查
看费⽤说明",
"tagName": "免费等待90分钟",
"tagType": 1
}
]
},
"status": 200,
"success": true
}
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
3.5.2查询送机报价
接⼝地址 /otaorder-api/common/v1.0/quoteprice/transfer?token=ACCESS_TOKEN
请求参数和响应参数同3.5.1接机
3.5.3查询包⻋报价
接⼝地址 /otaorder-api/common/v1.0/quoteprice/daily?token=ACCESS_TOKEN
请求参数
PriceSingleDailyRequestParam
PassPoiParam
返回参数
PriceDailyCarGroupResponseVo
PriceDailyCarDetailResponseVo
PriceDailyCarDetailResponseVo
PriceDailyCarIndexResponseVo
PriceStockRuleQueryVO
PriceDescription
PriceStockCancelRuleResponseVO
PriceTagInfo
请求⽰例
{
"cid":1185744601,
"sign":"cdbf2490ec60ac891a85372c25289e40",
"timestamp":"2024-08-01",
"channelId": 1989691208,
"serviceTime": "2024-11-14 09:00:00",
"singleDailyParamList":
[
{
"distance": 23900.0,
"duration": 45,
"endAddress": "8-7-13,GINZA,CHUOKU,银座/筑地/新桥地区,中央区,东京,东京
都,104-0061,⽇本",
"endDetailAddress": "8-7-13,GINZA,CHUOKU,银座/筑地/新桥地区,中央区,东
京,东京都,104-0061,⽇本",
"endLocation": "35.668016,139.760382",
"endServiceCityId": 217,
"endServiceCityName": "东京",
"halfDay": 0,
"passPoiList":
[
{
"location": "35.668016,139.760382"
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
},
{
"location": "35.7147651,139.7966553"
},
{
"location": "35.685175,139.7527995"
},
{
"location": "35.6585805,139.7454329"
},
{
"location": "35.668016,139.760382"
}
],
"startAddress": "8-7-13,GINZA,CHUOKU,银座/筑地/新桥地区,中央区,东京,
东京都,104-0061,⽇本",
"startDetailAddress": "8-7-13,GINZA,CHUOKU,银座/筑地/新桥地区,中央区,
东京,东京都,104-0061,⽇本",
"startLocation": "35.668016,139.760382",
"startServiceCityId": 217,
"startServiceCityName": "东京",
"startServiceTime": "2024-11-14 09:00:00",
"tourType": 1
},
{
"distance": 150450.0,
"duration": 187,
"endAddress": "837 Sengokuhara, Hakone, Ashigarashimo District,
Kanagawa Prefecture 250-0631, Japan",
"endDetailAddress": "837 Sengokuhara, Hakone, Ashigarashimo
District, Kanagawa Prefecture 250-0631, Japan",
"endLocation": "35.2682772,139.0094804",
"endServiceCityId": 3351,
"endServiceCityName": "箱根",
"halfDay": 0,
"passPoiList":
[
{
"location": "35.668016,139.760382"
},
{
"location": "35.2103227,139.01125909999996"
},
{
"location": "35.243473,139.01974"
},
{
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
"location": "35.245379,139.0567285"
},
{
"location": "35.2682772,139.0094804"
}
],
"startAddress": "8-7-13,GINZA,CHUOKU,银座/筑地/新桥地区,中央区,东京,
东京都,104-0061,⽇本",
"startDetailAddress": "8-7-13,GINZA,CHUOKU,银座/筑地/新桥地区,中央区,
东京,东京都,104-0061,⽇本",
"startLocation": "35.668016,139.760382",
"startServiceCityId": 217,
"startServiceCityName": "东京",
"startServiceTime": "2024-11-15 09:00:00",
"tourType": 2
},
{
"distance": 72500.0,
"duration": 140,
"endAddress": "",
"endDetailAddress": "",
"endLocation": "35.491832,138.780122",
"endServiceCityId": 3450,
"endServiceCityName": "富⼠⼭",
"halfDay": 0,
"passPoiList":
[
{
"location": "35.2682772,139.0094804"
},
{
"location": "35.5108344,138.75832760000003"
},
{
"location": "35.522526,138.768637"
},
{
"location": "35.460087,138.832772"
},
{
"location": "35.49326286849875,138.8040849969706"
},
{
"location": "35.491832,138.780122"
}
],
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
"startAddress": "837 Sengokuhara, Hakone, Ashigarashimo District,
Kanagawa Prefecture 250-0631, Japan",
"startDetailAddress": "837 Sengokuhara, Hakone, Ashigarashimo
District, Kanagawa Prefecture 250-0631, Japan",
"startLocation": "35.2682772,139.0094804",
"startServiceCityId": 3351,
"startServiceCityName": "箱根",
"startServiceTime": "2024-11-16 09:00:00",
"tourType": 1
},
{
"distance": 135560.0,
"duration": 124,
"endAddress": "8-7-13,GINZA,CHUOKU,银座/筑地/新桥地区,中央区,东京,东京
都,104-0061,⽇本",
"endDetailAddress": "8-7-13,GINZA,CHUOKU,银座/筑地/新桥地区,中央区,东
京,东京都,104-0061,⽇本",
"endLocation": "35.668016,139.760382",
"endServiceCityId": 217,
"endServiceCityName": "东京",
"halfDay": 0,
"passPoiList":
[
{
"location": "35.491832,138.780122"
},
{
"location": "35.6654861,139.7706668"
},
{
"location": "35.6599997,139.7015703"
},
{
"location": "35.668016,139.760382"
}
],
"startAddress": "",
"startDetailAddress": "",
"startLocation": "35.491832,138.780122",
"startServiceCityId": 3450,
"startServiceCityName": "富⼠⼭",
"startServiceTime": "2024-11-17 09:00:00",
"tourType": 2
}
]
}
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
返回⽰例
{
"data": [
{
"carPriceInfoList": [
{
"carPrices": [
{
"additionalPrices": [
{
"additionalType": 1,
"additionalTypeDesc": "⼉童座椅占1.5个位置",
"additionalTypeName": "⼉童座椅",
"priceChannelAdditional": 0,
"priceChannelAdditionalSingle": 0,
"priceSysAdditional": 0,
"priceSysAdditionalSingle": 0
}
],
"capOfLuggage": 2,
"capOfPerson": 4,
"carModelId": 6,
"carModelName": "舒适5座",
"carPictures": [
"https://fr-internal-new.oss-cn￾hangzhou.aliyuncs.com/production/CuaJ7p6hxA0"
],
"dailyCarIndexList": [
{
"day": 1,
"endCityId": 42,
"endCityName": "洛杉矶",
"halfDay": 0,
"index": 1,
"priceAccommodation": 0,
"priceBonus": 21100,
"priceChannel": 294000,
"priceCityId": 42,
"priceCityName": "洛杉矶",
"priceEndEmpty": 0,
"priceMarket": 294000,
"priceProportion": 50,
"priceStartEmpty": 0,
"priceSysTotal": 232300,
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
"serviceDistance": 300,
"serviceTime": "2024-09-23",
"serviceTimes": 10,
"startCityId": 42,
"startCityName": "洛杉矶",
"totalDay": 2,
"tourType": 2,
"tourTypeName": "市内+周边地区包⻋"
},
{
"day": 2,
"endCityId": 42,
"endCityName": "洛杉矶",
"halfDay": 0,
"index": 2,
"priceAccommodation": 0,
"priceBonus": 21100,
"priceChannel": 294000,
"priceCityId": 42,
"priceCityName": "洛杉矶",
"priceEndEmpty": 0,
"priceMarket": 294000,
"priceProportion": 50,
"priceStartEmpty": 0,
"priceSysTotal": 232300,
"serviceDistance": 300,
"serviceTime": "2024-09-24",
"serviceTimes": 10,
"startCityId": 42,
"startCityName": "洛杉矶",
"totalDay": 2,
"tourType": 2,
"tourTypeName": "市内+周边地区包⻋"
}
],
"expiredTime": "2024-09-12 19:47:10",
"priceChannel": 588000,
"priceMark": "0d50a7ec-f0fb-4146-8b36-
af3c8122ca971726134430138",
"priceSysTotal": 464600,
"quoteCityId": 42,
"quoteCityName": "洛杉矶",
"reconfirmDetail": "",
"reconfirmFlag": 0,
"reconfirmTip": "",
"referenceCarModels": "丰⽥普锐斯/丰⽥RAV4或同级别⻋型",
"settleBonus": 42200,
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
"urgentFlag": 0,
"vehicleTypeRank": 2
},
{
"additionalPrices": [
{
"additionalType": 1,
"additionalTypeDesc": "⼉童座椅占1.5个位置",
"additionalTypeName": "⼉童座椅",
"priceChannelAdditional": 0,
"priceChannelAdditionalSingle": 0,
"priceSysAdditional": 0,
"priceSysAdditionalSingle": 0
}
],
"capOfLuggage": 2,
"capOfPerson": 4,
"carModelId": 11,
"carModelName": "豪华5座",
"carPictures": [
"https://fr-internal-new.oss-cn￾hangzhou.aliyuncs.com/production/EYtD2X9F1A0"
],
"dailyCarIndexList": [
{
"day": 1,
"endCityId": 42,
"endCityName": "洛杉矶",
"halfDay": 0,
"index": 1,
"priceAccommodation": 0,
"priceBonus": 24700,
"priceChannel": 344000,
"priceCityId": 42,
"priceCityName": "洛杉矶",
"priceEndEmpty": 0,
"priceMarket": 344000,
"priceProportion": 50,
"priceStartEmpty": 0,
"priceSysTotal": 271800,
"serviceDistance": 300,
"serviceTime": "2024-09-23",
"serviceTimes": 10,
"startCityId": 42,
"startCityName": "洛杉矶",
"totalDay": 2,
"tourType": 2,
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
"tourTypeName": "市内+周边地区包⻋"
},
{
"day": 2,
"endCityId": 42,
"endCityName": "洛杉矶",
"halfDay": 0,
"index": 2,
"priceAccommodation": 0,
"priceBonus": 24700,
"priceChannel": 344000,
"priceCityId": 42,
"priceCityName": "洛杉矶",
"priceEndEmpty": 0,
"priceMarket": 344000,
"priceProportion": 50,
"priceStartEmpty": 0,
"priceSysTotal": 271800,
"serviceDistance": 300,
"serviceTime": "2024-09-24",
"serviceTimes": 10,
"startCityId": 42,
"startCityName": "洛杉矶",
"totalDay": 2,
"tourType": 2,
"tourTypeName": "市内+周边地区包⻋"
}
],
"expiredTime": "2024-09-12 19:47:10",
"priceChannel": 688000,
"priceMark": "0d50a7ec-f0fb-4146-8b36-
af3c8122ca971726134430138",
"priceSysTotal": 543600,
"quoteCityId": 42,
"quoteCityName": "洛杉矶",
"reconfirmDetail": "",
"reconfirmFlag": 0,
"reconfirmTip": "",
"referenceCarModels": "奔驰GLE350/雷克萨斯ES350或同级别⻋型",
"settleBonus": 49400,
"urgentFlag": 0,
"vehicleTypeRank": 3
}
],
"carTypeDesc": "5座",
"seatNum": 5
},
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
{
"carPrices": [
{
"additionalPrices": [
{
"additionalType": 1,
"additionalTypeDesc": "⼉童座椅占1.5个位置",
"additionalTypeName": "⼉童座椅",
"priceChannelAdditional": 0,
"priceChannelAdditionalSingle": 0,
"priceSysAdditional": 0,
"priceSysAdditionalSingle": 0
}
],
"capOfLuggage": 2,
"capOfPerson": 6,
"carModelId": 8,
"carModelName": "舒适7座",
"carPictures": [
"https://fr-internal-new.oss-cn￾hangzhou.aliyuncs.com/production/IBF8MitFug0"
],
"dailyCarIndexList": [
{
"day": 1,
"endCityId": 42,
"endCityName": "洛杉矶",
"halfDay": 0,
"index": 1,
"priceAccommodation": 0,
"priceBonus": 24500,
"priceChannel": 342100,
"priceCityId": 42,
"priceCityName": "洛杉矶",
"priceEndEmpty": 0,
"priceMarket": 342100,
"priceProportion": 50,
"priceStartEmpty": 0,
"priceSysTotal": 270300,
"serviceDistance": 300,
"serviceTime": "2024-09-23",
"serviceTimes": 10,
"startCityId": 42,
"startCityName": "洛杉矶",
"totalDay": 2,
"tourType": 2,
"tourTypeName": "市内+周边地区包⻋"
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
},
{
"day": 2,
"endCityId": 42,
"endCityName": "洛杉矶",
"halfDay": 0,
"index": 2,
"priceAccommodation": 0,
"priceBonus": 24500,
"priceChannel": 342100,
"priceCityId": 42,
"priceCityName": "洛杉矶",
"priceEndEmpty": 0,
"priceMarket": 342100,
"priceProportion": 50,
"priceStartEmpty": 0,
"priceSysTotal": 270300,
"serviceDistance": 300,
"serviceTime": "2024-09-24",
"serviceTimes": 10,
"startCityId": 42,
"startCityName": "洛杉矶",
"totalDay": 2,
"tourType": 2,
"tourTypeName": "市内+周边地区包⻋"
}
],
"expiredTime": "2024-09-12 19:47:10",
"priceChannel": 684200,
"priceMark": "0d50a7ec-f0fb-4146-8b36-
af3c8122ca971726134430138",
"priceSysTotal": 540600,
"quoteCityId": 42,
"quoteCityName": "洛杉矶",
"reconfirmDetail": "",
"reconfirmFlag": 0,
"reconfirmTip": "",
"referenceCarModels": "丰⽥Sienna (塞纳）/丰⽥Sienna (塞纳）或同级
别⻋型",
"settleBonus": 49000,
"urgentFlag": 0,
"vehicleTypeRank": 2
},
{
"additionalPrices": [
{
"additionalType": 1,
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
"additionalTypeDesc": "⼉童座椅占1.5个位置",
"additionalTypeName": "⼉童座椅",
"priceChannelAdditional": 0,
"priceChannelAdditionalSingle": 0,
"priceSysAdditional": 0,
"priceSysAdditionalSingle": 0
}
],
"capOfLuggage": 3,
"capOfPerson": 6,
"carModelId": 12,
"carModelName": "豪华7座",
"carPictures": [
"https://fr-internal-new.oss-cn￾hangzhou.aliyuncs.com/production/E1O_DRr1TA0"
],
"dailyCarIndexList": [
{
"day": 1,
"endCityId": 42,
"endCityName": "洛杉矶",
"halfDay": 0,
"index": 1,
"priceAccommodation": 0,
"priceBonus": 29000,
"priceChannel": 403900,
"priceCityId": 42,
"priceCityName": "洛杉矶",
"priceEndEmpty": 0,
"priceMarket": 403900,
"priceProportion": 50,
"priceStartEmpty": 0,
"priceSysTotal": 319100,
"serviceDistance": 300,
"serviceTime": "2024-09-23",
"serviceTimes": 10,
"startCityId": 42,
"startCityName": "洛杉矶",
"totalDay": 2,
"tourType": 2,
"tourTypeName": "市内+周边地区包⻋"
},
{
"day": 2,
"endCityId": 42,
"endCityName": "洛杉矶",
"halfDay": 0,
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286
287
288
289
290
291
292
293
294
295
296
297
298
299
300
301
302
303
304
305
306
307
308
309
310
311
312
313
314
315
316
317
"index": 2,
"priceAccommodation": 0,
"priceBonus": 29000,
"priceChannel": 403900,
"priceCityId": 42,
"priceCityName": "洛杉矶",
"priceEndEmpty": 0,
"priceMarket": 403900,
"priceProportion": 50,
"priceStartEmpty": 0,
"priceSysTotal": 319100,
"serviceDistance": 300,
"serviceTime": "2024-09-24",
"serviceTimes": 10,
"startCityId": 42,
"startCityName": "洛杉矶",
"totalDay": 2,
"tourType": 2,
"tourTypeName": "市内+周边地区包⻋"
}
],
"expiredTime": "2024-09-12 19:47:10",
"priceChannel": 807800,
"priceMark": "0d50a7ec-f0fb-4146-8b36-
af3c8122ca971726134430138",
"priceSysTotal": 638200,
"quoteCityId": 42,
"quoteCityName": "洛杉矶",
"reconfirmDetail": "",
"reconfirmFlag": 0,
"reconfirmTip": "",
"referenceCarModels": "丰⽥塞纳/雷克萨斯GX460或同级别⻋型",
"settleBonus": 58000,
"urgentFlag": 0,
"vehicleTypeRank": 3
}
],
"carTypeDesc": "7座",
"seatNum": 7
}
],
"composeOrder": 0,
"lastOfferLimit": 4,
"noneCarsReason": "报价成功",
"noneCarsState": 1,
"priceDescriptionList": [
[
318
319
320
321
322
323
324
325
326
327
328
329
330
331
332
333
334
335
336
337
338
339
340
341
342
343
344
345
346
347
348
349
350
351
352
353
354
355
356
357
358
359
360
361
362
363
{
"priceDescDetail": [
"免费等待30分钟分钟，以订单服务时间开始计算，超时将收取超时费⽤"
],
"priceDescFee": "30",
"priceDescFeeName": "",
"priceDescType": 12,
"priceDescTypeName": "包⻋须知",
"priceDescUnit": "分钟"
}
],
[
{
"priceDescDetail": [
"当地⼈服务费、⻋辆服务费、⼩费、油费、过路费、⾼速费、停⻋费、进城费、空
驶费、接送机夜间费、包⻋餐补、跨天包⻋住宿补贴"
],
"priceDescFeeName": "⽤⻋基础服务费",
"priceDescType": 4,
"priceDescTypeName": "费⽤包含"
}
],
[
{
"priceDescDetail": [
"超时等待费按照30分钟为⼀档收取(不满30分钟按30分钟计算)"
],
"priceDescFee": "¥190/30分钟",
"priceDescFeeName": "超时⻓费",
"priceDescType": 9,
"priceDescTypeName": "费⽤不含",
"priceDescUnit": "30分钟"
},
{
"priceDescDetail": [
"夜间服务时间段：22:00⾄次⽇07:00，涉及夜间服务将产⽣夜间服务费"
],
"priceDescFee": "¥200/天",
"priceDescFeeName": "夜间服务费",
"priceDescType": 11,
"priceDescTypeName": "费⽤不含",
"priceDescUnit": "天"
},
{
"priceDescDetail": [
"当⽇服务⼩时与⾥程仅当天有效，不累计到第⼆天"
],
364
365
366
367
368
369
370
371
372
373
374
375
376
377
378
379
380
381
382
383
384
385
386
387
388
389
390
391
392
393
394
395
396
397
398
399
400
401
402
403
404
405
406
407
408
409
"priceDescFee": "¥8/公⾥",
"priceDescFeeName": "超公⾥费",
"priceDescType": 10,
"priceDescTypeName": "费⽤不含",
"priceDescUnit": "公⾥"
},
{
"priceDescDetail": [
"⾏程中产⽣额外费⽤请线下⽀付给向导"
],
"priceDescFeeName": "",
"priceDescType": 99,
"priceDescTypeName": "费⽤不含"
}
]
],
"priceStockCancelRule": {
"cancelRuleDesc": [
"[当前时间]距离[服务时间]⼩于12⼩时，1⼩时⽆⼈接单退款。",
"[当前时间]距离[服务时间]为12~24⼩时，4⼩时⽆⼈接单退款。",
"[当前时间]距离[服务时间]为24~48⼩时，8⼩时⽆⼈接单退款。",
"[当前时间]距离[服务时间]为48~72⼩时，24⼩时⽆⼈接单退款。",
"[当前时间]距离[服务时间]为72~120⼩时，48⼩时⽆⼈接单退款。",
"[当前时间]距离[服务时间]⼤于120⼩时，72⼩时⽆⼈接单退款。"
],
"currentRule": "H_RANGE_72_TO_ABOVE",
"currentRuleName": "[当前时间]距离[服务时间]⼤于120⼩时，72⼩时⽆⼈接单退
款。",
"refundTime": 72,
"refundTimeName": "72⼩时",
"remainTime": "282"
},
"standardServiceType": 4
}
],
"status": 200,
"success": true
}
410
411
412
413
414
415
416
417
418
419
420
421
422
423
424
425
426
427
428
429
430
431
432
433
434
435
436
437
438
439
440
441
442
443
444
445
446
3.5.4检查包⻋城市和时间有效性
接⼝地址 /otaorder-api/common/v1.0/quoteprice/checkCityAndDate?token=ACCESS_TOKEN
请求参数
响应参数
请求⽰例
{
"cid":"1909498346",
"sign":"1234",
"timestamp":12345678,
"cityId": 217,
"date":"2024-07-07"
}
1
2
3
4
5
6
7
返回⽰例
{
"data": {
"noneCarsReason": "报价成功",
"noneCarsState": 1
},
"status": 200,
"success": true
}
1
2
3
4
5
6
7
8
3.5.5检查包⻋报价城市
接⼝地址 /otaorder-api/common/v1.0/quoteprice/checkCity?token=ACCESS_TOKEN
请求参数
响应参数
PriceArgsServiceRangeDTO
请求⽰例
{
"cid":"1909498346",
"sign":"1234",
"timestamp":12345678,
"cityId": 217
}
1
2
3
4
5
6
返回⽰例
{
"data": {
"cityId": 217,
"cityName": "东京",
"cityServiceUpgrades": 1,
"countryId": 60,
"countryName": "⽇本",
"msg": [],
"priceServiceRange": [
{
"key": 1,
"name": "市内包⻋",
"serviceContainTime": 10,
"serviceMaxDistance": 100
},
{
"key": 2,
"name": "市内+周边地区包⻋",
"serviceContainTime": 10,
"serviceMaxDistance": 300
},
{
"key": 3,
"name": "包⻋前往其他城市/地区",
"serviceContainTime": 10,
"serviceMaxDistance": 600
}
],
"status": 1
},
"status": 200,
"success": true
}
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
3.6订单服务
3.6.1接机下单（需要加密）
接机下单，使⽤公共参数请求接⼝，encryptBody为下单参数序列化后使⽤DES加密的结果
接⼝地址 /otaorder-api/common/v1.0/order/pickup?token=ACCESS_TOKEN
请求参数：
参数名称 说明 是否必须 类型
flightAirportCode 起⻜机场三字码 false string
flightAirportName 起⻜机场名称 false string
flightArriveTime 航班计划到达时间 false date-time
flightDestAirportName 降落机场名称 true string
flightDestBuilding 降落机场航站楼 false string
flightDestCode 降落机场三字码 true string
flightFlyTime 航班起⻜时间 false date-time
flightNo 航班编号 true string
orderNo 订单号 false string
orderThirdNo 第三⽅订单号 true string
priceActual 实际⽀付价格 单位 分 true int32
priceChannel 渠道价格（不包含增项费⽤）
单位 分
true int32
serviceCarModel ⻋型Id true string
serviceCarModelName ⻋型 true string
serviceCityId 服务城市id true int32
serviceCityName 服务城市名称 true string
serviceContinentId 服务⼤洲id false int32
serviceContinentName 服务⼤洲名称 false string
serviceCountryId 服务国家id false int32
serviceCountryName 服务国家名称 false string
serviceDestAddress 终⽌地 true string
serviceDestAddressDetail 终⽌地详细地址 true string
serviceDestPoi poi终点Id false string
serviceDestPoint 终⽌地经纬度 true string
serviceEndCityId 终⽌地 城市ID false int32
serviceEndCityName 终⽌地 城市名 false string
servicePassagerAreacode 乘⻋⼈⼿机区号 true string
servicePassagerMobile 乘⻋⼈⼿机号码 true string
servicePassagerName 乘⻋⼈名称 true string
serviceStartAddress 出发地 true string
serviceStartAddressDetail 出发地详细地址 true string
serviceStartPoi poi起点Id false string
serviceStartPoint 出发地经纬度 true string
serviceTime 服务时间 true date-time
userRemark ⽤⼾留⾔ false string
orderChannel 商⼾id true string
orderChannelName 商⼾名称 true string
airportPriceQuestParam 报价参数 true airportPrice
QuestPara
m
orderAdditionals 增项费⽤报价参数 FALSE orderAdditi
onals
orderAdditionals
additionalTypeName 增项费⽤类别名称 FALSE string
priceActual 实际⽀付价格单位分 FALSE integer(int32)
priceChannel 渠道价格单位分 FALSE integer(int32)
additionalType 增项费⽤类别 FALSE integer(int32)
airportPriceQuestParam
airportCode 机场三字码 TRUE string
airportName 机场名称 TRUE string
channelId 渠道ID TRUE integer(int64)
endAddress ⽬的地地址 TRUE string
endDetailAddress ⽬的地详细地址 TRUE string
endLocation ⽬的地坐标点 TRUE string
serviceCityId 服务城市ID TRUE integer(int32)
serviceCityName 服务城市名称 TRUE string
serviceTime 服务时间 TRUE string
startAddress 起始地址 TRUE string
startCityId 起始城市ID TRUE integer(int32)
startCityName 起始城市name TRUE string
startDetailAddress 起始详细地址 TRUE string
startLocation 起始坐标点 TRUE string
请求参数⽰例：
{
"sign": "1234",
"cid": "1909498346",
"timestamp":1232312,
"encryptBody":
"mdegM766yn9N1O10fwephiOv6VgJiaV+oLPhnebD2TXAujHSPge5K22U0Tt4cVCnh68NajpifSGgs
+Gd5sPZNWZhoOREEsolNQRoVqKcAqLwz+VUGvUHuTvAL18iSGgzfCWKvNsJPoQ+8iT+8nlSskwCeEJ
ltReLTrMI1VofOV51z1U+Wc2MNNXd6CFSmeU6LbWpI5rfi8IikwHKhuHjuwfDhMuplrGXkH/kTgMtQ
p4FFaAp4cu1k5E/sYo4MFka1sFYDG+qscxaQGBfy2sPTW2O+qN3y3PsVVfIjXNxmWTNzbGxqWXMxCm
pOm30ZVUlM+9zQFr4AJ+rQRxMqdsAOwrjXM5RrdLHHYXlsa1ctQmxz4efZooz+wZlxjjlVA9woN+W8
bg5EaL+SXUd4crR1H1UlL7a00Ih82qTSmsOQoq43pzZDS2jZ/Gsxq2hFFFBbXGpVz5bLR8lCvrXfrv
N3pg7ASr2e1PQsPTLuB+rUmqqhiN1gKVbiJ4yZfSIURQltrHx/T+o7NX1AH+TxadLkwUvbJ/DhKIn7
uE7JvSKXrO9qxIuk2jOeTbJEsQfeHvkWYvz76sdHNW9kOarPRamopwGXqTdbN1xEju0x4S+uuLaBfj
vU5SlyO7hOyb0il6zha2viGsYPBI8h6a/jjsnH+7hOyb0il6zBrD+nQM9OAl8UrkAQwTz46+z6ULlx
kqLD1y8T6IlMTAOf2Ljiqu7f+7hOyb0il6zPSaYKwq+Nfk+i0MfOmVNNR/vkWy9pM/KCudNGEjMdKE
mAlFHA5CnVxeHtFVpEQcyEZh9XLDgEix+3WI9PH6zm/FwtDcTCNMm3QhHSyCS13tFy7C28FZAjwPKs
hDBPewqDS9eGYIE5pS1uNT0QzL1lFMgfbBSVzUWJSx867rlzbZVk//39ILrtTsck3wUlyfUD1y8T6I
lMTAOf2Ljiqu7f1jpIo+JF4RarFtRXmxpZZkluSwNi29FPOvShyQUFepSefXZ/WBjsQRyIKdm35Pep
0wggeMiw1pNm3tKxxV08O9Nc72aP+Qp2nlv/LNT5CdATGNNqTZwsiZcnqJ124bkeq3bbQxwSsY8Ip9
1
2
3
4
5
p8cyJ+MjzapNKaw5CirjenNkNLaNngBnJP64fVqABu/CSyGduuEo5ZAChMWKj4+Hcbp04TEaBEKq6g
cd5o6uVSHgv/WWHvH1/I84doV0k1tfbLrCqu4jqIaOxZZ6L2apJs+7j1TEaekeEFZTP394oDGHDnsS
8Sqp4iGs8Kf3ynz3h4zjXFWLAgn9qwxBadD4HmwZbjzxyIKdm35Pepx9V85kkmhAm+zsr3LkhMCDi+
2puIjR3X98sdgD6S4NJ60d57K3GflYGKjxWBPpC3Updy1ZN248x8n//7mioRDt9sxduK+GfibeUen2
UXoZ5Ol7nZb3jDCJyIKdm35Pepx9V85kkmhAmPXhQazsVPoXLxPpAhVRWiqviLTA/g42PicY4NjXyu
Rs62G4YYxBr9cxmfboWJGcmIUH6XxRvK78RCgUlBQnhfMOVLtx5I+5gyEeo9TNroyDcDlbJlP7+CEr
GXss8vTPkiOoho7FlnovZqkmz7uPVMRp6R4QVlM/fLMSoNyBixgP9h2+7dKXikRVVDB0Q9EJNDk+uw
PtSxA+D5IsJKB6gp2aIG3+hXt/pdUkDm7KxmsZY6SKPiReEWp4yZfSIURQl4bAKAjfqtNODUF+j5Z9
sveuxBojOHY+iHfWUi/L7BU7O7bkGvnQt649iZuovW813Q4PartRdDykFeESlLvJcUV4lMUyeRLLRA
o32I1SqM3yRTfgbNymnxBYrfoBeWgD8XiUxTJ5EstGc2RdfG7FGPyDSmnI9RoBqyU4SSRURM6JtXF4
3Kpr+/A=="
} 6
加密报⽂⽰例
{
"agencyRemark": "",
"airportPriceQuestParam": {
"airportCode": "HND",
"airportName": "成⽥国际机场",
"channelId": 1909498346,
"endAddress": "东京希尔顿酒店",
"endDetailAddress": "东京希尔顿酒店",
"endLocation": "35.549441,139.779791",
"serviceCityId": 217,
"serviceCityName": "东京",
"serviceTime": "2024-06-20 09:00:00",
"startAddress": "成⽥国际机场",
"startCityId": 1,
"startCityName": 1,
"startDetailAddress": "成⽥国际机场",
"startLocation": "35.7110245884332,139.773156568408"
},
"payDescription": "已⽀付",
"flightAirportCode":"HND",
"flightAirportName":"成⽥国际机场",
"servicePassagerMobile":"18001288005",
"servicePassagerAreacode":"86",
"servicePassagerName":"⼩王",
"serviceCityId":217,
"priceChannel":35000,
"serviceStartAddressDetail":"东京希尔顿酒店",
"serviceStartPoint":"35.549441,139.779791",
"serviceDestAddress":"成⽥国际机场",
"serviceDestAddressDetail":"成⽥国际机场",
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
"serviceDestPoint":"35.7110245884332,139.773156568408",
"priceActual":35000,
"serviceStartAddress":"东京希尔顿酒店",
"serviceCarModel":1,
"serviceCarModelName":"经济5座",
"serviceTime":"2024-06-20 09:00:00",
"serviceCarModelName":"经济7座",
"orderChannel": "1909498346",
"orderChannelName": "⽆忧⾏",
"flightNo":"CA181"
}
31
32
33
34
35
36
37
38
39
40
41
响应参数说明:
{
"data": {
"createRsp": [
{
"orderNo": "Z4545116914",
"payDeadLine": "2024-06-19 00:49:30",
"payOrderCategory": 1,
"price": 35000,
"serviceUp": 1
}
],
"orderNos": [
"Z4545116914"
],
"payDeadLine": "2024-06-19 00:49:30",
"priceShouldPay": 35000
},
"status": 200,
"success": true
}
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
返回皇包⻋订单号
3.6.2送机下单（需要加密）
送机下单，使⽤公共参数请求接⼝，encryptBody为下单参数序列化后使⽤DES加密的结果
接⼝地址 /otaorder-api/common/v1.0/order/transfer?token=ACCESS_TOKEN
请求参数：
参数名称 说明 是否必须 类型
flightAirportCode 起⻜机场三字码 false string
flightAirportName 起⻜机场名称 false string
flightArriveTime 航班计划到达时间 false date-time
flightDestAirportName 降落机场名称 true string
flightDestBuilding 降落机场航站楼 false string
flightDestCode 降落机场三字码 true string
flightFlyTime 航班起⻜时间 false date-time
flightNo 航班编号 false string
orderNo 订单号 true string
orderThirdNo 第三⽅订单号 true string
priceActual 实际⽀付价格 单位 分 true int32
priceChannel 渠道价格（不包含增项费⽤）
单位 分
true int32
serviceCarModel ⻋型Id true string
serviceCarModelName ⻋型 true string
serviceCityId 服务城市id true int32
serviceCityName 服务城市名称 true string
serviceContinentId 服务⼤洲id false int32
serviceContinentName 服务⼤洲名称 false string
serviceCountryId 服务国家id false int32
serviceCountryName 服务国家名称 false string
serviceDestAddress 终⽌地 true string
serviceDestAddressDetail 终⽌地详细地址 true string
serviceDestPoi poi终点Id false string
serviceDestPoint 终⽌地经纬度 true string
serviceEndCityId 终⽌地 城市ID false int32
serviceEndCityName 终⽌地 城市名 false string
servicePassagerAreacode 乘⻋⼈⼿机区号 true string
servicePassagerMobile 乘⻋⼈⼿机号码 true string
servicePassagerName 乘⻋⼈名称 true string
serviceStartAddress 出发地 true string
serviceStartAddressDetail 出发地详细地址 true string
serviceStartPoi poi起点Id false string
serviceStartPoint 出发地经纬度 true string
serviceTime 服务时间 true date-time
userRemark ⽤⼾留⾔ false string
orderChannel 商⼾id true string
orderChannelName 商⼾名称 true string
airportPriceQuestParam 增项费⽤报价参数 body true
orderAdditionals 增项费⽤报价参数 body FALSE
orderAdditionals
additionalTypeName 增项费⽤类别名称 body FALSE string
priceActual 实际⽀付价格单位分 body FALSE integer(int32
)
priceChannel 渠道价格单位分 body FALSE integer(int32
)
additionalType 增项费⽤类别 body FALSE integer(int32
)
airportPriceQuestParam
airportCode 机场三字码 body TRUE string
airportName 机场名称 body TRUE string
channelId 渠道ID body TRUE integer(int64
)
endAddress ⽬的地地址 body TRUE string
endDetailAddress ⽬的地详细地址 body TRUE string
endLocation ⽬的地坐标点 body TRUE string
serviceCityId 服务城市ID body TRUE integer(int32
)
serviceCityName 服务城市名称 body TRUE string
serviceTime 服务时间 body TRUE string
startAddress 起始地址 body TRUE string
startCityId 起始城市ID body TRUE integer(int32
)
startCityName 起始城市name body TRUE string
startDetailAddress 起始详细地址 body TRUE string
startLocation 起始坐标点 body TRUE string
⼊参⽰例：
{
"cid":"1909498346",
"sign":"1234",
"timestamp":12345678,
"encryptBody":"B9idA1qppQDjEsY1NoVdQleH4S/m8/wlDBxO3NO2Go6BuLqnbkjg3T7J/hPmIb3
nCENzvwcXoqbwYICP5mDV1b2H4XRvB2b1uVzgBNcAi2z8eQjSt9wSq8JfEJguiIg00uvYufxN/56s4
G2EUlczDl9oMt2KQ8+RuvW49je4E0Rtjvqjd8tz7JB/5E4DLUKeBRWgKeHLtZORP7GKODBZGg2jU6G
/nJntfFK5AEME8+PrMnpdVu00sZE5FfXuUiBobdZ2gNhoOP4rfUUtU0vBjeh3uSdPa1l6haySoFTvK
OkVUk8PgSTDjicRFoomV1JLXgplx6ULYMxhjhF0Cw3U85Xf10xW4LDMlteiFQ1+gb/yRg2BCu3AYqi
9cvI42/K+dAPUPCnXEBliKHqml5ugF+pAT0wRaAOF04i11ZE6scG0hqIoB8EiuFqX8GFXofq56gh6j
O6r1cXcDlbJlP7+CPZLRRdWVQbfD1y8T6IlMTAOf2Ljiqu7f5W7BIiefE21pDtOFeeck2uVuwSInnx
Ntc693Rq9BX197Wn+GXUyCvw+B6rwb4P6rW2O+qN3y3Ps/Q+d+5S0+OfFe9bUIXykPIn5eOUyfuYR1
fmI6c6e2mwEiu2YSckCSv6UCkoJoZ8+CqWmW4Z6g54fv6B2L5q0/XaHmN7CJyazver0LNu9TyzZCIh
EqRwTS4xxb5gRM0q9L3zRQ0S0j1NmHZE1OgKxL09kUsOJghz2/Q+d+5S0+OfFe9bUIXykPFbsD8Xr8
1
2
3
4
5
JAuTCCB4yLDWk0/7EG/rppWLxnR5YbJMX/QGWKfk14N1wUgeULTG5qbaO0pgtkBE1xBhTHKmY1QuGb
muFiXdC9q3diLP3CKdpVP4Ni4WAgBunS3jRtCUXKZt9rG2UZOwiwQ6kBPTBFoA4W43pzZDS2jZ5agV
60A7ODudGVJ2vQmLKZk1MbDIGqxvpXf10xW4LDMIG0AJabQJEcORA1mMQdezl3ewJi2o6vws+s4xmC
8jFl2n5kPzdlWBjNwgPZCvf3eyEeo9TNroyA3yIrtf3t7fJe0GGBlrxe/1icE0xr5edTxh2BqSWf1j
upAT0wRaAOFHMd8PGylJpk4rskPuBU2S1mL8++rHRzVvZDmqz0WpqIdIQ90TsIf3zTS+oveF0S4DkQ
NZjEHXs7F+vtG+68JFFmL8++rHRzVvZDmqz0WpqIdIQ90TsIf3zhgxNqo+x2N8duq/AqUvRtQqDPRG
FuZ8iMUwLfGazjt2bhL510F3BsKxrHUYUTn9PaOZKoy+TCTXvnWm/kclvvNG024ucBPM8hHqPUza6M
g3A5WyZT+/ghKxl7LPL0z5IjqIaOxZZ6L2apJs+7j1TF7unl/iRcprw/QrNbezYjiBzjn5+M7ap2BE
Kq6gcd5o52GAgTwMcTjK+5rGpmpggxxdEgPMW6ndkd1OBf0nTp0sO/lLptZMqO0tJ1as1T5kLc2f4w
H1bSOT4C5oZ5MeFssxKg3IGLGA7kcTp5KtbREE6GyWhi1A2DQhtx3Lx7Ny0fBY4IiXB68AMIkVeGG8
nKHe/QticyhvrlOttbsuPzzkAr6xI5u6qXzItnfH5xCdd7arZGrsY1ZcYukaUJ8dwg="
}
6
7
加密报⽂⽰例：
{
"airportPriceQuestParam": {
"airportCode": "HND",
"airportName": "成⽥国际机场",
"channelId": 1909498346,
"endAddress": "东京希尔顿酒店",
"endDetailAddress": "东京希尔顿酒店",
"endLocation": "35.549441,139.779791",
"requestSystem": 0,
"serviceCityId": 217,
"serviceCityName": "东京",
"serviceTime": "2024-06-20 09:00:00",
"startAddress": "成⽥国际机场",
"startCityId": 1,
"startCityName": 1,
"startDetailAddress": "成⽥国际机场",
"startLocation": "35.7110245884332,139.773156568408"
},
"flightAirportCode":"HND",
"flightAirportName":"成⽥国际机场",
"servicePassagerMobile":"18001288005",
"servicePassagerAreacode":"86",
"servicePassagerName":"⼩王",
"serviceCityId":217,
"priceChannel":35000,
"serviceStartAddressDetail":"东京希尔顿酒店",
"serviceStartPoint":"35.549441,139.779791",
"serviceDestAddress":"成⽥国际机场",
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
"serviceDestAddressDetail":"成⽥国际机场",
"serviceDestPoint":"35.7110245884332,139.773156568408",
"priceActual":35000,
"serviceStartAddress":"东京希尔顿酒店",
"serviceCarModel":1,
"serviceCarModelName":"经济5座",
"serviceTime":"2024-06-20 09:00:00",
"serviceCarModelName":"经济7座",
"orderChannel": "1909498346",
"orderChannelName": "商⼾名称"
}
29
30
31
32
33
34
35
36
37
38
39
响应参数说明:
{
"data": {
"createRsp": [
{
"orderNo": "Z5615582130",
"payDeadLine": "2024-06-18 15:31:35",
"payOrderCategory": 1,
"price": 35000,
"serviceUp": 1
}
],
"orderNos": [
"Z5615582130"
],
"payDeadLine": "2024-06-18 15:31:35",
"priceShouldPay": 35000
},
"status": 200,
"success": true
}
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
3.6.3订单列表
接⼝地址 /otaorder-api/common/v1.0/order/orderList?token=ACCESS_TOKEN
请求参数：
参数名称 说明 是否必须 类型
limit 每⻚记录数 true int32
listType 列表类型：1-全部；2-待
⽀付；3-服务中；4已取
消
true int32
offset 偏移量 true int32
userId ⽤⼾ID true string
响应参数说明:
参数名称 说明 类型 schema
list 列表数据 list 订单列表返回体
size 列表记录数 int32
total 总记录数 int32
schema属性说明
订单列表返回体
同3.6.4
3.6.4订单详情
接⼝地址 /otaorder-api/common/v1.0/order/orderDetail?token=ACCESS_TOKEN
请求⽅式
请求参数：
参数名称 说明 是否必须 类型 备注
orderTravelNo 订单号 false string 与orderNoThird其中⼀个
不能为空
orderNoThird 第三⽅订单号 false string 与orderTravelNo其中⼀
个不能为空
响应参数说明:
OrderTravelConfirm对象
OtaProviderInfo对象
OtaStudioCarInfoRsp对象
评论对象
参数名称 说明 类型
commentContent 评价内容 string
commentGrade 星级 int32
commentPics 评价图⽚,json数组 string
commentStatus 评价状态：0-未⼈⼯⼲预; 1-⼈⼯⼲
预未通过; 2-⼈⼯⼲预通过
int32
commentType 评价类型1=当地⼈，2=商品 int32
fromUid 评价⼈ID string
fromUname 评价⼈名称 string
goodsNo 商品号 string
orderNo 订单号 string
providerId 当地⼈ID string
包⻋⾏程结构
参数名称 说明 类型 schema
journeySingles ⾏程列表 array 包⻋单⽇⾏程结构
passCityNames 途径城市名称 逗号分隔 string
passCitys 途径城市 array 途径城市结构
totalDay ⾏程天数 int32
包⻋单⽇⾏程结构
参数名称 说明 类型 schema
endAddress 下⻋地点 地址信息 地址信息
endCityInfo 结束城市信息 途径城市结构 途径城市结构
index 第N天 int32
passAddressInfo 途径地点 array 地址信息
serviceMaxDistance 可服务距离(公⾥数) int32
serviceMaxTime 可服务时间(⼩时数) int32
serviceTime 服务时间 date-time
startAddress 上⻋地点 地址信息 地址信息
startCityInfo 开始城市信息 途径城市结构 途径城市结构
地址信息
参数名称 说明 类型
address 地址 string
addressDetail 地址详情 string
location 坐标 string
poiId POIID string
途径城市结构
参数名称 说明 类型
cityId 城市ID int32
cityName 城市名称 string
JourneyListVoRsp
参数名称 说明 类型
cityId Poi城市id int32
location 坐标 string
outTownPriceFlag 是否产⽣追加费⽤ int32
payNo ⽀付单号 允许重复 string
poiAdress Poi详细地址 string
poiId poiId,完善⾏程必填 string
poiName Poi名称 string
poiType poi类型// 酒店 美景 餐厅 int32
priceMark 报价ID string
type poi属性 周边/跨城 int32
报价费⽤说明返回结果
参数名称 说明 类型
priceDescDetail 费⽤说明详情 array
priceDescFee 费⽤说明价格(单位:元) int32
priceDescFeeName 费⽤说明名称 string
priceDescType 费⽤说明类型 int32
priceDescTypeName 费⽤说明类型 string
priceDescUnit 费⽤说明单位 string
请求⽰例
{
"cid":"1909498346",
1
2
"sign":"1234",
"timestamp":12345678,
"orderTravelNo":"Z4549916911"
}
3
4
5
6
返回⽰例
{
"data": {
"amountShouldPay": 196000,
"carServiceStartDate": "2024-06-29 00:00:00",
"confirmDetailList": [
{
"amountCouponSelf": 0,
"amountRealShouldReceive": 196000,
"amountShouldReceive": 196000,
"amountShouldReceiveBase": 196000,
"confirmAdditionalRsp": [],
"orderConfirmNo": "R44596625516999",
"priceItemId": "11fb46f2-32d1-41fe-98b4-
2bfd93c920671719554430792",
"productType": 4,
"productTypeName": "包⻋",
"totalDay": 1
}
],
"countryId": 60,
"countryName": "⽇本",
"customerAreaCode": "86",
"customerMobile": "15230152733",
"customerName": "兔⼦",
"customerWechat": "",
"endCityId": 217,
"endCityName": "东京",
"orderChannel": "19101021",
"orderChannelName": "测试海⻢⾮⻢",
"orderTravelNo": "Z44596625516962",
"orderTravelStatus": 20,
"payDeadline": "2024-07-02 17:36:15",
"payStatus": 1,
"payStatusName": "已全额⽀付",
"payTime": "2024-07-01 17:36:16",
"providerList": [
{
"age": 37,
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
"areaCode": "64",
"genderName": "男",
"mobile": "210****234",
"photo": "https://fr-au-new.oss-ap-southeast-
2.aliyuncs.com/provider/84592316911/profile/2024-01-13/EMStO_2Y2A",
"providerId": "84592316911",
"providerName": "郭尚函",
"providerNo": "F91670",
"studioCarInfoRsp": {
"carBrandName": "丰⽥",
"carColor": "⽩",
"carGuestNum": 4,
"carLicenceNo": "GQGO",
"carLuggageNum": 2,
"carModel": "舒适5座",
"carModelId": 6,
"carName": "RAV4",
"carSeatNum": 5
}
}
],
"sopRecordRspList": [
{
"completeResult": 0,
"createTime": "2024-07-01 17:36:40",
"goodsType": 40,
"orderDaliyDayId": "187905",
"orderNo": "R44596625516999",
"orderTravelDailyNo": "D28654227671592",
"orderType": 4,
"preCompleteTime": "2024-06-29 09:00:00",
"preCompleteTimeServer": "2024-06-29 08:00:00",
"purchaseNo": "CP45976680148736",
"recordId": 476163,
"recordStatus": 0,
"serviceStandard": "请您在接单后 24 ⼩时内⾸次联系客⼈，在出⾏前
24 ⼩时内联系客⼈确认⾏程。",
"serviceTime": "2024-06-29 09:00:00",
"serviceTimeEnd": "2024-06-29 23:59:59",
"serviceTimeServer": "2024-06-29 08:00:00",
"settleRatio": 0,
"sopId": 630,
"sopType": 0,
"sopTypeName": "⾏前联系",
"sopVersion": 1,
"tripDay": 1,
"updateTime": "2024-07-01 17:37:00"
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
},
{
"completeResult": 0,
"createTime": "2024-07-01 17:36:40",
"goodsType": 40,
"orderDaliyDayId": "187905",
"orderNo": "R44596625516999",
"orderTravelDailyNo": "D28654227671592",
"orderType": 4,
"preCompleteTime": "2024-06-29 09:00:00",
"preCompleteTimeServer": "2024-06-29 08:00:00",
"purchaseNo": "CP45976680148736",
"recordId": 476164,
"recordStatus": 0,
"serviceStandard": "",
"serviceTime": "2024-06-29 09:00:00",
"serviceTimeEnd": "2024-06-29 23:59:59",
"serviceTimeServer": "2024-06-29 08:00:00",
"settleRatio": 0,
"sopId": 1,
"sopType": 1,
"sopTypeName": "准时出发",
"sopVersion": 1,
"tripDay": 1,
"updateTime": "2024-07-01 17:37:00"
},
{
"completeResult": 0,
"createTime": "2024-07-01 17:36:40",
"goodsType": 40,
"orderDaliyDayId": "187905",
"orderNo": "R44596625516999",
"orderTravelDailyNo": "D28654227671592",
"orderType": 4,
"preCompleteTime": "2024-06-29 09:00:00",
"preCompleteTimeServer": "2024-06-29 08:00:00",
"purchaseNo": "CP45976680148736",
"recordId": 476165,
"recordStatus": 0,
"serviceStandard": "",
"serviceTime": "2024-06-29 09:00:00",
"serviceTimeEnd": "2024-06-29 23:59:59",
"serviceTimeServer": "2024-06-29 08:00:00",
"settleRatio": 0,
"sopId": 2,
"sopType": 2,
"sopTypeName": "准时到达",
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
"sopVersion": 1,
"tripDay": 1,
"updateTime": "2024-07-01 17:37:00"
},
{
"completeResult": 0,
"createTime": "2024-07-01 17:36:40",
"goodsType": 40,
"orderDaliyDayId": "187905",
"orderNo": "R44596625516999",
"orderTravelDailyNo": "D28654227671592",
"orderType": 4,
"preCompleteTime": "2024-06-29 09:00:00",
"preCompleteTimeServer": "2024-06-29 08:00:00",
"purchaseNo": "CP45976680148736",
"recordId": 476166,
"recordStatus": 0,
"serviceStandard": "",
"serviceTime": "2024-06-29 09:00:00",
"serviceTimeEnd": "2024-06-29 23:59:59",
"serviceTimeServer": "2024-06-29 08:00:00",
"settleRatio": 0,
"sopId": 3,
"sopType": 3,
"sopTypeName": "准时接到客⼈",
"sopVersion": 1,
"tripDay": 1,
"updateTime": "2024-07-01 17:37:00"
},
{
"completeResult": 0,
"createTime": "2024-07-01 17:36:40",
"goodsType": 40,
"orderDaliyDayId": "187905",
"orderNo": "R44596625516999",
"orderTravelDailyNo": "D28654227671592",
"orderType": 4,
"preCompleteTime": "2024-06-29 09:00:00",
"preCompleteTimeServer": "2024-06-29 08:00:00",
"purchaseNo": "CP45976680148736",
"recordId": 476167,
"recordStatus": 0,
"serviceStandard": "",
"serviceTime": "2024-06-29 09:00:00",
"serviceTimeEnd": "2024-06-29 23:59:59",
"serviceTimeServer": "2024-06-29 08:00:00",
"settleRatio": 0,
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
"sopId": 4,
"sopType": 4,
"sopTypeName": "服务完成",
"sopVersion": 1,
"tripDay": 1,
"updateTime": "2024-07-01 17:37:00"
},
{
"completeResult": 0,
"createTime": "2024-07-01 17:36:40",
"goodsType": 40,
"orderDaliyDayId": "187905",
"orderNo": "R44596625516999",
"orderTravelDailyNo": "D28654227671592",
"orderType": 4,
"preCompleteTime": "2024-07-02 23:59:59",
"preCompleteTimeServer": "2024-07-02 22:59:59",
"purchaseNo": "CP45976680148736",
"recordId": 476168,
"recordStatus": 0,
"serviceStandard": "",
"serviceTime": "2024-06-29 09:00:00",
"serviceTimeEnd": "2024-06-29 23:59:59",
"serviceTimeServer": "2024-06-29 08:00:00",
"settleRatio": 0,
"sopId": 5,
"sopType": 5,
"sopTypeName": "完成增值服务",
"sopVersion": 1,
"tripDay": 1,
"updateTime": "2024-07-01 17:37:00"
},
{
"completeResult": 0,
"createTime": "2024-07-01 17:36:40",
"goodsType": 40,
"orderDaliyDayId": "187905",
"orderNo": "R44596625516999",
"orderTravelDailyNo": "D28654227671592",
"orderType": 4,
"preCompleteTime": "2024-07-02 23:59:59",
"preCompleteTimeServer": "2024-07-02 22:59:59",
"purchaseNo": "CP45976680148736",
"recordId": 476169,
"recordStatus": 0,
"serviceStandard": "",
"serviceTime": "2024-06-29 09:00:00",
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
"serviceTimeEnd": "2024-06-29 23:59:59",
"serviceTimeServer": "2024-06-29 08:00:00",
"settleRatio": 0,
"sopId": 6,
"sopType": 6,
"sopTypeName": "客⼈评价",
"sopVersion": 1,
"tripDay": 1,
"updateTime": "2024-07-01 17:37:00"
}
],
"startCityId": 217,
"startCityName": "东京",
"totalDay": 2,
"travelEndTimeBeijing": "2024-06-30 05:34:00",
"travelEndTimeLocal": "2024-06-30 06:34:00",
"travelStartTimeBeijing": "2024-06-29 08:00:00",
"travelStartTimeLocal": "2024-06-29 09:00:00",
"userId": "10961769847",
"userName": "兔⼦"
},
"status": 200,
"success": true,
"traceId": "[Ignored Trace]"
}
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
3.6.5 包⻋下单（需要加密）
包⻋下单，使⽤公共参数请求接⼝，encryptBody为下单参数序列化后使⽤DES加密的结果
接⼝地址 /otaorder-api/common/v1.0/order/car?token=ACCESS_TOKEN
请求参数：
参数名称 说明 是否必须 类型
orderNoThird 第三⽅订单号 true string
travelStartTimeLocal ⾏程开始时间(当地) true datetime
travelEndTimeLocal ⾏程束时间(当地) true datetime
travelStartTimeBeijing ⾏程开始时间(北京) true datetime
travelEndTimeBeijing ⾏程结束时间(北京) true datetime
totalDay 本次⾏程的总天数 true int32
startCityId 出发城市id true int32
startCityName 出发城市名称 false string
endCityId 结束城市id true int32
endCityName 结束城市名称 false string
amountChannelSys 渠道上的系统建议卖价
分
true int32
amountShouldPay 总应付⾦额 分 true int32
amountShouldReceive 总应收⾦额 分 true int32
amountActualPay 总实付⾦额 分 true int32
amountActualReceive 总实收⾦额 分 true int32
amountCouponTotal 总渠道优惠⾦额 分 true int32
customerName 主联系⼈姓名 true string
customerAreaCode 主联系⼈⼿机区号 true string
customerMobile 主联系⼈⼿机号 true string
customerWechat 主联系⼈微信号 false string
customerAreaCodeBackup 主联系⼈备⽤⼿机区号 false string
customerMobileBackup 主联系⼈备⽤⼿机号 false string
creatorId 下单⼈id false string
creatorName 下单⼈名称 false string
userId 订单归属⽤⼾id false string
userName 订单归属⽤⼾名称 false string
payTime ⽀付时间 true datetime
priceConfirmTwice 是否⼆次确认 false int32
thirdPayNo 第三⽅⽀付单号 false string
payDescription ⽀付说明 false string
nightOrder false int32
是否夜间订单 1 是 0 不
是
confirmTwiceType ⼆次确认类型 1 时间⼆
次确认 2 库存⼆次确认
false int32
priceMarkCity 报价城市ID true int32
travelPriceItemId 关联的报价项ID false string
remark 订单备注 最多只能输⼊
500个字符
false string
travelDailyBaseReqList 每天⾏程 true ValetOrderTravelDailyBaseRe
q[]
additionalBaseReqList 增项 false ValetOrderTravelDailyAdditio
nalBaseReq[]
ValetOrderTravelDailyBaseReq对象
参数名称 说明 是否必须 类型
dayIndex 第⼏天，从1开始 true int32
goodsType 商品细分类型 40=10⼩
时100公⾥,41=10⼩时
300,42=10⼩时600公⾥
true int32
goodsTypeName 商品细分类型名称 true string
serviceStartTimeLocal 服务开始时间(当地)-当
天
true datetime
serviceEndTimeLocal 服务结束时间(当地)-当
天
true datetime
serviceStartTimeBeijing 服务开始时间(北京)-当
天
true datetime
serviceEndTimeBeijing 服务结束时间(北京)-当
天
true datetime
amountChannelSys 渠道上的系统建议卖价
分
true int32
amountShouldPay 总应付⾦额-含优惠 分 true int32
amountShouldReceive 总应收⾦额-含平台优惠
分
true int32
amountActualPay 总实付⾦额 分 true int32
amountActualReceive 总实收⾦额 分 true int32
amountCouponTotal 总渠道优惠⾦额(包含了
渠道和平台的) 分
true int32
amountCouponSelf 总优惠⾦额（平台⾃⼰
的） 分
true int32
carModelId ⻋型id true int32
carModelName ⻋型名称,⽐如舒适5座 true string
startContinentId 出发⼤洲id true int32
startContinentName 出发⼤洲名称 true string
startCountryId 出发国家id true int32
startCountryName 出发国家名称 true string
startCityId 出发城市 true int32
startCityName 出发城市中⽂名称 true string
startCityNameEn 出发城市英⽂名称 false string
startPoiType 出发poi的类型 false int32
startPoiId 出发poi false string
startPoiName 出发poi名称 false string
startPoiNameEn 出发poi名称英⽂ false string
startPoiLocation 出发poi经纬度 false string
startAddressDetail 出发地详细地址 false string
endCountryId 结束国家id true int32
endCountryName 结束国家名称 true string
endCityId 结束城市 true int32
endCityName 结束城市中⽂名称 true string
endCityNameEn 结束城市英⽂名称 false string
endPoiType 结束poi的类型 false int32
endPoiId 结束poi false string
endPoiName 结束poi名称 false string
endPoiNameEn 结束poi名称英⽂ false string
endPoiLocation 结束poi的经纬度 false string
endAddressDetail 结束地址 false string
serviceMaxTime 服务最⼤时间（⼩时） true int32
serviceMaxDistance 服务最⼤距离 true int32
travelAdditionalDailyBaseReq 订单增项 false ValetOrderTravelDailyAdditio
nalBaseReq[]
travelStationsBaseReq 途径点 false ValetOrderTravelStationsBase
Req[]
ValetOrderTravelDailyAdditionalBaseReq对象
参数名称 说明 是否必须 类型
additionalType 增项费⽤类别 true int32
additionalTypeName 增项费⽤类别名称 true string
additionalTypeDesc 增项费⽤描述 false string
priceActual 实际⽀付价格 分 true int32
ValetOrderTravelStationsBaseReq对象
参数名称 说明 是否必须 类型
passCityId 途径城市id true int32
passCityName 途径城市中⽂名称 true string
passCityNameEn 途径城市英⽂名称 false string
passPoiType 途径poi的类型 true int32
passPoiId 途径poi true string
passPoiName 途径poi名称 true string
passPoiNameEn 途径poi名称英⽂ false string
longitude 经度 true string
latitude 纬度 true string
passPoiTypeName 途径poi的类型 true string
passAddress 途径地址 false string
passAddressDetail 途径地址详情 false string
⼊参⽰例：
{
"cid": "1909498346",
"sign": "1234",
"timestamp": 12345678,
"encryptBody":
"Q/wD3680/qgwcZqttZ9Wv5IEOfcjwDBOtkBmMVv/E0sF2DpUatu6KeQiOQRovVDZ+S0KJlpaAABAu
DL1+9NUN65CScMPr6Csc0qml+nDNVrVrHYBd6RwP+fuGeWK32/AslP7HH9TMKjpQXRimd1gMSTQLzW
v4Vgw1cdemfxbQghbNYEFvhYkox7FBlXyQx8hRAXXVslXz928eBm+UKYYQ3AgtrSVoQNXjZ6ugSGJ6
eih7SPp7ocf2ze+2UdUqUzEFAJ0lu7GAz6/0lCAhuNdJwZG1xFM0raBI3VLqszcTrJlXG3xNL/3FSN
dwEKyF7Iogx+5V0DIWEk8iMAOviRfQySWD/JaUhTfUgkQ23gsVC+agIN2lEyv1G5dxXmHrSq73sO/9
wuvIoSjjZ91hBS8k3fBedaOqJO4pzFceWp9A7owTWAdxRc55GO6RoiDRj+yZvc+z0DweIiFinDnz9E
/Volb7pKR6jWAKP0kmLKLe4iKaVZavEhPkVGXzSykWoW+FgM7dWUOAJORxFA4w1JVM0c2Oh3WeEI1N
tX7dCJmO5HzW8eEFbHdz6ugqi52E7d8Yjshm4JaVy2j3+CoS6DmAKugqi52E7d8Wc54f+WEUXj0Il6
aEiDkMsQoHgRcrkK8QA+bzUHWm7/ummQ+/u3ap0yiXRMscjeQ7PwLXk3OaxoOKUxEPFUtITdN+FEhK
M6iFvPRyDRP/JiUGlDT3fr8DcraKY/hDRZOcIvmnk5shA2HmQ7x8u1iM/bletjT8Z0EMC2w7R2CSU5
K0HCi1G/euQEAShmqwJLogqy3IfT8TDGD5IsJKB6gp61GZn+9cPO1IrU32suYxOFn2nfZTljPqbx9f
yPOHaFdL6mHVYxueE7I/LAcB2QyDzlYgDHUCn/0xAbOi+g4kRFi2CGq5lFd+G2K3+COU/1H+bHlKsi
qLtWVuwSInnxNtcYk+zZhq/INhu08+4rzGgq0+12ZDBmuX6HtI+nuhx/bN77ZR1SpTMQ1+cUyxLeM1
ORJ3rtoTZsgnwiCWX6jgzqV39dMVuCwzFPGDnXpFgBl9pD9hO6jkbWFra+Iaxg8EoH6i5oltbwB+Qx
CcAOfsO6BEKq6gcd5o8Vr+oIBRtP3I2ZzOEdU4QmVuwSInnxNtZgIxfQaWHwxLzoRiSHz5frS2SyLu
+hpgHLGW/3I5QPtUJcQDUlel1wv0oSOQ3rwNDX9MRpwyEsYZ+9VYUlo9cPoBWF3ljZcPtA33wPClNU
ooQqgldIhI4rasMyl5C2yVURa54BNOwZ9jcHN5rfh8qQBpVkZbsd+CacBFcvTKHQL28GXQAZPtsIhH
gufjCyEwMYk+zZhq/INhu08+4rzGgq0+12ZDBmuX3Jqi37ohjeF1A/7iuGnYunPjPpvU4JDZSsqT/M
mP5HhL3TR8h00zQb7OyvcuSEwIEElwMu68SaGN5dYvQgtFIRJXYhxZHWGnPRkt3C0H7nftsv+x6N2o
2Liq+vKgkG56PhgPLupA2PJqCNofKSLrUP5KfLfvij6kWPJ7XAZvveLiEP1HvlKQwgN6JIy2KlZiJM
nAxiSCikdbkwiGjij1+UCqcAS//OgDd0ok2zswHJORVKDN80LnRtMol0TLHI3kPCGk5kXI5kB1zPRq
x08eMdbucHctz9wuvdf8qL/WjFYZSpe8Pa3c+vxrvykmbD+K52b1yGWOGzzgRCquoHHeaPvsFSdeax
io2O6RoiDRj+yUK+WHU/HIt08v9FveGIUf4lb7pKR6jWAQqUv2SQibKAgn3GnD8pi9ewVUlvuaZ6s0
stejI0ikx71J7Pz+lpHaFIJENt4LFQvl5VUKIyFNOfx7VEN7D7OUyjj+TtZ+fp1E5KZytfTvQKCy8y
1
2
3
4
5
hq37RAFpUxyBUgQWZ3aFzlowjVXKm90rILEvfwU7q047etKX4pdcBEn5mv51QXcQRi2hmmhOSmcrX0
70CgsvMoat+0QA9JScQBnuU4d2hc5aMI1VyTOGLvA03iY7XACbVtkSczVUW6xAJv4F91/nsrArcua8
eS8HZw5HNcodQhfu5yL7Rm7ThQxNzFIw21ft0ImY7kYuSFz/IWESbuCQS8WZXm635UsdruLkh59vGI
tr+VewSGkojKltRB3h9/qSC1I07QRVjpqf3XNxuixwHMCLCB4B1lqxDnyp70Tfl+gw9Wjdqwd5czen
At69AapojUEBVRpU2LKDuW2RtMiRVweEiYIbGN126Lnah/cxFbqtqpkyHLaK8gCapvzxRxHSKePG6g
4IKZZ3YivqOQaDS+A3kiquV3C0N7C63lbwuYCLfyJ/Xc57fADXRBlJSCRDbeCxULzaYTeJO46ZczJ6
fmcGh781uENBK1olwjpNXFM2LnU3Q0FBv2cqZ9dHkW9xaJiMagvsfZcyddsv8dEy/pQpqWwGW3iOTI
xyj/Bn9H22DJp7kw5NXa0pTQez/ofAHbTMh6boI9tTWvXMi6Uihi2jmznMy1xe+uHx0KufHTFqi37g
/zr3dGr0FfX32GC8g4X/2o/CGk5kXI5kBR7cBmNSWf/mCCmWd2Ir6jgtXdUp3lxAgUduywF6Ix5F+4
6UszslAqYTdUz3TIA//Y0NF7YIqyL5DSYVlFSx2N/h+S3nUEchFxrI3PCPr76oqt7B4IlK0zcHrVGB
PsrU5ByBGenya1b/eJ9GymSI8tkGg0vgN5Iqrc/9Ubgy3/aKPohyLRTxB/tj+dtzN62lfxj537FJhB
0g="
} 6
加密报⽂⽰例：
{
"amountActualPay": 0,
"amountActualReceive": 0,
"amountChannelSys": 196000,
"amountCouponTotal": 0,
"amountShouldPay": 196000,
"amountShouldReceive": 196000,
"customerAreaCode": "86",
"customerAreaCodeBackup": "86",
"customerMobile": "15230152733",
"customerMobileBackup": "",
"customerName": "兔⼦",
"customerWechat": "",
"endCityId": 217,
"endCityName": "东京",
"orderNoThird": "3333333",
"payTime": 1719556012464,
"priceConfirmTwice": 1,
"priceMarkCity": 217,
"startCityId": 217,
"startCityName": "东京",
"thirdPayNo": "3333333",
"totalDay": 2,
"travelDailyBaseReqList":
[
{
"amountActualPay": 0,
"amountActualReceive": 0,
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
"amountChannelSys": 126900,
"amountCouponSelf": 0,
"amountCouponTotal": 0,
"amountShouldPay": 126900,
"amountShouldReceive": 126900,
"dayIndex": 1,
"goodsType": 40,
"goodsTypeName": "市内包⻋",
"carModelId": 6,
"carModelName": "舒适5座",
"endAddressDetail": "1 Chome-1-2 Oshiage, Sumida, Tokyo 131-0045,
Japan",
"endCityId": 217,
"endCityName": "东京",
"endCityNameEn": "Tokyo",
"endCountryId": 60,
"endCountryName": "⽇本",
"endPoiId": "v228677",
"endPoiLocation": "35.7100627,139.8107004",
"endPoiName": "东京晴空塔",
"endPoiNameEn": "Tokyo Sky Tree",
"endPoiType": 3,
"serviceMaxDistance": 100,
"serviceMaxTime": 10,
"startAddressDetail": "Japan, 台场 （Tokyo）",
"startCityId": 217,
"startCityName": "东京",
"startCityNameEn": "Tokyo",
"startContinentId": 6,
"startContinentName": "亚洲",
"startCountryId": 60,
"startCountryName": "⽇本",
"startPoiId": "v227934",
"startPoiLocation": "35.6259224,139.7714143",
"startPoiName": "御台场",
"startPoiNameEn": "Odaiba",
"startPoiType": 3,
"serviceEndTimeBeijing": 1719673199000,
"serviceEndTimeLocal": 1719676799000,
"serviceStartTimeBeijing": 1719619200000,
"serviceStartTimeLocal": 1719622800000,
"travelAdditionalDailyBaseReq":
[],
"travelStationsBaseReq":
[]
}
],
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
"travelEndTimeBeijing": 1719696840000,
"travelEndTimeLocal": 1719700440000,
"travelPriceItemId": "11fb46f2-32d1-41fe-98b4-2bfd93c920671719554430792",
"travelStartTimeBeijing": 1719619200000,
"travelStartTimeLocal": 1719622800000,
"userId": "10961769847",
"userName": "兔⼦"
}
75
76
77
78
79
80
81
82
响应参数说明:
{
"data": {
"orderNos": [
"Z5615582130"
]
},
"status": 200,
"success": true
}
1
2
3
4
5
6
7
8
9
3.6.6 包⻋下单未⽀付（需要加密）
包⻋下单，使⽤公共参数请求接⼝，encryptBody为下单参数序列化后使⽤DES加密的结果
接⼝地址 /otaorder-api/common/noPay/v1.0/order/car?token=ACCESS_TOKEN
请求参数：
参数名称 说明 是否必须 类型
orderNoThird 第三⽅订单号 true string
travelStartTimeLocal ⾏程开始时间(当地) true datetime
travelEndTimeLocal ⾏程束时间(当地) true datetime
travelStartTimeBeijing ⾏程开始时间(北京) true datetime
travelEndTimeBeijing ⾏程结束时间(北京) true datetime
totalDay 本次⾏程的总天数 true int32
startCityId 出发城市id true int32
startCityName 出发城市名称 false string
endCityId 结束城市id true int32
endCityName 结束城市名称 false string
amountChannelSys 渠道上的系统建议卖价
分
true int32
amountShouldPay 总应付⾦额 分 true int32
amountShouldReceive 总应收⾦额 分 true int32
amountActualPay 总实付⾦额 分 true int32
amountActualReceive 总实收⾦额 分 true int32
amountCouponTotal 总渠道优惠⾦额 分 true int32
customerName 主联系⼈姓名 true string
customerAreaCode 主联系⼈⼿机区号 true string
customerMobile 主联系⼈⼿机号 true string
customerWechat 主联系⼈微信号 false string
customerAreaCodeBackup 主联系⼈备⽤⼿机区号 false string
customerMobileBackup 主联系⼈备⽤⼿机号 false string
creatorId 下单⼈id false string
creatorName 下单⼈名称 false string
userId 订单归属⽤⼾id false string
userName 订单归属⽤⼾名称 false string
priceConfirmTwice 是否⼆次确认 false int32
nightOrder 是否夜间订单 1 是 0 不
是
false int32
confirmTwiceType ⼆次确认类型 1 时间⼆
次确认 2 库存⼆次确认
false int32
priceMarkCity 报价城市ID true int32
travelPriceItemId 关联的报价项ID true string
remark 订单备注 最多只能输⼊
500个字符
false string
travelDailyBaseReqList 每天⾏程 true ValetOrderTravelDailyBaseRe
q[]
additionalBaseReqList 增项 false ValetOrderTravelDailyAdditio
nalBaseReq[]
ValetOrderTravelDailyBaseReq对象
参数名称 说明 是否必须 类型
dayIndex 第⼏天，从1开始 true int32
goodsType 商品细分类型 40=市内
包⻋,41=周边包⻋,42=
跨城包⻋
true int32
goodsTypeName 商品细分类型名称 true string
serviceStartTimeLocal 服务开始时间(当地)-当
天
true datetime
serviceEndTimeLocal 服务结束时间(当地)-当
天
true datetime
serviceStartTimeBeijing 服务开始时间(北京)-当
天
true datetime
serviceEndTimeBeijing 服务结束时间(北京)-当
天
true datetime
amountChannelSys 渠道上的系统建议卖价
分
true int32
amountShouldPay 总应付⾦额-含优惠 分 true int32
amountShouldReceive 总应收⾦额-含平台优惠
分
true int32
amountActualPay 总实付⾦额 分 true int32
amountActualReceive 总实收⾦额 分 true int32
amountCouponTotal true int32
总渠道优惠⾦额(包含了
渠道和平台的) 分
amountCouponSelf 总优惠⾦额（平台⾃⼰
的） 分
true int32
carModelId ⻋型id true int32
carModelName ⻋型名称,⽐如舒适5座 true string
startContinentId 出发⼤洲id true int32
startContinentName 出发⼤洲名称 true string
startCountryId 出发国家id true int32
startCountryName 出发国家名称 true string
startCityId 出发城市 true int32
startCityName 出发城市中⽂名称 true string
startCityNameEn 出发城市英⽂名称 false string
startPoiType 出发poi的类型 true int32
startPoiId 出发poi true string
startPoiName 出发poi名称 true string
startPoiNameEn 出发poi名称英⽂ false string
startPoiLocation 出发poi经纬度 false string
startAddressDetail 出发地详细地址 false string
endCountryId 结束国家id true int32
endCountryName 结束国家名称 true string
endCityId 结束城市 true int32
endCityName 结束城市中⽂名称 true string
endCityNameEn 结束城市英⽂名称 false string
endPoiType 结束poi的类型 true int32
endPoiId 结束poi true string
endPoiName 结束poi名称 true string
endPoiNameEn 结束poi名称英⽂ false string
endPoiLocation 结束poi的经纬度 false string
endAddressDetail 结束地址 false string
serviceMaxTime 服务最⼤时间（⼩时） true int32
serviceMaxDistance 服务最⼤距离 true int32
travelAdditionalDailyBaseReq 订单增项 false ValetOrderTravelDailyAdditio
nalBaseReq[]
travelStationsBaseReq 途径点 false ValetOrderTravelStationsBase
Req[]
ValetOrderTravelDailyAdditionalBaseReq对象
参数名称 说明 是否必须 类型
additionalType 增项费⽤类别 true int32
additionalTypeName 增项费⽤类别名称 true string
additionalTypeDesc 增项费⽤描述 false string
priceActual 实际⽀付价格 分 true int32
ValetOrderTravelStationsBaseReq对象
参数名称 说明 是否必须 类型
passCityId 途径城市id true int32
passCityName 途径城市中⽂名称 true string
passCityNameEn 途径城市英⽂名称 false string
passPoiType 途径poi的类型 true int32
passPoiId 途径poi true string
passPoiName 途径poi名称 true string
passPoiNameEn 途径poi名称英⽂ false string
longitude 经度 true string
latitude 纬度 true string
passPoiTypeName 途径poi的类型 true string
passAddress 途径地址 false string
passAddressDetail 途径地址详情 false string
⼊参⽰例：
{
"cid": "1909498346",
"sign": "1234",
"timestamp": 12345678,
"encryptBody":
"Q/wD3680/qgwcZqttZ9Wv5IEOfcjwDBOtkBmMVv/E0sF2DpUatu6KeQiOQRovVDZ+S0KJlpaAABAu
DL1+9NUN65CScMPr6Csc0qml+nDNVrVrHYBd6RwP+fuGeWK32/AslP7HH9TMKjpQXRimd1gMSTQLzW
v4Vgw1cdemfxbQghbNYEFvhYkox7FBlXyQx8hRAXXVslXz928eBm+UKYYQ3AgtrSVoQNXjZ6ugSGJ6
eih7SPp7ocf2ze+2UdUqUzEFAJ0lu7GAz6/0lCAhuNdJwZG1xFM0raBI3VLqszcTrJlXG3xNL/3FSN
dwEKyF7Iogx+5V0DIWEk8iMAOviRfQySWD/JaUhTfUgkQ23gsVC+agIN2lEyv1G5dxXmHrSq73sO/9
wuvIoSjjZ91hBS8k3fBedaOqJO4pzFceWp9A7owTWAdxRc55GO6RoiDRj+yZvc+z0DweIiFinDnz9E
/Volb7pKR6jWAKP0kmLKLe4iKaVZavEhPkVGXzSykWoW+FgM7dWUOAJORxFA4w1JVM0c2Oh3WeEI1N
tX7dCJmO5HzW8eEFbHdz6ugqi52E7d8Yjshm4JaVy2j3+CoS6DmAKugqi52E7d8Wc54f+WEUXj0Il6
aEiDkMsQoHgRcrkK8QA+bzUHWm7/ummQ+/u3ap0yiXRMscjeQ7PwLXk3OaxoOKUxEPFUtITdN+FEhK
M6iFvPRyDRP/JiUGlDT3fr8DcraKY/hDRZOcIvmnk5shA2HmQ7x8u1iM/bletjT8Z0EMC2w7R2CSU5
K0HCi1G/euQEAShmqwJLogqy3IfT8TDGD5IsJKB6gp61GZn+9cPO1IrU32suYxOFn2nfZTljPqbx9f
yPOHaFdL6mHVYxueE7I/LAcB2QyDzlYgDHUCn/0xAbOi+g4kRFi2CGq5lFd+G2K3+COU/1H+bHlKsi
qLtWVuwSInnxNtcYk+zZhq/INhu08+4rzGgq0+12ZDBmuX6HtI+nuhx/bN77ZR1SpTMQ1+cUyxLeM1
ORJ3rtoTZsgnwiCWX6jgzqV39dMVuCwzFPGDnXpFgBl9pD9hO6jkbWFra+Iaxg8EoH6i5oltbwB+Qx
CcAOfsO6BEKq6gcd5o8Vr+oIBRtP3I2ZzOEdU4QmVuwSInnxNtZgIxfQaWHwxLzoRiSHz5frS2SyLu
+hpgHLGW/3I5QPtUJcQDUlel1wv0oSOQ3rwNDX9MRpwyEsYZ+9VYUlo9cPoBWF3ljZcPtA33wPClNU
ooQqgldIhI4rasMyl5C2yVURa54BNOwZ9jcHN5rfh8qQBpVkZbsd+CacBFcvTKHQL28GXQAZPtsIhH
gufjCyEwMYk+zZhq/INhu08+4rzGgq0+12ZDBmuX3Jqi37ohjeF1A/7iuGnYunPjPpvU4JDZSsqT/M
mP5HhL3TR8h00zQb7OyvcuSEwIEElwMu68SaGN5dYvQgtFIRJXYhxZHWGnPRkt3C0H7nftsv+x6N2o
2Liq+vKgkG56PhgPLupA2PJqCNofKSLrUP5KfLfvij6kWPJ7XAZvveLiEP1HvlKQwgN6JIy2KlZiJM
nAxiSCikdbkwiGjij1+UCqcAS//OgDd0ok2zswHJORVKDN80LnRtMol0TLHI3kPCGk5kXI5kB1zPRq
x08eMdbucHctz9wuvdf8qL/WjFYZSpe8Pa3c+vxrvykmbD+K52b1yGWOGzzgRCquoHHeaPvsFSdeax
io2O6RoiDRj+yUK+WHU/HIt08v9FveGIUf4lb7pKR6jWAQqUv2SQibKAgn3GnD8pi9ewVUlvuaZ6s0
stejI0ikx71J7Pz+lpHaFIJENt4LFQvl5VUKIyFNOfx7VEN7D7OUyjj+TtZ+fp1E5KZytfTvQKCy8y
hq37RAFpUxyBUgQWZ3aFzlowjVXKm90rILEvfwU7q047etKX4pdcBEn5mv51QXcQRi2hmmhOSmcrX0
70CgsvMoat+0QA9JScQBnuU4d2hc5aMI1VyTOGLvA03iY7XACbVtkSczVUW6xAJv4F91/nsrArcua8
eS8HZw5HNcodQhfu5yL7Rm7ThQxNzFIw21ft0ImY7kYuSFz/IWESbuCQS8WZXm635UsdruLkh59vGI
tr+VewSGkojKltRB3h9/qSC1I07QRVjpqf3XNxuixwHMCLCB4B1lqxDnyp70Tfl+gw9Wjdqwd5czen
At69AapojUEBVRpU2LKDuW2RtMiRVweEiYIbGN126Lnah/cxFbqtqpkyHLaK8gCapvzxRxHSKePG6g
4IKZZ3YivqOQaDS+A3kiquV3C0N7C63lbwuYCLfyJ/Xc57fADXRBlJSCRDbeCxULzaYTeJO46ZczJ6
fmcGh781uENBK1olwjpNXFM2LnU3Q0FBv2cqZ9dHkW9xaJiMagvsfZcyddsv8dEy/pQpqWwGW3iOTI
1
2
3
4
5
xyj/Bn9H22DJp7kw5NXa0pTQez/ofAHbTMh6boI9tTWvXMi6Uihi2jmznMy1xe+uHx0KufHTFqi37g
/zr3dGr0FfX32GC8g4X/2o/CGk5kXI5kBR7cBmNSWf/mCCmWd2Ir6jgtXdUp3lxAgUduywF6Ix5F+4
6UszslAqYTdUz3TIA//Y0NF7YIqyL5DSYVlFSx2N/h+S3nUEchFxrI3PCPr76oqt7B4IlK0zcHrVGB
PsrU5ByBGenya1b/eJ9GymSI8tkGg0vgN5Iqrc/9Ubgy3/aKPohyLRTxB/tj+dtzN62lfxj537FJhB
0g="
} 6
加密报⽂⽰例：
{
"amountActualPay": 0,
"amountActualReceive": 0,
"amountChannelSys": 196000,
"amountCouponTotal": 0,
"amountShouldPay": 196000,
"amountShouldReceive": 196000,
"customerAreaCode": "86",
"customerAreaCodeBackup": "86",
"customerMobile": "15230152733",
"customerMobileBackup": "",
"customerName": "兔⼦",
"customerWechat": "",
"endCityId": 217,
"endCityName": "东京",
"orderNoThird": "3333333",
"priceConfirmTwice": 1,
"priceMarkCity": 217,
"startCityId": 217,
"startCityName": "东京",
"totalDay": 2,
"travelDailyBaseReqList":
[
{
"amountActualPay": 0,
"amountActualReceive": 0,
"amountChannelSys": 126900,
"amountCouponSelf": 0,
"amountCouponTotal": 0,
"amountShouldPay": 126900,
"amountShouldReceive": 126900,
"dayIndex": 1,
"goodsType": 40,
"goodsTypeName": "市内包⻋",
"carModelId": 6,
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
"carModelName": "舒适5座",
"endAddressDetail": "1 Chome-1-2 Oshiage, Sumida, Tokyo 131-0045,
Japan",
"endCityId": 217,
"endCityName": "东京",
"endCityNameEn": "Tokyo",
"endCountryId": 60,
"endCountryName": "⽇本",
"endPoiId": "v228677",
"endPoiLocation": "35.7100627,139.8107004",
"endPoiName": "东京晴空塔",
"endPoiNameEn": "Tokyo Sky Tree",
"endPoiType": 3,
"serviceMaxDistance": 100,
"serviceMaxTime": 10,
"startAddressDetail": "Japan, 台场 （Tokyo）",
"startCityId": 217,
"startCityName": "东京",
"startCityNameEn": "Tokyo",
"startContinentId": 6,
"startContinentName": "亚洲",
"startCountryId": 60,
"startCountryName": "⽇本",
"startPoiId": "v227934",
"startPoiLocation": "35.6259224,139.7714143",
"startPoiName": "御台场",
"startPoiNameEn": "Odaiba",
"startPoiType": 3,
"serviceEndTimeBeijing": 1719673199000,
"serviceEndTimeLocal": 1719676799000,
"serviceStartTimeBeijing": 1719619200000,
"serviceStartTimeLocal": 1719622800000,
"travelAdditionalDailyBaseReq":
[],
"travelStationsBaseReq":
[]
}
],
"travelEndTimeBeijing": 1719696840000,
"travelEndTimeLocal": 1719700440000,
"travelPriceItemId": "11fb46f2-32d1-41fe-98b4-2bfd93c920671719554430792",
"travelStartTimeBeijing": 1719619200000,
"travelStartTimeLocal": 1719622800000,
"userId": "10961769847",
"userName": "兔⼦"
}
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
响应参数说明:
{
"data": {
"orderNos": [
"Z5615582130"
]
},
"status": 200,
"success": true
}
1
2
3
4
5
6
7
8
9
3.6.7 修改联系⼈信息（需要加密）
修改联系⼈信息，使⽤公共参数请求接⼝，encryptBody为下单参数序列化后使⽤DES加密的结果
接⼝地址 /otaorder-api/common/v1.0/order/modifyCustomerInfo?token=ACCESS_TOKEN
请求参数：
参数名称 说明 是否必须 类型
orderTravelNo 订单号 true string
customerName 主联系⼈姓名 false string
customerAreaCode 主联系⼈⼿机区号 false string
customerMobile 主联系⼈⼿机号 false string
customerAreaCodeBackup 主联系⼈备⽤⼿机区号 false string
customerMobileBackup 主联系⼈备⽤⼿机号 false string
creatorId 下单⼈id(3.6.4 订单详
情中返回)
true string
⼊参⽰例：
{ 1
"cid": "1909498346",
"sign": "1234",
"timestamp": 12345678,
"encryptBody":
"Q/wD3680/qgwcZqttZ9Wv5IEOfcjwDBOtkBmMVv/E0sF2DpUatu6KeQiOQRovVDZ+S0KJlpaAABAu
DL1+9NUN65CScMPr6Csc0qml+nDNVrVrHYBd6RwP+fuGeWK32/AslP7HH9TMKjpQXRimd1gMSTQLzW
v4Vgw1cdemfxbQghbNYEFvhYkox7FBlXyQx8hRAXXVslXz928eBm+UKYYQ3AgtrSVoQNXjZ6ugSGJ6
eih7SPp7ocf2ze+2UdUqUzEFAJ0lu7GAz6/0lCAhuNdJwZG1xFM0raBI3VLqszcTrJlXG3xNL/3FSN
dwEKyF7Iogx+5V0DIWEk8iMAOviRfQySWD/JaUhTfUgkQ23gsVC+agIN2lEyv1G5dxXmHrSq73sO/9
wuvIoSjjZ91hBS8k3fBedaOqJO4pzFceWp9A7owTWAdxRc55GO6RoiDRj+yZvc+z0DweIiFinDnz9E
/Volb7pKR6jWAKP0kmLKLe4iKaVZavEhPkVGXzSykWoW+FgM7dWUOAJORxFA4w1JVM0c2Oh3WeEI1N
tX7dCJmO5HzW8eEFbHdz6ugqi52E7d8Yjshm4JaVy2j3+CoS6DmAKugqi52E7d8Wc54f+WEUXj0Il6
aEiDkMsQoHgRcrkK8QA+bzUHWm7/ummQ+/u3ap0yiXRMscjeQ7PwLXk3OaxoOKUxEPFUtITdN+FEhK
M6iFvPRyDRP/JiUGlDT3fr8DcraKY/hDRZOcIvmnk5shA2HmQ7x8u1iM/bletjT8Z0EMC2w7R2CSU5
K0HCi1G/euQEAShmqwJLogqy3IfT8TDGD5IsJKB6gp61GZn+9cPO1IrU32suYxOFn2nfZTljPqbx9f
yPOHaFdL6mHVYxueE7I/LAcB2QyDzlYgDHUCn/0xAbOi+g4kRFi2CGq5lFd+G2K3+COU/1H+bHlKsi
qLtWVuwSInnxNtcYk+zZhq/INhu08+4rzGgq0+12ZDBmuX6HtI+nuhx/bN77ZR1SpTMQ1+cUyxLeM1
ORJ3rtoTZsgnwiCWX6jgzqV39dMVuCwzFPGDnXpFgBl9pD9hO6jkbWFra+Iaxg8EoH6i5oltbwB+Qx
CcAOfsO6BEKq6gcd5o8Vr+oIBRtP3I2ZzOEdU4QmVuwSInnxNtZgIxfQaWHwxLzoRiSHz5frS2SyLu
+hpgHLGW/3I5QPtUJcQDUlel1wv0oSOQ3rwNDX9MRpwyEsYZ+9VYUlo9cPoBWF3ljZcPtA33wPClNU
ooQqgldIhI4rasMyl5C2yVURa54BNOwZ9jcHN5rfh8qQBpVkZbsd+CacBFcvTKHQL28GXQAZPtsIhH
gufjCyEwMYk+zZhq/INhu08+4rzGgq0+12ZDBmuX3Jqi37ohjeF1A/7iuGnYunPjPpvU4JDZSsqT/M
mP5HhL3TR8h00zQb7OyvcuSEwIEElwMu68SaGN5dYvQgtFIRJXYhxZHWGnPRkt3C0H7nftsv+x6N2o
2Liq+vKgkG56PhgPLupA2PJqCNofKSLrUP5KfLfvij6kWPJ7XAZvveLiEP1HvlKQwgN6JIy2KlZiJM
nAxiSCikdbkwiGjij1+UCqcAS//OgDd0ok2zswHJORVKDN80LnRtMol0TLHI3kPCGk5kXI5kB1zPRq
x08eMdbucHctz9wuvdf8qL/WjFYZSpe8Pa3c+vxrvykmbD+K52b1yGWOGzzgRCquoHHeaPvsFSdeax
io2O6RoiDRj+yUK+WHU/HIt08v9FveGIUf4lb7pKR6jWAQqUv2SQibKAgn3GnD8pi9ewVUlvuaZ6s0
stejI0ikx71J7Pz+lpHaFIJENt4LFQvl5VUKIyFNOfx7VEN7D7OUyjj+TtZ+fp1E5KZytfTvQKCy8y
hq37RAFpUxyBUgQWZ3aFzlowjVXKm90rILEvfwU7q047etKX4pdcBEn5mv51QXcQRi2hmmhOSmcrX0
70CgsvMoat+0QA9JScQBnuU4d2hc5aMI1VyTOGLvA03iY7XACbVtkSczVUW6xAJv4F91/nsrArcua8
eS8HZw5HNcodQhfu5yL7Rm7ThQxNzFIw21ft0ImY7kYuSFz/IWESbuCQS8WZXm635UsdruLkh59vGI
tr+VewSGkojKltRB3h9/qSC1I07QRVjpqf3XNxuixwHMCLCB4B1lqxDnyp70Tfl+gw9Wjdqwd5czen
At69AapojUEBVRpU2LKDuW2RtMiRVweEiYIbGN126Lnah/cxFbqtqpkyHLaK8gCapvzxRxHSKePG6g
4IKZZ3YivqOQaDS+A3kiquV3C0N7C63lbwuYCLfyJ/Xc57fADXRBlJSCRDbeCxULzaYTeJO46ZczJ6
fmcGh781uENBK1olwjpNXFM2LnU3Q0FBv2cqZ9dHkW9xaJiMagvsfZcyddsv8dEy/pQpqWwGW3iOTI
xyj/Bn9H22DJp7kw5NXa0pTQez/ofAHbTMh6boI9tTWvXMi6Uihi2jmznMy1xe+uHx0KufHTFqi37g
/zr3dGr0FfX32GC8g4X/2o/CGk5kXI5kBR7cBmNSWf/mCCmWd2Ir6jgtXdUp3lxAgUduywF6Ix5F+4
6UszslAqYTdUz3TIA//Y0NF7YIqyL5DSYVlFSx2N/h+S3nUEchFxrI3PCPr76oqt7B4IlK0zcHrVGB
PsrU5ByBGenya1b/eJ9GymSI8tkGg0vgN5Iqrc/9Ubgy3/aKPohyLRTxB/tj+dtzN62lfxj537FJhB
0g="
}
2
3
4
5
6
加密报⽂⽰例：
{ 1
"creatorId": "128658",
"customerAreaCode": "010",
"customerAreaCodeBackup": "020",
"customerMobile": "88101",
"customerMobileBackup": "88202",
"customerName": "测试李12",
"orderTravelNo": "Z0831"
}
2
3
4
5
6
7
8
9
响应参数说明:
{
"data": true,
"status": 200,
"success": true
}
1
2
3
4
5
3.7 订单评价
3.7.1新增评价
接⼝地址 /otaorder-api/comment/v1.0/add?token=ACCESS_TOKEN
接⼝参数
名称 说明 是否必填 类型
orderTravelNo 订单号 true string
commentGrade 星级 false Integer
commentContent 评价内容 true string
commentPics 评价图⽚json数组 false string
请求⽰例
{
"cid":"1909498346",
"sign":"1234",
"timestamp":12345678,
1
2
3
4
"orderTravelNo":"Z4549916911",
"commentContent":"评价内容"
}
5
6
7
3.7.2更新评价
接⼝地址 /otaorder-api/comment/v1.0/update?token=ACCESS_TOKEN
接⼝参数
名称 说明 是否必填 类型
orderTravelNo 订单号 true string
commentGrade 星级 false Integer
commentContent 评价内容 true string
commentPics 评价图⽚json数组 false string
请求⽰例同3.7.1
3.7.3删除评价
接⼝地址 /otaorder-api/comment/v1.0/remove?token=ACCESS_TOKEN
接⼝参数
名称 说明 是否必填 类型
id 评价id true string
请求⽰例
{
"cid":"1909498346",
"sign":"1234",
"timestamp":12345678,
"id":1
}
1
2
3
4
5
6
3.7.4评价列表
接⼝地址 /otaorder-api/comment/v1.0/list?token=ACCESS_TOKEN
接⼝参数
名称 说明 是否必填 类型
orderTravelNo 订单号 true string
返回参数
名称 说明 类型
orderTravelNo 订单号 string
cityName 城市名称 string
commentContent 评价内容 string
commentGrade 星级 int32
commentGradeName 评价星级 string
commentLevel 来源：1=优质，2=普通 默认 2 int32
commentLevelName 评价级别名称 string
commentName 评价名称 string
commentPics 评价图⽚,json数组 string
commentSource 来源：1=客⼈，2=运营 int32
commentSourceName 来源 string
commentStatus 审核状态：0-未⼈⼯⼲预;1-⼈⼯⼲
预未通过; 2-⼈⼯⼲预通过
int32
commentStatusName 评价状态 string
commentTime 评论时间 string
commentType 评价类型1=当地⼈，2=商品，3=视
频
int32
createTime 创建时间 date-time
fromPhoto ⽤⼾头像 string
fromUname 评价⼈名称 string
goodsTypeName 产品类型名称 string
id 主键 int32
orderConfirmNo 预定项no string
orderTravelNo ⾏程订单号 string
orderTypeName 城市名称 string
picInfo 图⽚信息 string
providerId 当地⼈ID string
providerName 当地⼈名称 string
providerNo 当地⼈ID string
serviceProviderId 真正服务供应商id string
showStatusName 是否可⻅ string
studioId ⼯作室id string
studioMemberName ⼯作室成员名称 string
studioMemberNo ⼯作室成员编号 string
studioName ⼯作室名称 string
studioNo ⼯作室编号 string
thirdId 第三⽅ID:视频id 等 string
updateTime 更新时间 date-time
返回⽰例
3.8 取消退款
3.8.1 查询退改规则
接⼝地址 /otaorder-api/common/v1.0/order/queryUserRegressionRule?token=ACCESS_TOKEN
请求参数
名称 说明 是否必填 类型
orderTravelNo 订单号 true string
cancelType 取消类型（1:⽤⼾原因 2:
⼯作室原因 3:平台原因 4:
不可抗⼒）
true Integer
返回参数
名称 说明 类型
couponRefundInstruction 优惠券退款说明 string
description 取消规则说明 string
freeCancelTime 免费取消时间 string
nowCityTime 当前订单服务城市时间 string(date-time)
orderNo 订单号 string
personalActuallyPaid 订单实付⾦额 integer(int32)
personalCancelDeduction 取消扣款 integer(int32)
personalRefundableAmount 可退⾦额 integer(int32)
produceTypeName 产品类型 string
serviceTime 出发时间 string(date-time)
userCancelReasonListRsp ⽤⼾取消原因列表 list
userCancelReasonListRsp.assign
StudioStatus
指派⼯作室取消原因1：指派，0：
未指派
integer(int32)
userCancelReasonListRsp.cancel
Reason
取消原因 string
userCancelReasonListRsp.cancel
Type
取消类型(取消类型 1.客⼈原因，2.
当地⼈原因，3.平台原因，5.不可
抗⼒
integer(int32)
userCancelReasonListRsp.descri
ption
说明 string
userCancelReasonListRsp.guide
Punish
是否扣罚当地⼈：1 是、0 否 integer(int32)
userCancelReasonListRsp.id id integer(int32)
userCancelReasonListRsp.trigger
Worksheet
是否触发⼯单：1 是、0 否 integer(int32)
userCancelReasonListRsp.type 类型1:专⻋,2:吃喝玩乐 integer(int32)
userId ⽤⼾id string
响应参数说明:
{
"data": {
"description": "北京时间07⽉19⽇10:00前;\n当地时间07⽉19⽇10:00前;\n可免费取
消，之后将收取100%订单实付⾦额;",
"freeCancelTime": "24",
"nowCityTime": "2024-08-14 19:59:05",
"personalCancelDeduction": 0,
"personalRefundableAmount": 1110,
"produceTypeName": "包⻋",
"userCancelReasonListRsp": [
{
"assignStudioStatus": 0,
"cancelReason": "订单填写信息有误",
"cancelType": 1,
"description": "C端取消订单提⽰",
"guidePunish": 0,
"id": 99,
"triggerWorksheet": 0,
"type": 1
},
{
"assignStudioStatus": 0,
"cancelReason": "⾏程有变，要改变⽤⻋时间",
"cancelType": 1,
"description": "C端取消订单原因收集",
"guidePunish": 0,
"id": 100,
"triggerWorksheet": 0,
"type": 1
}
]
},
"status": 200,
"success": true
}
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
3.8.2 订单退款
接⼝地址 /otaorder-api/common/v1.0/order/cancel?token=ACCESS_TOKEN
请求参数
名称 说明 是否必填 类型
orderTravelNo 订单号 true string
cancelReason 取消原因 true String
cancelType 取消类型（1:⽤⼾原因 2:
⼯作室原因 3:平台原因 4:
不可抗⼒）
true Integer
cancelRemark 备注 false Integer
actuallyPaidPrice 实付⾦额 false Integer
响应参数
类型 说明
string true 成功 false 失败
响应参数说明:
{
"data":"true",
"status": 200,
"success": true
}
1
2
3
4
5
3.9 政策
3.9.1 规则说明
接⼝地址：/otaorder-api/common/policy/getPolicy?token=ACCESS_TOKEN
请求参数
名称 说明 是否必填 类型
customizeType 定制⽅式：10平台定制，20当地⼈定
制 30 CAPP
固定30
是 Integer
typeId 1填单⻚ 2订单详情
固定1
是 Integer
confirmList array
confirmList.productType 订单商品类型(1=接机,2=送机,3=单次接
送,4=包⻋,5=玩乐体验,6=定制游 7 签
证 8 酒店 9 机票 10 ⻔票 11 餐厅)
是 Integer
confirmList.productTypeNa
me
否 string
confirmList.cityId 城市ID 是 Integer
confirmList.serviceStartTime
Local
服务开始时间(当地) 是 string(date-time)
返回参数
参数名称 说明 类型
customizeType 定制⽅式：10平台定制，20当地⼈定制 int32
msgRspList 类型数据取消规则信息 array
bottomTitle 底部提⽰ string
index 排序 int64
message 取消政策 string
cancelTime 取消时间临界点(北京时间) date
messageChange 更改政策 string
orderConfirmNo 预订项单号，系统统⼀⽣成 string
productName 商品 string
productType 订单商品类型(1=接机,2=送机,3=单次接送,4=包⻋,5=玩乐体
验,6=定制游 7 签证 8 酒店 9 机票 10 ⻔票 11 餐厅)
int32
productTypeName
订单商品类型 string
title 主题 string
tripName ⾏程⽅案名称 string
typeId 返回类型数据，1免费取消时间，2不可免费取消，3取消规
则
int32
typeName 返回类型数据，1免费取消时间，2不可免费取消，3取消规
则
string
请求实例：
{
"customizeType": 30,
"typeId": 1,
"confirmList":
[
{
"productType": 4,
"productTypeName": "包⻋",
"cityId": "217",
"serviceStartTimeLocal": "2024-07-31 12:30:00"
}
],
"requestSource": 1
}
1
2
3
4
5
6
7
8
9
10
11
12
13
14
响应参数说明:
{
"data": {
"customizeType": 30,
"msgRspList": [
{
"bottomTitle": "⽀付后,北京时间07⽉29⽇11:30前可免费取消",
"cancelTime": "2024-07-29 11:30:00",
1
2
3
4
5
6
7
"message": "北京时间07⽉29⽇11:30前;\n当地时间07⽉29⽇12:30前;\n可免费取
消，之后将收取100%订单实付⾦额;",
"productType": 4,
"productTypeName": "包⻋",
"title": ""
}
],
"typeId": 1,
"typeName": "取消规则"
},
"status": 200,
"success": true
}
8
9
10
11
12
13
14
15
16
17
18
19
3.10 微信建群⼆维码
3.10.1 查询建群⼆维码
接⼝地址：/otaorder-api/common/v1.0/wecom/queryWeComExt?token=ACCESS_TOKEN
请求参数
名称 说明 是否必填 类型
orderTravelNo 订单编号 是 String
返回参数
参数名称 说明 类型
codeUrl ⼆维码链接 string
orderTravelNo 订单编号 string
orderConfirmNo ⼦单编号 string
purchaseNo 确认单号 string
请求实例：
{
"cid": "1032880064",
1
2
"sign": "22de4f134c01d2f9a772f3cdca350cf2",
"timestamp": "2024-02-01 10:57:00",
"orderTravelNo": "Z45976616148739"
}
3
4
5
6
响应参数说明:
{
"data": [
{
"codeUrl":
"https://wework.qpic.cn/wwpic3az/20450_MnjzfHl1TTKtMtl_1722334223/0",
"orderConfirmNo": "J45976616148738",
"orderTravelNo": "Z45976616148739",
"purchaseNo": "CP03318715469137"
},
{
"codeUrl":
"https://wework.qpic.cn/wwpic3az/697275_QbLE6JYkSVujvKV_1722334225/0",
"orderConfirmNo": "J45976616148738",
"orderTravelNo": "Z45976616148739",
"purchaseNo": "CP03318715469137"
},
{
"codeUrl":
"https://wework.qpic.cn/wwpic3az/700071_SxL888PGTX2wOxj_1722334228/0",
"orderConfirmNo": "J45976616148738",
"orderTravelNo": "Z45976616148739",
"purchaseNo": "CP03318715469137"
}
],
"status": 200,
"success": true,
"traceId": "[Ignored Trace]"
}
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
3.11 优惠券
3.11.1 优惠券领取
接⼝地址：/otaorder-api/common/marketing/v1.0/coupon/apiBindCoupon?
token=ACCESS_TOKEN
请求参数
名称 说明 是否必填 类型
userId ⽤⼾id(⽤⼾id 和 ⽤⼾区号+⼿机号 ⾄
少填写⼀种，如果都填写，以⽤⼾id为
准)
否 String
areaCode 区号 否 String
mobile ⼿机号 否 String
返回参数： ⽆
请求实例：
{
"cid": "10328800641",
"sign": "22de4f134c01d2f9a772f3cdca350cf2",
"timestamp": "2024-02-01 10:57:00",
"userId": "*********"
}
1
2
3
4
5
6
4.渠道提供的回调API
状态码说明
编码 备注说明
3002 导游已接单
3010 司导取消重发
4002 订单取消
3211 司导出发
3221 司导到达
3231 接到客⼈
3241 司导服务完成
4003 订单结算
4.1 确认当地⼈
⽰例回调URL：http://www.xxxx.com/openApi/order/driverConfirm
请求⽅式：POST
⽰例参数：{"orderNo":"123456","orderStatus":3002,"orderStatusName":"导游已接单"}
名称 说明 类型
orderNo 订单号 string
orderStatus 状态码 Integer
orderStatusName 状态名称 string
4.2 当地⼈出发
只有⾏程的第⼀天会回调
⽰例回调URL：http://www.xxxx.com/openApi/order/studioStart
请求⽅式：POST
⽰例参数：{"orderNo":"123456","orderStatus":3211,"orderStatusName":"司导出发"}
名称 说明 类型
orderNo 订单号 string
orderStatus 状态码 Integer
orderStatusName 状态名称 string
4.3 当地⼈到达(暂不给回调)
⾏程的每天会回调
⽰例回调URL：http://www.xxxx.com/openApi/order/orderStatusChange
请求⽅式：POST
⽰例参数：{"orderNo":"123456","orderStatus":3221,"orderStatusName":"司导到达"}
名称 说明 类型
orderNo 订单号 string
orderStatus 状态码 Integer
orderStatusName 状态名称 string
4.4 接到客⼈
⾏程的每天会回调
⽰例回调URL：http://www.xxxx.com/openApi/order/orderStatusChange
请求⽅式：POST
⽰例参数：{"orderNo":"123456","orderStatus":3231,"orderStatusName":"接到客⼈"}
名称 说明 类型
orderNo 订单号 string
orderStatus 状态码 Integer
orderStatusName 状态名称 string
4.5 当地⼈服务完成
⾏程的每天会回调
⽰例回调URL：http://www.xxxx.com/openApi/order/orderStatusChange
请求⽅式：POST
⽰例参数：{"orderNo":"123456","orderStatus":3241,"orderStatusName":"司导服务完成"}
名称 说明 类型
orderNo 订单号 string
orderStatus 状态码 Integer
orderStatusName 状态名称 string
4.6 取消订单
⽰例回调URL：http://www.xxxx.com/openApi/order/orderStatusChange
请求⽅式：POST
⽰例参数：{"orderNo":"123456","orderStatus":4002,"orderStatusName":"订单取消"}
名称 说明 类型
orderNo 订单号 string
orderStatus 状态码 Integer
orderStatusName 状态名称 string
4.7 当地⼈取消重发
当地⼈取消订单，系统重新派单
⽰例回调URL：http://www.xxxx.com/openApi/order/orderStatusChange
请求⽅式：POST
⽰例参数：{"orderNo":"123456","orderStatus":3010,"orderStatusName":"司导取消重发"}
名称 说明 类型
orderNo 订单号 string
orderStatus 状态码 Integer
orderStatusName 状态名称 string
4.8 订单结算
订单结算
⽰例回调URL：http://www.xxxx.com/openApi/order/orderStatusChange
请求⽅式：POST
⽰例参数：{"orderNo":"123456","orderStatus":4003,"orderStatusName":"订单结
算","priceSettle":2343}
名称 说明 类型
orderNo 订单号 string
orderStatus 状态码 Integer
orderStatusName 状态名称 string
priceSettle 结算价格 分 Integer
5.附录
5.1错误码
5.1.1皇包⻋的状态码
编码 提⽰信息 备注说明
200 成功 成功
9300002 签名错误 失败
9300003 请求已过期 允许客⼾端请求最⼤时间误差区间
[-10,10]min
9330001 渠道不存在或已禁⽤
9350001 微信建群⼆维码不存在
