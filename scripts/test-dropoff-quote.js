#!/usr/bin/env node

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

console.log('🧪 测试送机报价工具调用...\n');

// 启动 MCP 服务器
const mcpProcess = spawn('node', [join(projectRoot, 'dist/mcp-launcher.js')], {
  env: {
    ...process.env,
    HITRIP_CID: '1086910840',
    HITRIP_SECRET_KEY: '12345678',
    HITRIP_BASE_URL: 'https://api-gw.test.huangbaoche.com/',
    NODE_ENV: 'production',
    LOG_LEVEL: 'debug'
  },
  stdio: ['pipe', 'pipe', 'pipe']
});

let responseBuffer = '';

mcpProcess.stdout.on('data', (data) => {
  responseBuffer += data.toString();
  
  // 检查是否收到完整的 JSON-RPC 响应
  const lines = responseBuffer.split('\n');
  for (const line of lines) {
    if (line.trim() && line.startsWith('{')) {
      try {
        const response = JSON.parse(line);
        if (response.id === 2) { // 只显示送机报价的响应
          console.log('📨 送机报价响应:', JSON.stringify(response, null, 2));
        }
      } catch (e) {
        // 忽略解析错误
      }
    }
  }
});

mcpProcess.stderr.on('data', (data) => {
  console.error('❌ 错误输出:', data.toString());
});

// 等待服务器启动
setTimeout(() => {
  console.log('🚗 发送送机报价请求...');
  
  const dropoffQuoteRequest = {
    jsonrpc: '2.0',
    id: 2,
    method: 'tools/call',
    params: {
      name: 'hitrip_dropoff_quote',
      arguments: {
        airportCode: 'PEK',
        airportName: '北京首都国际机场',
        serviceTime: '2025-09-10 14:00:00',
        serviceCityId: 1,
        serviceCityName: '北京',
        origin: {
          address: '北京市朝阳区国贸CBD',
          addressDetail: '北京市朝阳区国贸CBD中心',
          location: '39.908722,116.447861'
        },
        endAddress: '北京首都国际机场',
        endDetailAddress: '北京首都国际机场T3航站楼',
        endLocation: '40.080111,116.584556',
        carModelId: '1,2,3'
      }
    }
  };
  
  mcpProcess.stdin.write(JSON.stringify(dropoffQuoteRequest) + '\n');
  
  // 等待响应后关闭
  setTimeout(() => {
    console.log('\n✅ 测试完成，关闭服务器...');
    mcpProcess.kill();
  }, 5000);
  
}, 1000);

mcpProcess.on('close', (code) => {
  console.log(`\n🔚 MCP 服务器已关闭，退出码: ${code}`);
});

mcpProcess.on('error', (error) => {
  console.error('❌ 启动 MCP 服务器失败:', error);
});
