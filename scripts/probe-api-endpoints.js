#!/usr/bin/env node

/**
 * 探测黄包车API端点
 */

import axios from 'axios';
import crypto from 'crypto';

const baseUrl = 'https://api-gw.test.huangbaoche.com';
const cid = '1086910840';
const secretKey = '12345678';

// 生成签名
function generateSignature(params) {
  const sortedKeys = Object.keys(params)
    .filter(key => key !== 'sign' && params[key] !== undefined && params[key] !== null)
    .sort();
  
  const queryString = sortedKeys
    .map(key => `${key}=${params[key]}`)
    .join('&');
  
  const signString = queryString + secretKey;
  return crypto.createHash('md5').update(signString).digest('hex');
}

// 创建签名参数
function createSignedParams(params = {}) {
  const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);
  const signedParams = {
    ...params,
    cid,
    timestamp,
  };
  
  signedParams.sign = generateSignature(signedParams);
  return signedParams;
}

// 测试端点
async function testEndpoint(path, description) {
  console.log(`\n🔍 测试: ${description}`);
  console.log(`   路径: ${path}`);
  
  try {
    const params = createSignedParams();
    const response = await axios.post(`${baseUrl}${path}`, params, {
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Hitrip-MCP-Server/1.0.0'
      }
    });
    
    console.log(`   ✅ 状态: ${response.status}`);
    console.log(`   📄 响应: ${JSON.stringify(response.data).substring(0, 200)}...`);
    return true;
    
  } catch (error) {
    if (error.response) {
      console.log(`   ❌ 状态: ${error.response.status}`);
      console.log(`   📄 响应: ${JSON.stringify(error.response.data || {}).substring(0, 200)}`);
    } else {
      console.log(`   ❌ 错误: ${error.message}`);
    }
    return false;
  }
}

async function main() {
  console.log('🚀 黄包车API端点探测');
  console.log('='.repeat(50));
  console.log(`基础URL: ${baseUrl}`);
  console.log(`CID: ${cid}`);
  console.log(`密钥: ${'*'.repeat(secretKey.length)}`);
  
  // 可能的API端点路径
  const endpoints = [
    // 原始路径
    { path: '/getToken', desc: '获取Token (无前缀)' },
    { path: '/getCityList', desc: '城市列表 (无前缀)' },
    
    // 带open前缀
    { path: '/open/getToken', desc: '获取Token (open前缀)' },
    { path: '/open/getCityList', desc: '城市列表 (open前缀)' },
    
    // 带api前缀
    { path: '/api/getToken', desc: '获取Token (api前缀)' },
    { path: '/api/getCityList', desc: '城市列表 (api前缀)' },
    
    // 带v1前缀
    { path: '/v1/getToken', desc: '获取Token (v1前缀)' },
    { path: '/v1/getCityList', desc: '城市列表 (v1前缀)' },
    
    // 带open/api前缀
    { path: '/open/api/getToken', desc: '获取Token (open/api前缀)' },
    { path: '/open/api/getCityList', desc: '城市列表 (open/api前缀)' },
    
    // 其他可能的路径
    { path: '/openapi/getToken', desc: '获取Token (openapi前缀)' },
    { path: '/openapi/getCityList', desc: '城市列表 (openapi前缀)' },
  ];
  
  const results = [];
  
  for (const endpoint of endpoints) {
    const success = await testEndpoint(endpoint.path, endpoint.desc);
    results.push({ ...endpoint, success });
    
    // 避免请求过快
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  console.log('\n📊 测试结果汇总:');
  console.log('='.repeat(50));
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  if (successful.length > 0) {
    console.log(`✅ 成功的端点 (${successful.length}个):`);
    successful.forEach(r => {
      console.log(`   ${r.path} - ${r.desc}`);
    });
  }
  
  if (failed.length > 0) {
    console.log(`\n❌ 失败的端点 (${failed.length}个):`);
    failed.forEach(r => {
      console.log(`   ${r.path} - ${r.desc}`);
    });
  }
  
  if (successful.length > 0) {
    console.log('\n💡 建议:');
    console.log('根据成功的端点更新API客户端中的路径配置');
  } else {
    console.log('\n⚠️  所有端点都失败了，可能的原因:');
    console.log('1. API服务器不可用');
    console.log('2. 认证参数不正确');
    console.log('3. 需要其他的路径前缀');
    console.log('4. 需要特殊的请求头或参数');
  }
}
