#!/usr/bin/env node

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

console.log('🧪 测试创建接机订单工具调用...\n');

// 启动 MCP 服务器
const mcpProcess = spawn('node', [join(projectRoot, 'dist/mcp-launcher.js')], {
  env: {
    ...process.env,
    HITRIP_CID: '1086910840',
    HITRIP_SECRET_KEY: '12345678',
    HITRIP_BASE_URL: 'https://api-gw.test.huangbaoche.com/',
    NODE_ENV: 'production',
    LOG_LEVEL: 'debug'
  },
  stdio: ['pipe', 'pipe', 'pipe']
});

let responseBuffer = '';

mcpProcess.stdout.on('data', (data) => {
  responseBuffer += data.toString();
  
  // 检查是否收到完整的 JSON-RPC 响应
  const lines = responseBuffer.split('\n');
  for (const line of lines) {
    if (line.trim() && line.startsWith('{')) {
      try {
        const response = JSON.parse(line);
        if (response.id === 2) { // 只显示创建订单的响应
          console.log('📨 创建接机订单响应:', JSON.stringify(response, null, 2));
        }
      } catch (e) {
        // 忽略解析错误
      }
    }
  }
});

mcpProcess.stderr.on('data', (data) => {
  console.error('❌ 错误输出:', data.toString());
});

// 等待服务器启动
setTimeout(() => {
  console.log('✈️ 发送创建接机订单请求...');
  
  const createPickupOrderRequest = {
    jsonrpc: '2.0',
    id: 2,
    method: 'tools/call',
    params: {
      name: 'hitrip_create_pickup_order',
      arguments: {
        // 航班信息
        flightNo: 'CA181',
        flightDestCode: 'PEK',
        flightDestAirportName: '北京首都国际机场',
        flightDestBuilding: 'T3',
        flightArriveTime: '2025-09-10 10:30:00',
        flightFlyTime: '2025-09-10 08:00:00',
        flightAirportCode: 'PVG',
        flightAirportName: '上海浦东国际机场',
        
        // 订单信息
        orderThirdNo: 'TEST_ORDER_' + Date.now(),
        priceActual: 15000, // 150元，单位：分
        priceChannel: 14000, // 140元，单位：分
        
        // 服务信息
        serviceCarModel: '1',
        serviceCarModelName: '经济型',
        serviceCityId: 1,
        serviceCityName: '北京',
        serviceTime: '2025-09-10 11:00:00',
        
        // 地址信息
        serviceStartAddress: '北京首都国际机场',
        serviceStartAddressDetail: '北京首都国际机场T3航站楼',
        serviceStartPoint: '40.080111,116.584556',
        serviceDestAddress: '北京市朝阳区国贸CBD',
        serviceDestAddressDetail: '北京市朝阳区国贸CBD中心',
        serviceDestPoint: '39.908722,116.447861',
        
        // 乘客信息
        servicePassagerName: '张三',
        servicePassagerMobile: '13800138000',
        servicePassagerAreacode: '+86',
        
        // 商户信息
        orderChannel: '1086910840',
        orderChannelName: '测试商户',

        // 报价参数（必需）
        airportPriceQuestParam: {
          airportCode: 'PEK',
          airportName: '北京首都国际机场',
          channelId: 1086910840,
          endAddress: '北京市朝阳区国贸CBD',
          endDetailAddress: '北京市朝阳区国贸CBD中心',
          endLocation: '39.908722,116.447861',
          serviceCityId: 1,
          serviceCityName: '北京',
          serviceTime: '2025-09-10 11:00:00',
          startAddress: '北京首都国际机场',
          startCityId: 1,
          startCityName: '北京',
          startDetailAddress: '北京首都国际机场T3航站楼',
          startLocation: '40.080111,116.584556'
        },

        // 可选信息
        userRemark: '测试订单，请勿实际派车'
      }
    }
  };
  
  mcpProcess.stdin.write(JSON.stringify(createPickupOrderRequest) + '\n');
  
  // 等待响应后关闭
  setTimeout(() => {
    console.log('\n✅ 测试完成，关闭服务器...');
    mcpProcess.kill();
  }, 5000);
  
}, 1000);

mcpProcess.on('close', (code) => {
  console.log(`\n🔚 MCP 服务器已关闭，退出码: ${code}`);
});

mcpProcess.on('error', (error) => {
  console.error('❌ 启动 MCP 服务器失败:', error);
});
