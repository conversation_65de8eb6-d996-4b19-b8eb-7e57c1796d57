#!/usr/bin/env node

import fetch from 'node-fetch';

console.log('🍒 Cherry Studio 配置格式测试');
console.log('===============================\n');

const testConfigs = [
  {
    name: 'Cherry Studio SSE 格式 1',
    config: {
      type: 'sse',
      url: 'http://localhost:8081/sse',
      timeout: 30000
    },
    description: '使用 url 字段指向 SSE 端点'
  },
  {
    name: 'Cherry Studio SSE 格式 2',
    config: {
      type: 'sse',
      sseUrl: 'http://localhost:8081/sse',
      mcpUrl: 'http://localhost:8081/mcp',
      timeout: 30000
    },
    description: '分别指定 sseUrl 和 mcpUrl'
  },
  {
    name: 'Cherry Studio HTTP 格式 1',
    config: {
      type: 'http',
      url: 'http://localhost:8081/mcp',
      timeout: 30000
    },
    description: '使用 HTTP 模式连接 MCP 端点'
  },
  {
    name: 'Cherry Studio HTTP 格式 2',
    config: {
      type: 'http',
      url: 'http://localhost:8080/mcp',
      timeout: 30000
    },
    description: '使用 HTTP 模式连接 HTTP 服务器'
  },
  {
    name: 'Cherry Studio WebSocket 格式',
    config: {
      type: 'websocket',
      url: 'ws://localhost:8080/ws',
      timeout: 30000
    },
    description: '使用 WebSocket 模式'
  }
];

async function testConfig(config) {
  console.log(`🧪 测试: ${config.name}`);
  console.log(`📝 描述: ${config.description}`);
  console.log(`⚙️ 配置:`, JSON.stringify(config.config, null, 2));
  
  try {
    if (config.config.type === 'sse') {
      // 测试 SSE 连接
      const sseUrl = config.config.url || config.config.sseUrl;
      const response = await fetch(sseUrl, {
        headers: { 'Accept': 'text/event-stream' }
      });
      
      if (response.ok && response.headers.get('content-type')?.includes('text/event-stream')) {
        console.log('   ✅ SSE 端点可访问');
      } else {
        console.log('   ❌ SSE 端点问题');
      }
      
      // 测试 MCP 端点
      if (config.config.mcpUrl) {
        const mcpResponse = await fetch(config.config.mcpUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            jsonrpc: '2.0',
            id: 1,
            method: 'tools/list',
            params: {}
          })
        });
        
        if (mcpResponse.ok) {
          console.log('   ✅ MCP 端点可访问');
        } else {
          console.log('   ❌ MCP 端点问题');
        }
      }
      
    } else if (config.config.type === 'http') {
      // 测试 HTTP 连接
      const response = await fetch(config.config.url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          jsonrpc: '2.0',
          id: 1,
          method: 'tools/list',
          params: {}
        })
      });
      
      if (response.ok) {
        const data = await response.json();
        if (data.result && data.result.tools) {
          console.log(`   ✅ HTTP MCP 连接成功，工具数量: ${data.result.tools.length}`);
        } else {
          console.log('   ❌ HTTP MCP 响应格式错误');
        }
      } else {
        console.log(`   ❌ HTTP 连接失败: ${response.status}`);
      }
      
    } else if (config.config.type === 'websocket') {
      // WebSocket 测试需要特殊处理
      console.log('   ℹ️ WebSocket 测试需要专门的客户端');
    }
    
    console.log('   ✅ 配置格式有效\n');
    
  } catch (error) {
    console.log(`   ❌ 测试失败: ${error.message}\n`);
  }
}

async function main() {
  for (const config of testConfigs) {
    await testConfig(config);
  }
  
  console.log('🎯 Cherry Studio 配置建议:');
  console.log('==========================\n');
  
  console.log('基于测试结果，推荐以下配置顺序:\n');
  
  console.log('1. 🥇 首选 - HTTP 模式 (最稳定):');
  console.log('```json');
  console.log(JSON.stringify({
    mcpServers: {
      hitrip: {
        name: "Hitrip MCP Server",
        type: "http",
        url: "http://localhost:8081/mcp",
        timeout: 30000
      }
    }
  }, null, 2));
  console.log('```\n');
  
  console.log('2. 🥈 备选 - HTTP 服务器模式:');
  console.log('```json');
  console.log(JSON.stringify({
    mcpServers: {
      hitrip: {
        name: "Hitrip MCP Server",
        type: "http",
        url: "http://localhost:8080/mcp",
        timeout: 30000
      }
    }
  }, null, 2));
  console.log('```\n');
  
  console.log('3. 🥉 实验性 - SSE 模式 (如果 Cherry Studio 支持):');
  console.log('```json');
  console.log(JSON.stringify({
    mcpServers: {
      hitrip: {
        name: "Hitrip MCP Server",
        type: "sse",
        url: "http://localhost:8081/sse",
        timeout: 30000
      }
    }
  }, null, 2));
  console.log('```\n');
  
  console.log('🔍 Cherry Studio SSE 问题可能原因:');
  console.log('1. Cherry Studio 版本不支持 SSE 模式');
  console.log('2. SSE 配置格式与 Cherry Studio 期望不匹配');
  console.log('3. Cherry Studio 需要特定的 SSE 实现方式');
  console.log('4. 浏览器安全策略限制 SSE 连接');
  console.log('5. Cherry Studio 内部 SSE 客户端实现问题\n');
  
  console.log('💡 调试建议:');
  console.log('1. 查看 Cherry Studio 开发者工具控制台');
  console.log('2. 检查 Cherry Studio 版本和文档');
  console.log('3. 尝试简化配置格式');
  console.log('4. 联系 Cherry Studio 开发者确认 SSE 支持');
  console.log('5. 使用 HTTP 模式作为稳定的替代方案');
}

main().catch(console.error);
