#!/usr/bin/env node

/**
 * 调试机场API请求的详细脚本
 */

import { HitripApiClient } from '../dist/api/client.js';

console.log('🔍 机场API请求调试');
console.log('='.repeat(50));

// 创建API客户端
const client = new HitripApiClient({
  cid: process.env.HITRIP_CID || '1086910840',
  secretKey: process.env.HITRIP_SECRET_KEY || '12345678',
  baseUrl: process.env.HITRIP_BASE_URL || 'https://api-gw.test.huangbaoche.com/',
});

// 拦截HTTP请求来查看实际发送的数据
const originalPost = client.httpClient.post;
client.httpClient.post = function(url, data, config) {
  console.log('\n📤 HTTP请求详情:');
  console.log('URL:', url);
  console.log('请求体:', JSON.stringify(data, null, 2));
  console.log('配置:', config ? JSON.stringify(config, null, 2) : '无');
  
  return originalPost.call(this, url, data, config).then(response => {
    console.log('\n📥 HTTP响应详情:');
    console.log('状态:', response.status);
    console.log('响应头:', JSON.stringify(response.headers, null, 2));
    console.log('响应体:', JSON.stringify(response.data, null, 2));
    return response;
  }).catch(error => {
    console.log('\n❌ HTTP请求错误:');
    console.log('错误:', error.message);
    if (error.response) {
      console.log('响应状态:', error.response.status);
      console.log('响应数据:', JSON.stringify(error.response.data, null, 2));
    }
    throw error;
  });
};

async function testAirportRequests() {
  try {
    console.log('\n1️⃣ 测试无参数机场查询...');
    const allAirports = await client.getAirports();
    console.log(`✅ 返回 ${allAirports.length} 个机场`);
    if (allAirports.length > 0) {
      console.log('前3个机场:', allAirports.slice(0, 3).map(a => `${a.airportName} (${a.code})`));
    }

    console.log('\n2️⃣ 测试搜索"北京"...');
    const beijingAirports = await client.getAirports({ keyword: '北京' });
    console.log(`✅ 返回 ${beijingAirports.length} 个机场`);
    if (beijingAirports.length > 0) {
      beijingAirports.forEach(a => console.log(`- ${a.airportName} (${a.code}) - 城市ID: ${a.cityId}`));
    } else {
      console.log('❌ 没有找到包含"北京"的机场');
    }

    console.log('\n3️⃣ 测试搜索"Beijing"...');
    const beijingEnAirports = await client.getAirports({ keyword: 'Beijing' });
    console.log(`✅ 返回 ${beijingEnAirports.length} 个机场`);
    if (beijingEnAirports.length > 0) {
      beijingEnAirports.forEach(a => console.log(`- ${a.airportName} (${a.code}) - 城市ID: ${a.cityId}`));
    } else {
      console.log('❌ 没有找到包含"Beijing"的机场');
    }

    console.log('\n4️⃣ 测试搜索"首都"...');
    const capitalAirports = await client.getAirports({ keyword: '首都' });
    console.log(`✅ 返回 ${capitalAirports.length} 个机场`);
    if (capitalAirports.length > 0) {
      capitalAirports.forEach(a => console.log(`- ${a.airportName} (${a.code}) - 城市ID: ${a.cityId}`));
    } else {
      console.log('❌ 没有找到包含"首都"的机场');
    }

    console.log('\n5️⃣ 测试搜索"成田"（API文档示例）...');
    const naritaAirports = await client.getAirports({ keyword: '成田' });
    console.log(`✅ 返回 ${naritaAirports.length} 个机场`);
    if (naritaAirports.length > 0) {
      naritaAirports.forEach(a => console.log(`- ${a.airportName} (${a.code}) - 城市ID: ${a.cityId}`));
    } else {
      console.log('❌ 没有找到包含"成田"的机场');
    }

    console.log('\n6️⃣ 从全部机场中查找北京相关机场...');
    const beijingRelated = allAirports.filter(airport => 
      airport.airportName.includes('北京') || 
      airport.airportName.includes('Beijing') ||
      airport.airportName.includes('首都') ||
      airport.code === 'PEK' ||
      airport.code === 'PKX'
    );
    
    console.log(`🔍 在全部机场中找到 ${beijingRelated.length} 个北京相关机场:`);
    beijingRelated.forEach(a => console.log(`- ${a.airportName} (${a.code}) - 城市ID: ${a.cityId}`));

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.stack) {
      console.error('错误堆栈:', error.stack);
    }
  }
}

// 运行测试
testAirportRequests().then(() => {
  console.log('\n✅ 机场API调试完成');
  process.exit(0);
}).catch(error => {
  console.error('❌ 调试失败:', error);
  process.exit(1);
});
