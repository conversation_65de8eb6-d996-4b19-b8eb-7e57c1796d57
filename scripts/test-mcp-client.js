#!/usr/bin/env node

/**
 * 模拟MCP客户端测试
 */

const { spawn } = require('child_process');
const path = require('path');

const serverPath = path.join(__dirname, '../dist/mcp-launcher.js');

console.log('🧪 模拟MCP客户端测试');
console.log('='.repeat(40));

const env = {
  ...process.env,
  HITRIP_CID: '1086910840',
  HITRIP_SECRET_KEY: '12345678',
  HITRIP_BASE_URL: 'https://api-gw.test.huangbaoche.com/',
  NODE_ENV: 'production',
  LOG_LEVEL: 'error'
};

const server = spawn('node', [serverPath], {
  env,
  stdio: ['pipe', 'pipe', 'pipe']
});

let messageId = 1;
const responses = [];

server.stdout.on('data', (data) => {
  const lines = data.toString().split('\n').filter(line => line.trim());
  
  lines.forEach(line => {
    try {
      const response = JSON.parse(line);
      responses.push(response);
      console.log('📥 收到响应:', JSON.stringify(response, null, 2));
    } catch (error) {
      console.log('📥 非JSON响应:', line);
    }
  });
});

server.stderr.on('data', (data) => {
  console.log('🔍 服务器日志:', data.toString().trim());
});

server.on('error', (error) => {
  console.error('❌ 服务器错误:', error.message);
  process.exit(1);
});

function sendMessage(message) {
  const jsonMessage = JSON.stringify(message);
  console.log('📤 发送消息:', jsonMessage);
  server.stdin.write(jsonMessage + '\n');
}

// 测试序列
setTimeout(() => {
  console.log('\n1. 发送初始化消息...');
  sendMessage({
    jsonrpc: '2.0',
    id: messageId++,
    method: 'initialize',
    params: {
      protocolVersion: '2024-11-05',
      capabilities: {
        tools: {}
      },
      clientInfo: {
        name: 'test-client',
        version: '1.0.0'
      }
    }
  });
}, 1000);

setTimeout(() => {
  console.log('\n2. 发送初始化完成通知...');
  sendMessage({
    jsonrpc: '2.0',
    method: 'notifications/initialized'
  });
}, 2000);

setTimeout(() => {
  console.log('\n3. 请求工具列表...');
  sendMessage({
    jsonrpc: '2.0',
    id: messageId++,
    method: 'tools/list'
  });
}, 3000);

setTimeout(() => {
  console.log('\n4. 测试工具调用...');
  sendMessage({
    jsonrpc: '2.0',
    id: messageId++,
    method: 'tools/call',
    params: {
      name: 'hitrip_get_cities',
      arguments: {}
    }
  });
}, 4000);

setTimeout(() => {
  console.log('\n📊 测试总结:');
  console.log('='.repeat(40));
  
  if (responses.length > 0) {
    console.log(`✅ 收到 ${responses.length} 个响应`);
    
    const initResponse = responses.find(r => r.id === 1);
    if (initResponse && !initResponse.error) {
      console.log('✅ 初始化成功');
    }
    
    const toolsResponse = responses.find(r => r.id === 2);
    if (toolsResponse && toolsResponse.result && toolsResponse.result.tools) {
      console.log(`✅ 工具列表获取成功: ${toolsResponse.result.tools.length} 个工具`);
    }
    
    const callResponse = responses.find(r => r.id === 3);
    if (callResponse) {
      if (callResponse.error) {
        console.log('⚠️  工具调用失败 (预期，因为API端点可能不可用)');
      } else {
        console.log('✅ 工具调用成功');
      }
    }
  } else {
    console.log('❌ 没有收到任何响应');
  }
  
  console.log('\n💡 如果看到初始化和工具列表成功，说明MCP服务器配置正确');
  
  server.kill();
  process.exit(0);
}, 6000);
