#!/usr/bin/env node

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

console.log('🧪 测试取消订单工具调用...\n');
console.log('⚠️  注意：这是一个测试调用，不会实际取消真实订单\n');

// 启动 MCP 服务器
const mcpProcess = spawn('node', [join(projectRoot, 'dist/mcp-launcher.js')], {
  env: {
    ...process.env,
    HITRIP_CID: '1086910840',
    HITRIP_SECRET_KEY: '12345678',
    HITRIP_BASE_URL: 'https://api-gw.test.huangbaoche.com/',
    NODE_ENV: 'production',
    LOG_LEVEL: 'debug'
  },
  stdio: ['pipe', 'pipe', 'pipe']
});

let responseBuffer = '';

mcpProcess.stdout.on('data', (data) => {
  responseBuffer += data.toString();
  
  // 检查是否收到完整的 JSON-RPC 响应
  const lines = responseBuffer.split('\n');
  for (const line of lines) {
    if (line.trim() && line.startsWith('{')) {
      try {
        const response = JSON.parse(line);
        if (response.id === 2) { // 只显示取消订单的响应
          console.log('📨 取消订单响应:', JSON.stringify(response, null, 2));
        }
      } catch (e) {
        // 忽略解析错误
      }
    }
  }
});

mcpProcess.stderr.on('data', (data) => {
  console.error('❌ 错误输出:', data.toString());
});

// 等待服务器启动
setTimeout(() => {
  console.log('❌ 发送取消订单请求...');
  
  // 使用一个测试订单号（注意：这可能会失败，因为订单可能已经被取消或不存在）
  const cancelOrderRequest = {
    jsonrpc: '2.0',
    id: 2,
    method: 'tools/call',
    params: {
      name: 'hitrip_cancel_order',
      arguments: {
        orderTravelNo: 'TEST_ORDER_FOR_CANCEL_' + Date.now(), // 使用一个不存在的测试订单号
        cancelReason: '测试取消订单功能',
        cancelType: 1, // 1:用户原因
        cancelRemark: '这是一个API功能测试，不是真实的取消请求',
        actuallyPaidPrice: 0
      }
    }
  };
  
  mcpProcess.stdin.write(JSON.stringify(cancelOrderRequest) + '\n');
  
  // 等待响应后关闭
  setTimeout(() => {
    console.log('\n✅ 测试完成，关闭服务器...');
    mcpProcess.kill();
  }, 5000);
  
}, 1000);

mcpProcess.on('close', (code) => {
  console.log(`\n🔚 MCP 服务器已关闭，退出码: ${code}`);
});

mcpProcess.on('error', (error) => {
  console.error('❌ 启动 MCP 服务器失败:', error);
});
