#!/usr/bin/env node

/**
 * 测试机场查询API的专用脚本
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const serverPath = path.join(__dirname, '../dist/debug-mcp-launcher.js');

console.log('✈️ 机场查询API测试');
console.log('='.repeat(40));

// 测试环境变量
const env = {
  ...process.env,
  HITRIP_CID: process.env.HITRIP_CID || '1086910840',
  HITRIP_SECRET_KEY: process.env.HITRIP_SECRET_KEY || '12345678',
  HITRIP_BASE_URL: process.env.HITRIP_BASE_URL || 'https://api-gw.test.huangbaoche.com/',
  NODE_ENV: 'development',
  LOG_LEVEL: 'debug'
};

console.log('🔧 环境变量检查:');
console.log(`CID: ${env.HITRIP_CID}`);
console.log(`SECRET: ${'*'.repeat(env.HITRIP_SECRET_KEY.length)}`);
console.log(`BASE_URL: ${env.HITRIP_BASE_URL}`);

console.log('\n🚀 启动MCP服务器...');
const server = spawn('node', [serverPath], {
  env,
  stdio: ['pipe', 'pipe', 'pipe']
});

let responses = [];
let messageId = 1;

server.stdout.on('data', (data) => {
  const output = data.toString();
  const lines = output.split('\n').filter(line => line.trim());
  
  lines.forEach(line => {
    try {
      const response = JSON.parse(line);
      responses.push(response);
      console.log('📥 MCP响应:', JSON.stringify(response, null, 2));
    } catch (error) {
      console.log('📥 非JSON输出:', line);
    }
  });
});

server.stderr.on('data', (data) => {
  const output = data.toString();
  console.log('🔍 服务器日志:', output.trim());
});

server.on('error', (error) => {
  console.error('❌ 服务器错误:', error.message);
  process.exit(1);
});

function sendMessage(message) {
  const jsonMessage = JSON.stringify(message);
  console.log('📤 发送消息:', jsonMessage);
  server.stdin.write(jsonMessage + '\n');
}

// 测试序列
setTimeout(() => {
  console.log('\n1️⃣ 初始化MCP连接...');
  sendMessage({
    jsonrpc: '2.0',
    id: messageId++,
    method: 'initialize',
    params: {
      protocolVersion: '2024-11-05',
      capabilities: { tools: {} },
      clientInfo: { name: 'airports-test', version: '1.0.0' }
    }
  });
}, 1000);

setTimeout(() => {
  console.log('\n2️⃣ 发送初始化完成通知...');
  sendMessage({
    jsonrpc: '2.0',
    method: 'notifications/initialized'
  });
}, 2000);

setTimeout(() => {
  console.log('\n3️⃣ 测试机场查询 - 无参数...');
  sendMessage({
    jsonrpc: '2.0',
    id: messageId++,
    method: 'tools/call',
    params: {
      name: 'hitrip_get_airports',
      arguments: {}
    }
  });
}, 3000);

setTimeout(() => {
  console.log('\n4️⃣ 测试机场查询 - 搜索"北京"...');
  sendMessage({
    jsonrpc: '2.0',
    id: messageId++,
    method: 'tools/call',
    params: {
      name: 'hitrip_get_airports',
      arguments: {
        keyword: '北京'
      }
    }
  });
}, 4000);

setTimeout(() => {
  console.log('\n5️⃣ 测试机场查询 - 搜索"Beijing"...');
  sendMessage({
    jsonrpc: '2.0',
    id: messageId++,
    method: 'tools/call',
    params: {
      name: 'hitrip_get_airports',
      arguments: {
        keyword: 'Beijing'
      }
    }
  });
}, 5000);

setTimeout(() => {
  console.log('\n6️⃣ 测试机场查询 - 搜索"首都"...');
  sendMessage({
    jsonrpc: '2.0',
    id: messageId++,
    method: 'tools/call',
    params: {
      name: 'hitrip_get_airports',
      arguments: {
        keyword: '首都'
      }
    }
  });
}, 6000);

setTimeout(() => {
  console.log('\n📊 机场查询API测试总结:');
  console.log('='.repeat(50));
  
  if (responses.length > 0) {
    console.log(`✅ 收到 ${responses.length} 个响应`);
    
    responses.forEach((response, index) => {
      console.log(`\n响应 ${index + 1} (ID: ${response.id}):`);
      if (response.error) {
        console.log(`❌ 错误: ${response.error.message}`);
      } else if (response.result && response.result.content) {
        const content = response.result.content[0];
        if (content && content.text) {
          const text = content.text;
          try {
            const data = JSON.parse(text);
            if (Array.isArray(data)) {
              console.log(`✅ 成功返回 ${data.length} 个机场`);
              if (data.length > 0) {
                console.log(`   示例机场: ${data[0].airportName || data[0].name || '未知'}`);
                if (data.length <= 5) {
                  data.forEach((airport, i) => {
                    console.log(`   ${i+1}. ${airport.airportName} (${airport.code}) - 城市ID: ${airport.cityId}`);
                  });
                }
              }
            } else {
              console.log(`✅ 返回数据: ${typeof data}`);
            }
          } catch (e) {
            if (text.includes('Error:')) {
              console.log(`❌ 工具执行错误: ${text.substring(0, 100)}...`);
            } else {
              console.log(`✅ 工具执行成功: ${text.length} 字符的响应`);
            }
          }
        }
      }
    });
  } else {
    console.log('❌ 没有收到任何响应');
  }
  
  console.log('\n💡 测试结论:');
  console.log('1. 检查keyword参数是否正确传递');
  console.log('2. 验证API端点是否正确');
  console.log('3. 确认返回的机场数据格式');
  console.log('4. 测试不同关键词的搜索效果');
  
  server.kill();
  process.exit(0);
}, 8000);
