#!/usr/bin/env node

import { HitripApiClient } from '../dist/api/client.js';
import { createHitripTools } from '../dist/tools/index.js';

// 模拟配置
const mockConfig = {
  cid: '1086910840',
  secretKey: '12345678',
  baseUrl: 'https://api-gw.test.huangbaoche.com/',
  cache: { enabled: false }
};

console.log('🔍 调试 Charter Quote 工具...\n');

try {
  // 创建 API 客户端
  const apiClient = new HitripApiClient(mockConfig);
  
  // 创建工具列表
  const tools = createHitripTools(apiClient);
  
  // 查找 charter_quote 工具
  const charterQuoteTool = tools.find(tool => tool.name === 'hitrip_charter_quote');
  
  if (charterQuoteTool) {
    console.log('✅ 找到 hitrip_charter_quote 工具!');
    console.log('📋 工具定义:');
    console.log(JSON.stringify(charterQuoteTool, null, 2));
    
    // 检查必需参数
    const required = charterQuoteTool.inputSchema?.required || [];
    console.log(`\n🔧 必需参数: ${required.join(', ')}`);
    
    // 检查所有参数
    const properties = charterQuoteTool.inputSchema?.properties || {};
    console.log('\n📝 所有参数:');
    Object.keys(properties).forEach(key => {
      console.log(`  - ${key}: ${properties[key].description}`);
    });
    
  } else {
    console.log('❌ 未找到 hitrip_charter_quote 工具!');
    console.log('\n📋 可用工具列表:');
    tools.forEach(tool => {
      console.log(`  - ${tool.name}: ${tool.description}`);
    });
  }
  
  // 统计工具数量
  console.log(`\n📊 总工具数量: ${tools.length}`);
  
  // 查找所有包含 "quote" 的工具
  const quoteTools = tools.filter(tool => tool.name.includes('quote'));
  console.log(`\n💰 报价相关工具 (${quoteTools.length}):`)
  quoteTools.forEach(tool => {
    console.log(`  - ${tool.name}: ${tool.description}`);
  });
  
} catch (error) {
  console.error('❌ 调试过程中出错:', error.message);
  console.error(error.stack);
}
