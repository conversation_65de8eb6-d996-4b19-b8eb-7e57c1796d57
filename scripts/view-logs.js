#!/usr/bin/env node

/**
 * 日志查看工具
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const logDir = path.join(path.dirname(__dirname), 'logs');

console.log('📋 Hitrip MCP Server 日志查看器');
console.log('='.repeat(50));

// 检查日志目录
if (!fs.existsSync(logDir)) {
  console.log('❌ 日志目录不存在:', logDir);
  console.log('请先运行调试版启动器生成日志');
  process.exit(1);
}

// 获取所有日志文件
const logFiles = fs.readdirSync(logDir)
  .filter(file => file.endsWith('.log'))
  .sort()
  .reverse(); // 最新的在前面

if (logFiles.length === 0) {
  console.log('❌ 没有找到日志文件');
  process.exit(1);
}

console.log(`📁 找到 ${logFiles.length} 个日志文件:`);
logFiles.forEach((file, index) => {
  const filePath = path.join(logDir, file);
  const stats = fs.statSync(filePath);
  console.log(`${index + 1}. ${file} (${stats.size} bytes, ${stats.mtime.toLocaleString()})`);
});

// 读取最新的日志文件
const latestLogFile = path.join(logDir, logFiles[0]);
console.log(`\n📖 读取最新日志文件: ${logFiles[0]}`);
console.log('='.repeat(50));

try {
  const logContent = fs.readFileSync(latestLogFile, 'utf8');
  const lines = logContent.split('\n').filter(line => line.trim());
  
  console.log(`📊 总共 ${lines.length} 条日志记录\n`);
  
  // 按级别统计
  const stats = { ERROR: 0, WARN: 0, INFO: 0, DEBUG: 0 };
  const errors = [];
  const mcpMessages = [];
  
  lines.forEach((line, index) => {
    try {
      const entry = JSON.parse(line);
      stats[entry.level] = (stats[entry.level] || 0) + 1;
      
      // 收集错误
      if (entry.level === 'ERROR') {
        errors.push({ index: index + 1, entry });
      }
      
      // 收集MCP消息
      if (entry.message.includes('MCP message') || entry.message.includes('MCP response')) {
        mcpMessages.push({ index: index + 1, entry });
      }
      
    } catch (error) {
      console.log(`⚠️  无法解析第 ${index + 1} 行日志`);
    }
  });
  
  // 显示统计信息
  console.log('📊 日志级别统计:');
  Object.entries(stats).forEach(([level, count]) => {
    if (count > 0) {
      const icon = level === 'ERROR' ? '❌' : level === 'WARN' ? '⚠️' : level === 'INFO' ? 'ℹ️' : '🔍';
      console.log(`${icon} ${level}: ${count} 条`);
    }
  });
  
  // 显示错误详情
  if (errors.length > 0) {
    console.log(`\n❌ 错误详情 (${errors.length} 个):`);
    console.log('='.repeat(30));
    errors.forEach(({ index, entry }) => {
      console.log(`\n[${index}] ${entry.timestamp}`);
      console.log(`消息: ${entry.message}`);
      if (entry.data) {
        console.log(`数据: ${JSON.stringify(entry.data, null, 2)}`);
      }
    });
  }
  
  // 显示MCP消息流
  if (mcpMessages.length > 0) {
    console.log(`\n📡 MCP 消息流 (${mcpMessages.length} 个):`);
    console.log('='.repeat(30));
    mcpMessages.slice(-10).forEach(({ index, entry }) => { // 只显示最后10个
      const time = new Date(entry.timestamp).toLocaleTimeString();
      console.log(`\n[${index}] ${time} - ${entry.message}`);
      if (entry.data) {
        if (entry.data.method) {
          console.log(`  方法: ${entry.data.method}`);
        }
        if (entry.data.messageId) {
          console.log(`  消息ID: ${entry.data.messageId}`);
        }
        if (entry.data.hasError) {
          console.log(`  ❌ 包含错误`);
        }
        if (entry.data.hasResult) {
          console.log(`  ✅ 包含结果`);
        }
      }
    });
  }
  
  // 显示最近的日志条目
  console.log(`\n📝 最近的日志条目 (最后10条):`);
  console.log('='.repeat(40));
  lines.slice(-10).forEach((line, index) => {
    try {
      const entry = JSON.parse(line);
      const time = new Date(entry.timestamp).toLocaleTimeString();
      const icon = entry.level === 'ERROR' ? '❌' : entry.level === 'WARN' ? '⚠️' : entry.level === 'INFO' ? 'ℹ️' : '🔍';
      console.log(`${icon} [${time}] ${entry.message}`);
      if (entry.data && Object.keys(entry.data).length > 0) {
        console.log(`   ${JSON.stringify(entry.data)}`);
      }
    } catch (error) {
      console.log(`⚠️  无法解析日志: ${line.substring(0, 100)}...`);
    }
  });
  
  console.log(`\n💡 提示:`);
  console.log(`- 完整日志文件: ${latestLogFile}`);
  console.log(`- 使用 'tail -f ${latestLogFile}' 实时查看日志`);
  console.log(`- 使用 'grep ERROR ${latestLogFile}' 只查看错误`);
  
} catch (error) {
  console.error('❌ 读取日志文件失败:', error.message);
  process.exit(1);
}
