#!/usr/bin/env node

/**
 * Cherry Studio MCP 调试工具
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const serverPath = path.join(__dirname, '../dist/debug-mcp-launcher.js');

console.log('🍒 Cherry Studio MCP 调试');
console.log('='.repeat(40));

// Cherry Studio 环境变量
const env = {
  ...process.env,
  HITRIP_CID: '1086910840',
  HITRIP_SECRET_KEY: '12345678',
  HITRIP_BASE_URL: 'https://api-gw.huangbaoche.com/',
  NODE_ENV: 'development',
  LOG_LEVEL: 'debug'
};

console.log('🔧 环境变量检查:');
console.log(`CID: ${env.HITRIP_CID}`);
console.log(`SECRET: ${'*'.repeat(env.HITRIP_SECRET_KEY.length)}`);
console.log(`BASE_URL: ${env.HITRIP_BASE_URL}`);

console.log('\n🚀 启动MCP服务器...');
const server = spawn('node', [serverPath], {
  env,
  stdio: ['pipe', 'pipe', 'pipe']
});

let responses = [];
let messageId = 1;

server.stdout.on('data', (data) => {
  const output = data.toString();
  const lines = output.split('\n').filter(line => line.trim());
  
  lines.forEach(line => {
    try {
      const response = JSON.parse(line);
      responses.push(response);
      console.log('📥 MCP响应:', JSON.stringify(response, null, 2));
    } catch (error) {
      console.log('📥 非JSON输出:', line);
    }
  });
});

server.stderr.on('data', (data) => {
  const output = data.toString();
  console.log('🔍 服务器日志:', output.trim());
});

server.on('error', (error) => {
  console.error('❌ 服务器错误:', error.message);
  process.exit(1);
});

function sendMessage(message) {
  const jsonMessage = JSON.stringify(message);
  console.log('📤 发送消息:', jsonMessage);
  server.stdin.write(jsonMessage + '\n');
}

// 模拟Cherry Studio的MCP交互序列
setTimeout(() => {
  console.log('\n1️⃣ 初始化MCP连接...');
  sendMessage({
    jsonrpc: '2.0',
    id: messageId++,
    method: 'initialize',
    params: {
      protocolVersion: '2024-11-05',
      capabilities: {
        tools: {}
      },
      clientInfo: {
        name: 'cherry-studio',
        version: '1.0.0'
      }
    }
  });
}, 1000);

setTimeout(() => {
  console.log('\n2️⃣ 发送初始化完成通知...');
  sendMessage({
    jsonrpc: '2.0',
    method: 'notifications/initialized'
  });
}, 2000);

setTimeout(() => {
  console.log('\n3️⃣ 获取工具列表...');
  sendMessage({
    jsonrpc: '2.0',
    id: messageId++,
    method: 'tools/list'
  });
}, 3000);

setTimeout(() => {
  console.log('\n4️⃣ 测试Token获取 (这里可能会失败)...');
  sendMessage({
    jsonrpc: '2.0',
    id: messageId++,
    method: 'tools/call',
    params: {
      name: 'hitrip_get_token',
      arguments: {}
    }
  });
}, 4000);

setTimeout(() => {
  console.log('\n5️⃣ 测试机场查询 (模拟Cherry Studio的调用)...');
  sendMessage({
    jsonrpc: '2.0',
    id: messageId++,
    method: 'tools/call',
    params: {
      name: 'hitrip_get_airports',
      arguments: {
        keyword: 'Beijing'
      }
    }
  });
}, 5000);

setTimeout(() => {
  console.log('\n📊 Cherry Studio MCP 测试总结:');
  console.log('='.repeat(50));
  
  if (responses.length > 0) {
    console.log(`✅ 收到 ${responses.length} 个响应`);
    
    responses.forEach((response, index) => {
      console.log(`\n响应 ${index + 1} (ID: ${response.id}):`);
      if (response.error) {
        console.log(`❌ 错误: ${response.error.message}`);
        if (response.error.code) {
          console.log(`   错误码: ${response.error.code}`);
        }
        if (response.error.data) {
          console.log(`   详情: ${JSON.stringify(response.error.data)}`);
        }
      } else if (response.result) {
        console.log(`✅ 成功`);
        if (response.result.tools) {
          console.log(`   工具数量: ${response.result.tools.length}`);
        }
        if (response.result.content) {
          const content = response.result.content[0];
          if (content && content.text) {
            const text = content.text;
            if (text.includes('Error:')) {
              console.log(`   ❌ 工具执行错误: ${text.substring(0, 100)}...`);
            } else {
              console.log(`   ✅ 工具执行成功: ${text.length} 字符的响应`);
            }
          }
        }
      }
    });
  } else {
    console.log('❌ 没有收到任何响应');
  }
  
  console.log('\n💡 Cherry Studio 配置建议:');
  console.log('1. 确保MCP服务器路径正确');
  console.log('2. 检查环境变量是否正确设置');
  console.log('3. 查看Cherry Studio的MCP日志');
  console.log('4. 如果Token获取失败，检查API凭据');
  
  server.kill();
  process.exit(0);
}, 7000);
