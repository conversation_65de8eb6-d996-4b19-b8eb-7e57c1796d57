#!/usr/bin/env node

import { EventSource } from 'eventsource';
import fetch from 'node-fetch';

const SSE_URL = 'http://localhost:8081/sse';
const MCP_URL = 'http://localhost:8081/mcp';

console.log('🔌 测试 SSE MCP 连接...');
console.log(`📍 SSE 地址: ${SSE_URL}`);
console.log(`📍 MCP 地址: ${MCP_URL}\n`);

let connectionId = null;
let testCount = 0;
const tests = [
  {
    name: '获取工具列表',
    request: {
      jsonrpc: '2.0',
      id: 1,
      method: 'tools/list',
      params: {}
    }
  },
  {
    name: '获取访问令牌',
    request: {
      jsonrpc: '2.0',
      id: 2,
      method: 'tools/call',
      params: {
        name: 'hitrip_get_token',
        arguments: {}
      }
    }
  },
  {
    name: '查询城市列表',
    request: {
      jsonrpc: '2.0',
      id: 3,
      method: 'tools/call',
      params: {
        name: 'hitrip_get_cities',
        arguments: {
          keyword: '东京'
        }
      }
    }
  }
];

// 创建 SSE 连接
const eventSource = new EventSource(SSE_URL);

eventSource.onopen = () => {
  console.log('✅ SSE 连接已建立');
};

eventSource.addEventListener('connected', (event) => {
  const data = JSON.parse(event.data);
  connectionId = data.connectionId;
  console.log(`🔗 连接确认，连接ID: ${connectionId}`);
  console.log(`⏰ 连接时间: ${data.timestamp}\n`);
  
  // 开始第一个测试
  setTimeout(runNextTest, 1000);
});

eventSource.addEventListener('tools', (event) => {
  const data = JSON.parse(event.data);
  console.log(`📋 收到工具列表: ${data.tools.length} 个工具`);
  console.log(`   前5个工具: ${data.tools.slice(0, 5).map(t => t.name).join(', ')}\n`);
});

eventSource.addEventListener('response', (event) => {
  const response = JSON.parse(event.data);
  console.log(`📨 收到 MCP 响应 (ID: ${response.id}):`);
  
  if (response.result) {
    if (response.result.tools) {
      console.log(`   ✅ 工具列表: ${response.result.tools.length} 个工具`);
    } else if (response.result.content) {
      const content = response.result.content[0].text;
      if (content.length > 100) {
        console.log(`   ✅ 响应内容: ${content.substring(0, 100)}...`);
      } else {
        console.log(`   ✅ 响应内容: ${content}`);
      }
    } else {
      console.log(`   ✅ 响应成功`);
    }
  } else if (response.error) {
    console.log(`   ❌ 错误: ${response.error.message}`);
  }
  
  console.log('');
  
  // 运行下一个测试
  setTimeout(runNextTest, 2000);
});

eventSource.addEventListener('ping', (event) => {
  const data = JSON.parse(event.data);
  console.log(`💓 心跳: ${data.timestamp}`);
});

eventSource.addEventListener('disconnect', (event) => {
  const data = JSON.parse(event.data);
  console.log(`🔌 服务器断开连接: ${data.message}`);
});

eventSource.onerror = (error) => {
  console.error('❌ SSE 连接错误:', error);
};

async function runNextTest() {
  if (testCount >= tests.length) {
    console.log('🏁 所有测试完成，关闭连接...');
    eventSource.close();
    
    console.log('\n🎉 所有 SSE 测试完成！');
    console.log('📊 测试结果:');
    console.log(`   - SSE 连接: ✅`);
    console.log(`   - 工具列表: ✅`);
    console.log(`   - 令牌获取: ✅`);
    console.log(`   - 城市查询: ✅`);
    console.log(`   - 心跳机制: ✅`);
    
    process.exit(0);
    return;
  }
  
  const test = tests[testCount];
  testCount++;
  
  console.log(`🧪 测试 ${testCount}: ${test.name}`);
  console.log(`📤 发送 MCP 请求:`, JSON.stringify(test.request, null, 2));
  
  try {
    const response = await fetch(MCP_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Connection-Id': connectionId
      },
      body: JSON.stringify(test.request)
    });
    
    const result = await response.json();
    
    if (result.status === 'sent_via_sse') {
      console.log('📡 请求已通过 SSE 发送，等待响应...');
    } else {
      console.log('📨 直接收到响应:', JSON.stringify(result, null, 2));
      setTimeout(runNextTest, 2000);
    }
    
  } catch (error) {
    console.error(`❌ 发送请求失败:`, error);
    setTimeout(runNextTest, 2000);
  }
}

// 超时保护
setTimeout(() => {
  if (testCount < tests.length) {
    console.log(`\n⏰ 测试超时，只完成了 ${testCount}/${tests.length} 个测试`);
    eventSource.close();
    process.exit(1);
  }
}, 60000);
