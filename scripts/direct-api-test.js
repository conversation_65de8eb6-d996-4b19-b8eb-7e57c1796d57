#!/usr/bin/env node

/**
 * 直接调用服务端接口测试参数构造
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const serverPath = path.join(__dirname, '../dist/debug-mcp-launcher.js');

console.log('🔧 直接API调用测试 - 参数构造调试');
console.log('='.repeat(50));

const env = {
  ...process.env,
  HITRIP_CID: '1086910840',
  HITRIP_SECRET_KEY: '12345678',
  HITRIP_BASE_URL: 'https://api-gw.test.huangbaoche.com/',
  NODE_ENV: 'development',
  LOG_LEVEL: 'debug'
};

console.log('🚀 启动MCP服务器进行API调试...');
const server = spawn('node', [serverPath], {
  env,
  stdio: ['pipe', 'pipe', 'pipe']
});

let messageId = 1;
let responses = [];

server.stdout.on('data', (data) => {
  const output = data.toString();
  const lines = output.split('\n').filter(line => line.trim());
  
  lines.forEach(line => {
    try {
      const response = JSON.parse(line);
      responses.push(response);
      
      if (response.result && response.result.content) {
        const content = response.result.content[0];
        if (content && content.text) {
          console.log(`\n📥 工具响应 (ID: ${response.id}):`);
          const text = content.text;
          try {
            const data = JSON.parse(text);
            if (Array.isArray(data)) {
              console.log(`✅ 返回 ${data.length} 个机场`);
              if (data.length > 0 && data.length <= 5) {
                data.forEach((airport, i) => {
                  console.log(`   ${i+1}. ${airport.airportName} (${airport.code}) - 城市ID: ${airport.cityId}`);
                });
              } else if (data.length > 5) {
                console.log('   前5个机场:');
                data.slice(0, 5).forEach((airport, i) => {
                  console.log(`   ${i+1}. ${airport.airportName} (${airport.code}) - 城市ID: ${airport.cityId}`);
                });
              }
            }
          } catch (e) {
            console.log(`📄 响应内容: ${text.substring(0, 200)}...`);
          }
        }
      }
    } catch (error) {
      // 处理日志输出
      if (line.includes('API Request') || line.includes('API Response')) {
        console.log(`🔍 ${line}`);
      }
    }
  });
});

server.stderr.on('data', (data) => {
  const output = data.toString();
  if (output.includes('API Request') || output.includes('API Response') || output.includes('ERROR')) {
    console.log(`🔍 服务器日志: ${output.trim()}`);
  }
});

function sendMessage(message) {
  const jsonMessage = JSON.stringify(message);
  console.log(`\n📤 发送: ${message.method || 'response'} ${message.params?.name || ''}`);
  server.stdin.write(jsonMessage + '\n');
}

// 测试序列
setTimeout(() => {
  console.log('\n1️⃣ 初始化MCP连接...');
  sendMessage({
    jsonrpc: '2.0',
    id: messageId++,
    method: 'initialize',
    params: {
      protocolVersion: '2024-11-05',
      capabilities: { tools: {} },
      clientInfo: { name: 'api-debug', version: '1.0.0' }
    }
  });
}, 1000);

setTimeout(() => {
  sendMessage({
    jsonrpc: '2.0',
    method: 'notifications/initialized'
  });
}, 2000);

// 测试不同的参数组合
const testCases = [
  { name: '无参数', args: {} },
  { name: '搜索"北京"', args: { keyword: '北京' } },
  { name: '搜索"Beijing"', args: { keyword: 'Beijing' } },
  { name: '搜索"首都"', args: { keyword: '首都' } },
  { name: '搜索"成田"', args: { keyword: '成田' } },
  { name: '搜索"NRT"', args: { keyword: 'NRT' } },
  { name: '搜索"PEK"', args: { keyword: 'PEK' } },
  { name: '搜索"Capital"', args: { keyword: 'Capital' } },
];

testCases.forEach((testCase, index) => {
  setTimeout(() => {
    console.log(`\n${index + 3}️⃣ 测试机场查询 - ${testCase.name}...`);
    sendMessage({
      jsonrpc: '2.0',
      id: messageId++,
      method: 'tools/call',
      params: {
        name: 'hitrip_get_airports',
        arguments: testCase.args
      }
    });
  }, 3000 + (index * 1000));
});

// 总结测试结果
setTimeout(() => {
  console.log('\n📊 API参数构造测试总结:');
  console.log('='.repeat(50));
  
  console.log('\n🔍 关键发现:');
  console.log('1. 检查所有测试用例的返回结果');
  console.log('2. 对比无参数查询和有参数查询的差异');
  console.log('3. 验证参数是否正确传递到API');
  console.log('4. 分析API搜索逻辑是否正常工作');
  
  console.log('\n💡 下一步调试方向:');
  console.log('- 如果无参数返回全部机场，有参数返回空，说明搜索逻辑有问题');
  console.log('- 如果所有查询都返回空，说明API调用本身有问题');
  console.log('- 如果某些关键词有结果，某些没有，说明搜索匹配规则需要调整');
  
  server.kill();
  process.exit(0);
}, 3000 + (testCases.length * 1000) + 2000);
