#!/bin/bash

echo "🍒 Cherry Studio SSE 模式配置助手"
echo "=================================="
echo ""

# 检查 SSE 服务器状态
echo "1. 🔍 检查 SSE 服务器状态..."
if curl -s http://localhost:8081/health > /dev/null; then
    echo "   ✅ SSE 服务器正在运行"
    
    # 获取服务器信息
    server_info=$(curl -s http://localhost:8081/health | jq -r '.service')
    active_connections=$(curl -s http://localhost:8081/health | jq -r '.activeConnections')
    echo "   📋 服务: $server_info"
    echo "   🔗 活跃连接: $active_connections"
else
    echo "   ❌ SSE 服务器未运行"
    echo "   💡 请先启动服务器: ./scripts/start-sse-server.sh"
    exit 1
fi
echo ""

# 测试连接
echo "2. 🧪 测试连接..."
if node scripts/test-cherry-studio-connection.js > /dev/null 2>&1; then
    echo "   ✅ 连接测试通过"
else
    echo "   ❌ 连接测试失败"
    echo "   💡 请检查服务器状态和网络连接"
    exit 1
fi
echo ""

# 生成配置文件
echo "3. 📝 生成 Cherry Studio 配置..."

# 检查用户偏好
echo "请选择配置类型:"
echo "1) 简单配置 (推荐)"
echo "2) 完整配置 (高级选项)"
read -p "请输入选择 (1-2): " config_type

case $config_type in
    1)
        config_file="config/cherry-studio-sse-simple.json"
        echo "   📄 使用简单配置: $config_file"
        ;;
    2)
        config_file="config/cherry-studio-sse.json"
        echo "   📄 使用完整配置: $config_file"
        ;;
    *)
        config_file="config/cherry-studio-sse-simple.json"
        echo "   📄 默认使用简单配置: $config_file"
        ;;
esac

# 复制配置文件到用户目录（可选）
echo ""
echo "4. 📋 配置文件位置:"
echo "   源文件: $(pwd)/$config_file"
echo ""

# 显示配置内容
echo "5. 📄 配置文件内容:"
echo "---"
cat $config_file
echo "---"
echo ""

# 提供配置指导
echo "6. 🔧 Cherry Studio 配置步骤:"
echo ""
echo "方法一: 使用配置文件"
echo "1. 复制上述配置内容"
echo "2. 在 Cherry Studio 中导入配置文件"
echo "3. 或者将配置保存到 Cherry Studio 配置目录"
echo ""
echo "方法二: 手动配置"
echo "1. 打开 Cherry Studio"
echo "2. 进入设置 → MCP 服务器"
echo "3. 添加新服务器，填入以下信息:"
echo "   - 名称: Hitrip MCP Server"
echo "   - 类型: SSE"
echo "   - SSE URL: http://localhost:8081/sse"
echo "   - MCP URL: http://localhost:8081/mcp"
echo "   - 超时: 30000"
echo "   - 重连: 启用"
echo ""

# 测试建议
echo "7. 🧪 测试建议:"
echo "配置完成后，在 Cherry Studio 中测试以下功能:"
echo ""
echo "基础测试:"
echo '- "请帮我查询东京的城市信息"'
echo ""
echo "接机报价测试:"
echo '- "请帮我查询从东京成田机场到东京站的接机报价"'
echo ""
echo "送机报价测试:"
echo '- "请帮我查询从东京站到东京成田机场的送机报价"'
echo ""

# 故障排除
echo "8. 🔧 故障排除:"
echo "如果遇到问题:"
echo "1. 检查 SSE 服务器状态: curl http://localhost:8081/health"
echo "2. 查看服务器日志"
echo "3. 运行连接测试: node scripts/test-cherry-studio-connection.js"
echo "4. 检查防火墙和网络设置"
echo ""

echo "🎉 配置助手完成！"
echo "📚 详细文档: docs/CHERRY_STUDIO_SSE_CONFIG.md"
