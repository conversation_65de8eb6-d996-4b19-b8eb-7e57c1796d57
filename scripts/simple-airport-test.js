#!/usr/bin/env node

/**
 * 简单的机场API测试
 */

import axios from 'axios';
import crypto from 'crypto';

const config = {
  cid: '1086910840',
  secretKey: '12345678',
  baseUrl: 'https://api-gw.test.huangbaoche.com/',
};

function generateTimestamp() {
  return new Date().toISOString().replace('T', ' ').substring(0, 19);
}

function generateSignature(params) {
  const sortedKeys = Object.keys(params).sort();
  const queryString = sortedKeys
    .map(key => `${key}=${params[key]}`)
    .join('&');
  const signString = queryString + config.secretKey;
  return crypto.createHash('md5').update(signString).digest('hex').toUpperCase();
}

function createSignedParams(params) {
  const timestamp = generateTimestamp();
  const signedParams = {
    ...params,
    cid: config.cid,
    timestamp,
  };
  signedParams.sign = generateSignature(signedParams);
  return signedParams;
}

async function getToken() {
  try {
    const response = await axios.get(
      `${config.baseUrl}/otaorder-api/access/getAccessToken?cid=${config.cid}&secretKey=${config.secretKey}`,
      {
        proxy: false, // 禁用代理
        timeout: 10000
      }
    );
    
    if (response.data.success) {
      return response.data.data; // 直接返回token字符串
    } else {
      throw new Error(`Get token failed: ${response.data.message}`);
    }
  } catch (error) {
    throw new Error(`Get token error: ${error.message}`);
  }
}

async function testAirportAPI() {
  console.log('🔍 简单机场API测试');
  console.log('='.repeat(40));

  try {
    // 获取token
    console.log('1️⃣ 获取访问令牌...');
    const token = await getToken();
    console.log('✅ 令牌获取成功');

    // 测试1: 无参数查询
    console.log('\n2️⃣ 测试无参数查询...');
    const params1 = createSignedParams({});
    console.log('请求参数:', JSON.stringify(params1, null, 2));
    
    const response1 = await axios.post(
      `${config.baseUrl}/otaorder-api/common/v1.0/airport/listByNameAndSpell?token=${token}`,
      params1
    );
    
    console.log('响应状态:', response1.status);
    console.log('响应成功:', response1.data.success);
    console.log('机场数量:', response1.data.data?.length || 0);
    
    if (response1.data.data && response1.data.data.length > 0) {
      console.log('前3个机场:');
      response1.data.data.slice(0, 3).forEach(airport => {
        console.log(`- ${airport.airportName} (${airport.code}) - 城市ID: ${airport.cityId}`);
      });
    }

    // 测试2: 搜索"北京"
    console.log('\n3️⃣ 测试搜索"北京"...');
    const params2 = createSignedParams({ keyword: '北京' });
    console.log('请求参数:', JSON.stringify(params2, null, 2));
    
    const response2 = await axios.post(
      `${config.baseUrl}/otaorder-api/common/v1.0/airport/listByNameAndSpell?token=${token}`,
      params2
    );
    
    console.log('响应状态:', response2.status);
    console.log('响应成功:', response2.data.success);
    console.log('机场数量:', response2.data.data?.length || 0);
    
    if (response2.data.data && response2.data.data.length > 0) {
      console.log('找到的机场:');
      response2.data.data.forEach(airport => {
        console.log(`- ${airport.airportName} (${airport.code}) - 城市ID: ${airport.cityId}`);
      });
    } else {
      console.log('❌ 没有找到包含"北京"的机场');
    }

    // 测试3: 搜索"成田"（API文档示例）
    console.log('\n4️⃣ 测试搜索"成田"...');
    const params3 = createSignedParams({ keyword: '成田' });
    console.log('请求参数:', JSON.stringify(params3, null, 2));
    
    const response3 = await axios.post(
      `${config.baseUrl}/otaorder-api/common/v1.0/airport/listByNameAndSpell?token=${token}`,
      params3
    );
    
    console.log('响应状态:', response3.status);
    console.log('响应成功:', response3.data.success);
    console.log('机场数量:', response3.data.data?.length || 0);
    
    if (response3.data.data && response3.data.data.length > 0) {
      console.log('找到的机场:');
      response3.data.data.forEach(airport => {
        console.log(`- ${airport.airportName} (${airport.code}) - 城市ID: ${airport.cityId}`);
      });
    } else {
      console.log('❌ 没有找到包含"成田"的机场');
    }

    // 分析结果
    console.log('\n📊 测试结果分析:');
    const allAirports = response1.data.data || [];
    const beijingKeywords = ['北京', 'Beijing', '首都', 'Capital'];
    const beijingAirports = allAirports.filter(airport => 
      beijingKeywords.some(keyword => 
        airport.airportName.includes(keyword) || 
        airport.code === 'PEK' || 
        airport.code === 'PKX'
      )
    );
    
    console.log(`🔍 在全部 ${allAirports.length} 个机场中找到 ${beijingAirports.length} 个北京相关机场:`);
    beijingAirports.forEach(airport => {
      console.log(`- ${airport.airportName} (${airport.code}) - 城市ID: ${airport.cityId}`);
    });

    if (beijingAirports.length > 0 && (response2.data.data?.length || 0) === 0) {
      console.log('\n⚠️  问题分析:');
      console.log('- 全部机场列表中存在北京相关机场');
      console.log('- 但搜索"北京"时返回空结果');
      console.log('- 这说明API的搜索功能可能有问题');
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

testAirportAPI().then(() => {
  console.log('\n✅ 测试完成');
}).catch(error => {
  console.error('❌ 测试失败:', error);
});
