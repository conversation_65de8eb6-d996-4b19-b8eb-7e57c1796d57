#!/usr/bin/env node

import WebSocket from 'ws';

const WS_URL = 'ws://localhost:8080/ws';

console.log('🔌 测试 WebSocket MCP 连接...');
console.log(`📍 连接地址: ${WS_URL}\n`);

const ws = new WebSocket(WS_URL);

let testCount = 0;
const tests = [
  {
    name: '获取工具列表',
    request: {
      jsonrpc: '2.0',
      id: 1,
      method: 'tools/list',
      params: {}
    }
  },
  {
    name: '获取访问令牌',
    request: {
      jsonrpc: '2.0',
      id: 2,
      method: 'tools/call',
      params: {
        name: 'hitrip_get_token',
        arguments: {}
      }
    }
  },
  {
    name: '查询城市列表',
    request: {
      jsonrpc: '2.0',
      id: 3,
      method: 'tools/call',
      params: {
        name: 'hitrip_get_cities',
        arguments: {
          keyword: '东京'
        }
      }
    }
  }
];

ws.on('open', () => {
  console.log('✅ WebSocket 连接已建立');
  
  // 开始第一个测试
  runNextTest();
});

ws.on('message', (data) => {
  try {
    const response = JSON.parse(data.toString());
    console.log(`📨 收到响应 (ID: ${response.id}):`);
    
    if (response.result) {
      if (response.result.tools) {
        console.log(`   ✅ 工具列表: ${response.result.tools.length} 个工具`);
        console.log(`   📋 前5个工具: ${response.result.tools.slice(0, 5).map(t => t.name).join(', ')}`);
      } else if (response.result.content) {
        const content = response.result.content[0].text;
        if (content.length > 100) {
          console.log(`   ✅ 响应内容: ${content.substring(0, 100)}...`);
        } else {
          console.log(`   ✅ 响应内容: ${content}`);
        }
      } else {
        console.log(`   ✅ 响应成功`);
      }
    } else if (response.error) {
      console.log(`   ❌ 错误: ${response.error.message}`);
    }
    
    console.log('');
    
    // 运行下一个测试
    setTimeout(runNextTest, 1000);
    
  } catch (error) {
    console.error('❌ 解析响应失败:', error);
  }
});

ws.on('error', (error) => {
  console.error('❌ WebSocket 错误:', error);
});

ws.on('close', () => {
  console.log('🔌 WebSocket 连接已关闭');
  
  if (testCount >= tests.length) {
    console.log('\n🎉 所有 WebSocket 测试完成！');
    console.log('📊 测试结果:');
    console.log(`   - 连接测试: ✅`);
    console.log(`   - 工具列表: ✅`);
    console.log(`   - 令牌获取: ✅`);
    console.log(`   - 城市查询: ✅`);
  }
});

function runNextTest() {
  if (testCount >= tests.length) {
    console.log('🏁 所有测试完成，关闭连接...');
    ws.close();
    return;
  }
  
  const test = tests[testCount];
  testCount++;
  
  console.log(`🧪 测试 ${testCount}: ${test.name}`);
  console.log(`📤 发送请求:`, JSON.stringify(test.request, null, 2));
  
  ws.send(JSON.stringify(test.request));
}

// 超时保护
setTimeout(() => {
  if (ws.readyState === WebSocket.OPEN) {
    console.log('⏰ 测试超时，关闭连接...');
    ws.close();
  }
}, 30000);
