#!/usr/bin/env node

import { EventSource } from 'eventsource';
import fetch from 'node-fetch';

console.log('🔍 Cursor MCP 连接测试');
console.log('====================\n');

const SSE_URL = 'http://localhost:8081/sse';
const MCP_URL = 'http://localhost:8081/mcp';

async function testCursorMCP() {
  console.log('1. 测试 SSE 连接...');
  
  try {
    // 测试 SSE 连接
    const sseTest = await new Promise((resolve, reject) => {
      const eventSource = new EventSource(SSE_URL);
      let connected = false;
      let toolsReceived = false;
      
      const timeout = setTimeout(() => {
        eventSource.close();
        if (!connected) {
          reject(new Error('SSE 连接超时'));
        } else if (!toolsReceived) {
          reject(new Error('未收到工具列表'));
        } else {
          resolve('SSE 连接成功');
        }
      }, 10000);
      
      eventSource.onopen = () => {
        console.log('   ✅ SSE 连接已建立');
        connected = true;
      };
      
      eventSource.addEventListener('connected', (event) => {
        const data = JSON.parse(event.data);
        console.log(`   ✅ 连接确认: ${data.connectionId}`);
      });
      
      eventSource.addEventListener('tools', (event) => {
        const data = JSON.parse(event.data);
        console.log(`   ✅ 工具列表: ${data.tools.length} 个工具`);
        toolsReceived = true;
        clearTimeout(timeout);
        eventSource.close();
        resolve('SSE 连接和工具列表接收成功');
      });
      
      eventSource.onerror = (error) => {
        clearTimeout(timeout);
        eventSource.close();
        reject(new Error(`SSE 连接错误: ${error.message || 'Unknown error'}`));
      };
    });
    
    console.log(`   ✅ ${sseTest}\n`);
    
  } catch (error) {
    console.log(`   ❌ SSE 测试失败: ${error.message}\n`);
    return false;
  }
  
  console.log('2. 测试 MCP 协议兼容性...');
  
  try {
    // 测试标准 MCP 方法
    const methods = [
      { name: 'tools/list', params: {} },
      { name: 'mcp:list-tools', params: {} },
      { name: 'initialize', params: { protocolVersion: '2024-11-05', capabilities: {} } },
      { name: 'mcp:initialize', params: { protocolVersion: '2024-11-05', capabilities: {} } }
    ];
    
    for (const method of methods) {
      const response = await fetch(MCP_URL, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          jsonrpc: '2.0',
          id: 1,
          method: method.name,
          params: method.params
        })
      });
      
      const data = await response.json();
      
      if (data.error) {
        console.log(`   ❌ ${method.name}: ${data.error.message}`);
      } else {
        console.log(`   ✅ ${method.name}: 成功`);
        if (data.result.tools) {
          console.log(`      工具数量: ${data.result.tools.length}`);
        }
      }
    }
    
  } catch (error) {
    console.log(`   ❌ MCP 协议测试失败: ${error.message}`);
    return false;
  }
  
  console.log('\n3. 生成 Cursor 配置建议...');
  
  // 生成多种配置选项
  const configs = [
    {
      name: 'SSE 配置',
      config: {
        mcpServers: {
          hitrip: {
            type: 'sse',
            url: SSE_URL
          }
        }
      }
    },
    {
      name: 'HTTP 配置',
      config: {
        mcpServers: {
          hitrip: {
            type: 'http',
            url: MCP_URL
          }
        }
      }
    },
    {
      name: 'Streamable HTTP 配置',
      config: {
        mcpServers: {
          hitrip: {
            type: 'streamable-http',
            url: MCP_URL
          }
        }
      }
    }
  ];
  
  configs.forEach(({ name, config }) => {
    console.log(`\n📋 ${name}:`);
    console.log('```json');
    console.log(JSON.stringify(config, null, 2));
    console.log('```');
  });
  
  console.log('\n🔧 Cursor 配置文件位置:');
  console.log('项目级: .cursor/mcp.json');
  console.log('全局级: ~/.cursor/mcp.json');
  
  console.log('\n📝 调试建议:');
  console.log('1. 检查 Cursor 的 MCP 日志:');
  console.log('   - 打开 Output 面板 (Ctrl+Shift+U)');
  console.log('   - 选择 "MCP Logs" 下拉选项');
  console.log('2. 在 Cursor 设置中检查 MCP 服务器状态:');
  console.log('   - 设置 → Features → Model Context Protocol');
  console.log('3. 确保完全重启 Cursor 后再测试');
  
  return true;
}

// 运行测试
testCursorMCP().catch(error => {
  console.error('❌ 测试失败:', error);
  process.exit(1);
});
