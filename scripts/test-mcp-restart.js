#!/usr/bin/env node

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

console.log('🔄 测试 MCP 服务重启后的状态...\n');

// 启动 MCP 服务器
const mcpProcess = spawn('node', [join(projectRoot, 'dist/mcp-launcher.js')], {
  env: {
    ...process.env,
    HITRIP_CID: '1086910840',
    HITRIP_SECRET_KEY: '12345678',
    HITRIP_BASE_URL: 'https://api-gw.test.huangbaoche.com/',
    NODE_ENV: 'production',
    LOG_LEVEL: 'info'
  },
  stdio: ['pipe', 'pipe', 'pipe']
});

let responseBuffer = '';
let testsPassed = 0;
const totalTests = 3;

mcpProcess.stdout.on('data', (data) => {
  responseBuffer += data.toString();
  
  // 检查是否收到完整的 JSON-RPC 响应
  const lines = responseBuffer.split('\n');
  for (const line of lines) {
    if (line.trim() && line.startsWith('{')) {
      try {
        const response = JSON.parse(line);
        
        if (response.id === 1) {
          console.log('✅ 工具列表获取成功');
          console.log(`   - 工具数量: ${response.result.tools.length}`);
          testsPassed++;
        } else if (response.id === 2) {
          console.log('✅ 获取令牌成功');
          console.log(`   - 令牌长度: ${response.result.content[0].text.length} 字符`);
          testsPassed++;
        } else if (response.id === 3) {
          console.log('✅ 城市列表获取成功');
          const cityData = JSON.parse(response.result.content[0].text);
          console.log(`   - 城市数量: ${cityData.list.length}`);
          testsPassed++;
        }
        
        // 如果所有测试都通过了，关闭服务器
        if (testsPassed >= totalTests) {
          setTimeout(() => {
            console.log('\n🎉 所有测试通过！MCP 服务重启成功');
            mcpProcess.kill();
          }, 1000);
        }
      } catch (e) {
        // 忽略解析错误
      }
    }
  }
});

mcpProcess.stderr.on('data', (data) => {
  const errorMsg = data.toString();
  if (errorMsg.includes('started successfully')) {
    console.log('🚀 MCP 服务器启动成功');
  }
});

// 等待服务器启动
setTimeout(() => {
  console.log('📋 1. 测试工具列表...');
  
  // 1. 获取工具列表
  const listToolsRequest = {
    jsonrpc: '2.0',
    id: 1,
    method: 'tools/list',
    params: {}
  };
  
  mcpProcess.stdin.write(JSON.stringify(listToolsRequest) + '\n');
  
  // 2. 测试获取令牌
  setTimeout(() => {
    console.log('🔑 2. 测试获取令牌...');
    
    const getTokenRequest = {
      jsonrpc: '2.0',
      id: 2,
      method: 'tools/call',
      params: {
        name: 'hitrip_get_token',
        arguments: {}
      }
    };
    
    mcpProcess.stdin.write(JSON.stringify(getTokenRequest) + '\n');
    
    // 3. 测试获取城市列表
    setTimeout(() => {
      console.log('🏙️ 3. 测试获取城市列表...');
      
      const getCitiesRequest = {
        jsonrpc: '2.0',
        id: 3,
        method: 'tools/call',
        params: {
          name: 'hitrip_get_cities',
          arguments: {}
        }
      };
      
      mcpProcess.stdin.write(JSON.stringify(getCitiesRequest) + '\n');
      
    }, 2000);
    
  }, 2000);
  
}, 2000);

// 超时保护
setTimeout(() => {
  if (testsPassed < totalTests) {
    console.log(`\n⚠️ 测试超时，只通过了 ${testsPassed}/${totalTests} 个测试`);
    mcpProcess.kill();
  }
}, 15000);

mcpProcess.on('close', (code) => {
  console.log(`\n🔚 MCP 服务器已关闭，退出码: ${code}`);
  
  if (testsPassed === totalTests) {
    console.log('✅ MCP 服务重启验证完成！所有核心功能正常工作');
  } else {
    console.log(`❌ 部分测试失败，通过率: ${testsPassed}/${totalTests}`);
  }
});

mcpProcess.on('error', (error) => {
  console.error('❌ 启动 MCP 服务器失败:', error);
});
