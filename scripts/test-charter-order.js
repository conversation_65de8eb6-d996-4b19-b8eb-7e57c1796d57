#!/usr/bin/env node

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

console.log('🧪 测试创建包车订单工具调用...\n');

// 启动 MCP 服务器
const mcpProcess = spawn('node', [join(projectRoot, 'dist/mcp-launcher.js')], {
  env: {
    ...process.env,
    HITRIP_CID: '1086910840',
    HITRIP_SECRET_KEY: '12345678',
    HITRIP_BASE_URL: 'https://api-gw.test.huangbaoche.com/',
    NODE_ENV: 'production',
    LOG_LEVEL: 'debug'
  },
  stdio: ['pipe', 'pipe', 'pipe']
});

let responseBuffer = '';

mcpProcess.stdout.on('data', (data) => {
  responseBuffer += data.toString();
  
  // 检查是否收到完整的 JSON-RPC 响应
  const lines = responseBuffer.split('\n');
  for (const line of lines) {
    if (line.trim() && line.startsWith('{')) {
      try {
        const response = JSON.parse(line);
        if (response.id === 2) { // 只显示创建订单的响应
          console.log('📨 创建包车订单响应:', JSON.stringify(response, null, 2));
        }
      } catch (e) {
        // 忽略解析错误
      }
    }
  }
});

mcpProcess.stderr.on('data', (data) => {
  console.error('❌ 错误输出:', data.toString());
});

// 等待服务器启动
setTimeout(() => {
  console.log('🚐 发送创建包车订单请求...');
  
  const createCharterOrderRequest = {
    jsonrpc: '2.0',
    id: 2,
    method: 'tools/call',
    params: {
      name: 'hitrip_create_charter_order',
      arguments: {
        // 订单基础信息
        orderNoThird: 'TEST_CHARTER_' + Date.now(),
        travelStartTimeLocal: '2025-09-10 09:00:00',
        travelEndTimeLocal: '2025-09-10 18:00:00',
        travelStartTimeBeijing: '2025-09-10 09:00:00',
        travelEndTimeBeijing: '2025-09-10 18:00:00',
        totalDay: 1,
        
        // 城市信息
        startCityId: 1,
        startCityName: '北京',
        endCityId: 1,
        endCityName: '北京',
        
        // 价格信息
        amountChannelSys: 50000, // 500元，单位：分
        amountShouldPay: 50000,
        amountShouldReceive: 50000,
        amountActualPay: 50000,
        amountActualReceive: 50000,
        amountCouponTotal: 0,
        
        // 联系人信息
        customerName: '王五',
        customerAreaCode: '+86',
        customerMobile: '13800138002',
        customerWechat: 'wangwu123',
        
        // 支付信息
        payTime: '2025-09-09 15:00:00',
        thirdPayNo: 'PAY_' + Date.now(),
        
        // 其他信息
        priceMarkCity: 1,
        remark: '测试包车订单，一日游',
        
        // 每日行程
        travelDailyBaseReq: {
          dayIndex: 1,
          goodsType: 40, // 10小时100公里
          goodsTypeName: '10小时100公里',
          serviceStartTimeLocal: '2025-09-10 09:00:00',
          serviceEndTimeLocal: '2025-09-10 18:00:00',
          serviceStartTimeBeijing: '2025-09-10 09:00:00',
          serviceEndTimeBeijing: '2025-09-10 18:00:00',
          carModelId: 1,
          carModelName: '舒适5座',
          startCityId: 1,
          startCityName: '北京',
          endCityId: 1,
          endCityName: '北京',
          serviceMaxTime: 10,
          serviceMaxDistance: 100
        }
      }
    }
  };
  
  mcpProcess.stdin.write(JSON.stringify(createCharterOrderRequest) + '\n');
  
  // 等待响应后关闭
  setTimeout(() => {
    console.log('\n✅ 测试完成，关闭服务器...');
    mcpProcess.kill();
  }, 5000);
  
}, 1000);

mcpProcess.on('close', (code) => {
  console.log(`\n🔚 MCP 服务器已关闭，退出码: ${code}`);
});

mcpProcess.on('error', (error) => {
  console.error('❌ 启动 MCP 服务器失败:', error);
});
