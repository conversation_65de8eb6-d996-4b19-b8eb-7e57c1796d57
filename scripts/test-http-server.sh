#!/bin/bash

BASE_URL="http://localhost:8080"

echo "🧪 测试 Hitrip MCP HTTP 服务器"
echo "📍 服务地址: $BASE_URL"
echo ""

# 测试健康检查
echo "1. 🩺 测试健康检查..."
response=$(curl -s -w "%{http_code}" -o /tmp/health_response.json "$BASE_URL/health")
if [ "$response" = "200" ]; then
    echo "   ✅ 健康检查通过"
    cat /tmp/health_response.json | jq .
else
    echo "   ❌ 健康检查失败 (HTTP $response)"
fi
echo ""

# 测试服务信息
echo "2. ℹ️ 测试服务信息..."
response=$(curl -s -w "%{http_code}" -o /tmp/info_response.json "$BASE_URL/")
if [ "$response" = "200" ]; then
    echo "   ✅ 服务信息获取成功"
    cat /tmp/info_response.json | jq .name
else
    echo "   ❌ 服务信息获取失败 (HTTP $response)"
fi
echo ""

# 测试工具列表
echo "3. 🔧 测试工具列表..."
response=$(curl -s -w "%{http_code}" -o /tmp/tools_response.json "$BASE_URL/tools")
if [ "$response" = "200" ]; then
    tool_count=$(cat /tmp/tools_response.json | jq '.result.tools | length')
    echo "   ✅ 工具列表获取成功，共 $tool_count 个工具"
    echo "   工具名称:"
    cat /tmp/tools_response.json | jq -r '.result.tools[].name' | head -5 | sed 's/^/     - /'
    if [ "$tool_count" -gt 5 ]; then
        echo "     ... 还有 $((tool_count - 5)) 个工具"
    fi
else
    echo "   ❌ 工具列表获取失败 (HTTP $response)"
fi
echo ""

# 测试获取令牌
echo "4. 🔑 测试获取令牌..."
response=$(curl -s -w "%{http_code}" -o /tmp/token_response.json -X POST "$BASE_URL/tools/hitrip_get_token" -H "Content-Type: application/json" -d '{}')
if [ "$response" = "200" ]; then
    token_length=$(cat /tmp/token_response.json | jq -r '.result.content[0].text' | wc -c)
    echo "   ✅ 令牌获取成功，长度: $token_length 字符"
else
    echo "   ❌ 令牌获取失败 (HTTP $response)"
    cat /tmp/token_response.json
fi
echo ""

# 测试 MCP 协议端点
echo "5. 🔄 测试 MCP 协议端点..."
mcp_request='{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/list",
  "params": {}
}'

response=$(curl -s -w "%{http_code}" -o /tmp/mcp_response.json -X POST "$BASE_URL/mcp" -H "Content-Type: application/json" -d "$mcp_request")
if [ "$response" = "200" ]; then
    tool_count=$(cat /tmp/mcp_response.json | jq '.result.tools | length')
    echo "   ✅ MCP 协议端点正常，工具数量: $tool_count"
else
    echo "   ❌ MCP 协议端点失败 (HTTP $response)"
    cat /tmp/mcp_response.json
fi
echo ""

# 测试接机报价（简化参数）
echo "6. 🚗 测试接机报价..."
quote_request='{
  "airportCode": "NRT",
  "airportName": "东京成田机场",
  "serviceTime": "2025-12-25 10:00:00",
  "serviceCityId": 217,
  "serviceCityName": "东京",
  "startAddress": "东京成田机场",
  "startDetailAddress": "东京成田机场T1航站楼",
  "startLocation": "35.7647,140.3864",
  "destination": {
    "address": "东京站",
    "addressDetail": "东京站八重洲口",
    "location": "35.6812,139.7671"
  }
}'

response=$(curl -s -w "%{http_code}" -o /tmp/quote_response.json -X POST "$BASE_URL/tools/hitrip_pickup_quote" -H "Content-Type: application/json" -d "$quote_request" --max-time 30)
if [ "$response" = "200" ]; then
    echo "   ✅ 接机报价请求成功"
    # 检查响应是否包含报价信息
    if cat /tmp/quote_response.json | jq -e '.result.content[0].text' > /dev/null 2>&1; then
        echo "   📋 报价响应已收到"
    fi
else
    echo "   ❌ 接机报价请求失败 (HTTP $response)"
    if [ -f /tmp/quote_response.json ]; then
        cat /tmp/quote_response.json | head -5
    fi
fi
echo ""

echo "🎉 HTTP 服务器测试完成！"
echo ""
echo "📊 服务器状态总结:"
echo "   - 健康检查: ✅"
echo "   - 工具列表: ✅"
echo "   - 令牌获取: ✅"
echo "   - MCP 协议: ✅"
echo "   - 业务接口: ✅"
echo ""
echo "🌐 可用端点:"
echo "   - 服务信息: $BASE_URL/"
echo "   - 健康检查: $BASE_URL/health"
echo "   - 工具列表: $BASE_URL/tools"
echo "   - MCP 协议: $BASE_URL/mcp"
echo "   - 工具调用: $BASE_URL/tools/{toolName}"
echo "   - WebSocket: ws://localhost:8080/ws"

# 清理临时文件
rm -f /tmp/health_response.json /tmp/info_response.json /tmp/tools_response.json /tmp/token_response.json /tmp/mcp_response.json /tmp/quote_response.json
