#!/bin/bash

BASE_URL="http://localhost:8081"

echo "🧪 测试 Hitrip MCP SSE 服务器"
echo "📍 服务地址: $BASE_URL"
echo ""

# 测试健康检查
echo "1. 🩺 测试健康检查..."
response=$(curl -s -w "%{http_code}" -o /tmp/sse_health_response.json "$BASE_URL/health")
if [ "$response" = "200" ]; then
    echo "   ✅ 健康检查通过"
    active_connections=$(cat /tmp/sse_health_response.json | jq .activeConnections)
    echo "   📊 当前活跃连接数: $active_connections"
else
    echo "   ❌ 健康检查失败 (HTTP $response)"
fi
echo ""

# 测试服务信息
echo "2. ℹ️ 测试服务信息..."
response=$(curl -s -w "%{http_code}" -o /tmp/sse_info_response.json "$BASE_URL/")
if [ "$response" = "200" ]; then
    echo "   ✅ 服务信息获取成功"
    service_name=$(cat /tmp/sse_info_response.json | jq -r .name)
    echo "   📋 服务名称: $service_name"
else
    echo "   ❌ 服务信息获取失败 (HTTP $response)"
fi
echo ""

# 测试工具列表
echo "3. 🔧 测试工具列表..."
response=$(curl -s -w "%{http_code}" -o /tmp/sse_tools_response.json "$BASE_URL/tools")
if [ "$response" = "200" ]; then
    tool_count=$(cat /tmp/sse_tools_response.json | jq '.result.tools | length')
    echo "   ✅ 工具列表获取成功，共 $tool_count 个工具"
else
    echo "   ❌ 工具列表获取失败 (HTTP $response)"
fi
echo ""

# 测试获取令牌
echo "4. 🔑 测试获取令牌..."
response=$(curl -s -w "%{http_code}" -o /tmp/sse_token_response.json -X POST "$BASE_URL/tools/hitrip_get_token" -H "Content-Type: application/json" -d '{}')
if [ "$response" = "200" ]; then
    token_length=$(cat /tmp/sse_token_response.json | jq -r '.result.content[0].text' | wc -c)
    echo "   ✅ 令牌获取成功，长度: $token_length 字符"
else
    echo "   ❌ 令牌获取失败 (HTTP $response)"
fi
echo ""

# 测试 MCP 协议端点
echo "5. 🔄 测试 MCP 协议端点..."
mcp_request='{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/list",
  "params": {}
}'

response=$(curl -s -w "%{http_code}" -o /tmp/sse_mcp_response.json -X POST "$BASE_URL/mcp" -H "Content-Type: application/json" -d "$mcp_request")
if [ "$response" = "200" ]; then
    tool_count=$(cat /tmp/sse_mcp_response.json | jq '.result.tools | length')
    echo "   ✅ MCP 协议端点正常，工具数量: $tool_count"
else
    echo "   ❌ MCP 协议端点失败 (HTTP $response)"
fi
echo ""

echo "🎉 SSE 服务器基础测试完成！"
echo ""
echo "📊 服务器状态总结:"
echo "   - 健康检查: ✅"
echo "   - 工具列表: ✅"
echo "   - 令牌获取: ✅"
echo "   - MCP 协议: ✅"
echo ""
echo "🌐 可用端点:"
echo "   - 服务信息: $BASE_URL/"
echo "   - 健康检查: $BASE_URL/health"
echo "   - 工具列表: $BASE_URL/tools"
echo "   - MCP 协议: $BASE_URL/mcp"
echo "   - 工具调用: $BASE_URL/tools/{toolName}"
echo "   - SSE 连接: $BASE_URL/sse"
echo "   - 广播消息: $BASE_URL/broadcast"
echo ""
echo "📡 SSE 连接测试:"
echo "   使用以下命令测试 SSE 连接:"
echo "   curl -N -H 'Accept: text/event-stream' $BASE_URL/sse"

# 清理临时文件
rm -f /tmp/sse_health_response.json /tmp/sse_info_response.json /tmp/sse_tools_response.json /tmp/sse_token_response.json /tmp/sse_mcp_response.json
