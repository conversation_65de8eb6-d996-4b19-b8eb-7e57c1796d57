#!/usr/bin/env node

/**
 * 检查工具定义的脚本
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const serverPath = path.join(__dirname, '../dist/mcp-launcher.js');

console.log('🔍 检查MCP工具定义');
console.log('='.repeat(40));

const env = {
  ...process.env,
  HITRIP_CID: '1086910840',
  HITRIP_SECRET_KEY: '12345678',
  HITRIP_BASE_URL: 'https://api-gw.test.huangbaoche.com/',
  NODE_ENV: 'production'
};

const server = spawn('node', [serverPath], {
  env,
  stdio: ['pipe', 'pipe', 'pipe']
});

let messageId = 1;

server.stdout.on('data', (data) => {
  const output = data.toString();
  const lines = output.split('\n').filter(line => line.trim());
  
  lines.forEach(line => {
    try {
      const response = JSON.parse(line);
      if (response.result && response.result.tools) {
        const citiesTools = response.result.tools.filter(tool => tool.name === 'hitrip_get_cities');
        if (citiesTools.length > 0) {
          console.log('✅ 找到 hitrip_get_cities 工具定义:');
          console.log(JSON.stringify(citiesTools[0], null, 2));
          
          const properties = citiesTools[0].inputSchema?.properties;
          if (properties && properties.keyword) {
            console.log('✅ keyword 参数定义正确!');
            console.log('   类型:', properties.keyword.type);
            console.log('   描述:', properties.keyword.description);
          } else if (properties && properties.countryId) {
            console.log('❌ 仍然使用旧的 countryId 参数!');
            console.log('   需要重新构建项目');
          } else {
            console.log('⚠️  未找到参数定义');
          }
        }
      }
    } catch (error) {
      // 忽略非JSON输出
    }
  });
});

server.stderr.on('data', (data) => {
  // 忽略日志输出
});

function sendMessage(message) {
  server.stdin.write(JSON.stringify(message) + '\n');
}

setTimeout(() => {
  sendMessage({
    jsonrpc: '2.0',
    id: messageId++,
    method: 'initialize',
    params: {
      protocolVersion: '2024-11-05',
      capabilities: { tools: {} },
      clientInfo: { name: 'tool-checker', version: '1.0.0' }
    }
  });
}, 1000);

setTimeout(() => {
  sendMessage({
    jsonrpc: '2.0',
    method: 'notifications/initialized'
  });
}, 2000);

setTimeout(() => {
  sendMessage({
    jsonrpc: '2.0',
    id: messageId++,
    method: 'tools/list'
  });
}, 3000);

setTimeout(() => {
  server.kill();
  process.exit(0);
}, 5000);
