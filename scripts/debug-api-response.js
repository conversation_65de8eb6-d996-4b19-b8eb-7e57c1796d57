#!/usr/bin/env node

/**
 * 详细的API响应调试工具
 */

import axios from 'axios';

const config = {
  cid: '1086910840',
  secretKey: '12345678',
  baseUrl: 'https://api-gw.huangbaoche.com/'
};

console.log('🔍 黄包车API详细调试');
console.log('='.repeat(50));

async function debugTokenAPI() {
  const url = `${config.baseUrl}otaorder-api/access/getAccessToken?cid=${config.cid}&secretKey=${config.secretKey}`;
  
  console.log('📡 Token API 调试');
  console.log('-'.repeat(30));
  console.log('URL:', url);
  console.log('CID:', config.cid);
  console.log('Secret:', config.secretKey);
  
  try {
    console.log('\n🚀 发送请求...');
    const response = await axios.get(url, {
      timeout: 10000,
      headers: {
        'User-Agent': 'Hitrip-MCP-Server/1.0.0',
        'Accept': 'application/json'
      }
    });
    
    console.log('\n✅ 响应成功');
    console.log('状态码:', response.status);
    console.log('响应头:', JSON.stringify(response.headers, null, 2));
    console.log('响应体:', JSON.stringify(response.data, null, 2));
    
    // 分析响应
    if (response.data) {
      if (response.data.success === false) {
        console.log('\n❌ API返回失败');
        console.log('错误码:', response.data.status || response.data.code);
        console.log('错误信息:', response.data.message || response.data.msg);
        
        // 常见错误码解释
        const errorCodes = {
          '9330005': 'CID或密钥无效',
          '9330001': '参数缺失',
          '9330002': '签名验证失败',
          '9330003': '请求频率过高',
          '9330004': '账户状态异常'
        };
        
        const errorCode = response.data.status || response.data.code;
        if (errorCodes[errorCode]) {
          console.log('错误解释:', errorCodes[errorCode]);
        }
      } else if (response.data.success === true) {
        console.log('\n✅ Token获取成功');
        console.log('Token:', response.data.data);
      }
    }
    
  } catch (error) {
    console.log('\n❌ 请求失败');
    console.log('错误类型:', error.constructor.name);
    console.log('错误信息:', error.message);
    
    if (error.response) {
      console.log('响应状态:', error.response.status);
      console.log('响应头:', JSON.stringify(error.response.headers, null, 2));
      console.log('响应体:', JSON.stringify(error.response.data, null, 2));
    } else if (error.request) {
      console.log('请求配置:', error.config);
      console.log('无响应 - 可能是网络问题或服务器无响应');
    }
  }
}

async function testAlternativeEndpoints() {
  console.log('\n🔄 测试其他可能的端点');
  console.log('-'.repeat(30));
  
  const endpoints = [
    'otaorder-api/access/getAccessToken',
    'api/access/getAccessToken', 
    'access/getAccessToken',
    'getAccessToken'
  ];
  
  for (const endpoint of endpoints) {
    const url = `${config.baseUrl}${endpoint}?cid=${config.cid}&secretKey=${config.secretKey}`;
    console.log(`\n🧪 测试端点: ${endpoint}`);
    
    try {
      const response = await axios.get(url, { timeout: 5000 });
      console.log(`✅ ${endpoint} - 状态: ${response.status}`);
      if (response.data && response.data.success !== undefined) {
        console.log(`   成功: ${response.data.success}`);
        if (response.data.success) {
          console.log(`   🎉 找到正确端点: ${endpoint}`);
          return endpoint;
        }
      }
    } catch (error) {
      console.log(`❌ ${endpoint} - 错误: ${error.response?.status || error.message}`);
    }
  }
  
  return null;
}

async function main() {
  await debugTokenAPI();
  await testAlternativeEndpoints();
  
  console.log('\n💡 调试建议:');
  console.log('1. 检查CID和密钥是否正确');
  console.log('2. 确认账户状态是否正常');
  console.log('3. 检查API端点是否正确');
  console.log('4. 联系黄包车技术支持确认API配置');
}

main().catch(console.error);
