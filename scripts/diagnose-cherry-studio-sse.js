#!/usr/bin/env node

import { EventSource } from 'eventsource';
import fetch from 'node-fetch';
import net from 'net';
import { URL } from 'url';

const SSE_URL = 'http://localhost:8081/sse';
const MCP_URL = 'http://localhost:8081/mcp';
const HEALTH_URL = 'http://localhost:8081/health';

console.log('🔍 Cherry Studio SSE 连接诊断工具');
console.log('=====================================\n');

const diagnostics = [
  {
    name: '网络连通性检查',
    test: testNetworkConnectivity
  },
  {
    name: '服务器健康检查',
    test: testServerHealth
  },
  {
    name: 'SSE 端点可访问性',
    test: testSSEEndpoint
  },
  {
    name: 'SSE 连接建立测试',
    test: testSSEConnection
  },
  {
    name: 'SSE 事件接收测试',
    test: testSSEEvents
  },
  {
    name: 'MCP 协议兼容性',
    test: testMCPCompatibility
  },
  {
    name: 'Cherry Studio 配置验证',
    test: testCherryStudioConfig
  },
  {
    name: 'CORS 和头部检查',
    test: testCORSHeaders
  }
];

let results = [];

async function main() {
  console.log('🚀 开始诊断...\n');
  
  for (const diagnostic of diagnostics) {
    console.log(`📋 ${diagnostic.name}...`);
    try {
      const result = await diagnostic.test();
      results.push({ 
        name: diagnostic.name, 
        status: 'PASS', 
        result,
        recommendations: result.recommendations || []
      });
      console.log(`   ✅ ${diagnostic.name} - 通过`);
      if (result.details) {
        console.log(`   📝 ${result.details}`);
      }
    } catch (error) {
      results.push({ 
        name: diagnostic.name, 
        status: 'FAIL', 
        error: error.message,
        recommendations: error.recommendations || []
      });
      console.log(`   ❌ ${diagnostic.name} - 失败: ${error.message}`);
    }
    console.log('');
  }
  
  generateDiagnosticReport();
}

async function testNetworkConnectivity() {
  const url = new URL(SSE_URL);
  const port = url.port || (url.protocol === 'https:' ? 443 : 80);
  
  return new Promise((resolve, reject) => {
    const socket = net.createConnection(port, url.hostname);
    
    socket.on('connect', () => {
      socket.destroy();
      resolve({
        details: `端口 ${port} 可访问`,
        host: url.hostname,
        port: port
      });
    });
    
    socket.on('error', (error) => {
      reject(new Error(`网络连接失败: ${error.message}`));
    });
    
    setTimeout(() => {
      socket.destroy();
      reject(new Error('连接超时'));
    }, 5000);
  });
}

async function testServerHealth() {
  const response = await fetch(HEALTH_URL);
  
  if (!response.ok) {
    throw new Error(`健康检查失败: HTTP ${response.status}`);
  }
  
  const data = await response.json();
  
  return {
    details: `服务状态: ${data.status}, 活跃连接: ${data.activeConnections}`,
    serverStatus: data.status,
    activeConnections: data.activeConnections,
    service: data.service
  };
}

async function testSSEEndpoint() {
  const response = await fetch(SSE_URL, {
    method: 'GET',
    headers: {
      'Accept': 'text/event-stream',
      'Cache-Control': 'no-cache'
    }
  });
  
  if (!response.ok) {
    throw new Error(`SSE 端点不可访问: HTTP ${response.status}`);
  }
  
  const contentType = response.headers.get('content-type');
  
  if (!contentType || !contentType.includes('text/event-stream')) {
    throw new Error(`错误的 Content-Type: ${contentType}, 期望: text/event-stream`);
  }
  
  return {
    details: `SSE 端点正常, Content-Type: ${contentType}`,
    contentType,
    headers: Object.fromEntries(response.headers.entries())
  };
}

async function testSSEConnection() {
  return new Promise((resolve, reject) => {
    const eventSource = new EventSource(SSE_URL);
    let connected = false;
    
    const timeout = setTimeout(() => {
      if (!connected) {
        eventSource.close();
        reject(new Error('SSE 连接超时 (10秒)'));
      }
    }, 10000);
    
    eventSource.onopen = () => {
      connected = true;
      clearTimeout(timeout);
      eventSource.close();
      resolve({
        details: 'SSE 连接成功建立',
        readyState: eventSource.readyState
      });
    };
    
    eventSource.onerror = (error) => {
      clearTimeout(timeout);
      eventSource.close();
      reject(new Error(`SSE 连接错误: ${error.message || 'Unknown error'}`));
    };
  });
}

async function testSSEEvents() {
  return new Promise((resolve, reject) => {
    const eventSource = new EventSource(SSE_URL);
    let eventsReceived = [];
    let connectionId = null;
    
    const timeout = setTimeout(() => {
      eventSource.close();
      if (eventsReceived.length === 0) {
        reject(new Error('未收到任何 SSE 事件'));
      } else {
        resolve({
          details: `收到 ${eventsReceived.length} 个事件`,
          events: eventsReceived,
          connectionId
        });
      }
    }, 15000);
    
    eventSource.addEventListener('connected', (event) => {
      const data = JSON.parse(event.data);
      connectionId = data.connectionId;
      eventsReceived.push({ type: 'connected', data });
    });
    
    eventSource.addEventListener('tools', (event) => {
      const data = JSON.parse(event.data);
      eventsReceived.push({ type: 'tools', toolCount: data.tools.length });
      
      clearTimeout(timeout);
      eventSource.close();
      resolve({
        details: `成功接收事件: connected, tools (${data.tools.length} 个工具)`,
        events: eventsReceived,
        connectionId
      });
    });
    
    eventSource.addEventListener('ping', (event) => {
      eventsReceived.push({ type: 'ping', timestamp: JSON.parse(event.data).timestamp });
    });
    
    eventSource.onerror = (error) => {
      clearTimeout(timeout);
      eventSource.close();
      reject(new Error(`SSE 事件接收错误: ${error.message || 'Unknown error'}`));
    };
  });
}

async function testMCPCompatibility() {
  const request = {
    jsonrpc: '2.0',
    id: 1,
    method: 'tools/list',
    params: {}
  };
  
  const response = await fetch(MCP_URL, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(request)
  });
  
  if (!response.ok) {
    throw new Error(`MCP 请求失败: HTTP ${response.status}`);
  }
  
  const data = await response.json();
  
  if (data.error) {
    throw new Error(`MCP 协议错误: ${data.error.message}`);
  }
  
  if (!data.result || !data.result.tools) {
    throw new Error('MCP 响应格式错误: 缺少 tools 字段');
  }
  
  return {
    details: `MCP 协议兼容, 工具数量: ${data.result.tools.length}`,
    toolCount: data.result.tools.length,
    jsonrpcVersion: data.jsonrpc,
    requestId: data.id
  };
}

async function testCherryStudioConfig() {
  // 测试不同的配置格式
  const configs = [
    {
      name: 'SSE 模式 (url)',
      config: {
        type: 'sse',
        url: SSE_URL,
        timeout: 30000
      }
    },
    {
      name: 'HTTP 模式 (SSE 端点)',
      config: {
        type: 'http',
        url: SSE_URL,
        timeout: 30000
      }
    },
    {
      name: 'HTTP 模式 (MCP 端点)',
      config: {
        type: 'http',
        url: MCP_URL,
        timeout: 30000
      }
    }
  ];
  
  const recommendations = [];
  
  // 检查 SSE 端点是否返回正确的头部
  const sseResponse = await fetch(SSE_URL, {
    method: 'GET',
    headers: { 'Accept': 'text/event-stream' }
  });
  
  if (sseResponse.headers.get('content-type')?.includes('text/event-stream')) {
    recommendations.push('✅ SSE 端点返回正确的 Content-Type');
  } else {
    recommendations.push('❌ SSE 端点 Content-Type 不正确');
  }
  
  // 检查 MCP 端点
  const mcpResponse = await fetch(MCP_URL, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ jsonrpc: '2.0', id: 1, method: 'tools/list', params: {} })
  });
  
  if (mcpResponse.ok) {
    recommendations.push('✅ MCP 端点工作正常');
  } else {
    recommendations.push('❌ MCP 端点有问题');
  }
  
  return {
    details: '配置兼容性检查完成',
    supportedConfigs: configs,
    recommendations
  };
}

async function testCORSHeaders() {
  const response = await fetch(SSE_URL, {
    method: 'OPTIONS',
    headers: {
      'Origin': 'http://localhost:3000',
      'Access-Control-Request-Method': 'GET',
      'Access-Control-Request-Headers': 'Accept'
    }
  });
  
  const corsHeaders = {
    'access-control-allow-origin': response.headers.get('access-control-allow-origin'),
    'access-control-allow-methods': response.headers.get('access-control-allow-methods'),
    'access-control-allow-headers': response.headers.get('access-control-allow-headers'),
    'access-control-allow-credentials': response.headers.get('access-control-allow-credentials')
  };
  
  const recommendations = [];
  
  if (!corsHeaders['access-control-allow-origin']) {
    recommendations.push('⚠️ 缺少 CORS Access-Control-Allow-Origin 头部');
  }
  
  if (!corsHeaders['access-control-allow-methods']) {
    recommendations.push('⚠️ 缺少 CORS Access-Control-Allow-Methods 头部');
  }
  
  return {
    details: 'CORS 头部检查完成',
    corsHeaders,
    recommendations
  };
}

function generateDiagnosticReport() {
  console.log('📊 诊断报告');
  console.log('=' * 50);
  
  const passCount = results.filter(r => r.status === 'PASS').length;
  const failCount = results.filter(r => r.status === 'FAIL').length;
  
  console.log(`总检查项: ${results.length}`);
  console.log(`通过: ${passCount}`);
  console.log(`失败: ${failCount}`);
  console.log(`成功率: ${((passCount / results.length) * 100).toFixed(1)}%\n`);
  
  // 详细结果
  results.forEach(result => {
    const status = result.status === 'PASS' ? '✅' : '❌';
    console.log(`${status} ${result.name}`);
    if (result.status === 'FAIL') {
      console.log(`   错误: ${result.error}`);
    }
    if (result.recommendations && result.recommendations.length > 0) {
      result.recommendations.forEach(rec => {
        console.log(`   ${rec}`);
      });
    }
  });
  
  console.log('\n🔧 Cherry Studio 配置建议:');
  
  const failedTests = results.filter(r => r.status === 'FAIL');
  
  if (failedTests.length === 0) {
    console.log('\n✅ 所有测试通过！SSE 服务器工作正常。');
    console.log('\n可能的 Cherry Studio 配置问题:');
    console.log('1. Cherry Studio 可能不支持 SSE 模式');
    console.log('2. 配置格式可能不正确');
    console.log('3. Cherry Studio 版本问题');
    
    console.log('\n推荐配置:');
    console.log('```json');
    console.log(JSON.stringify({
      mcpServers: {
        hitrip: {
          name: "Hitrip MCP Server",
          type: "http",
          url: "http://localhost:8081/mcp",
          timeout: 30000
        }
      }
    }, null, 2));
    console.log('```');
    
    console.log('\n备选配置 (如果支持 SSE):');
    console.log('```json');
    console.log(JSON.stringify({
      mcpServers: {
        hitrip: {
          name: "Hitrip MCP Server",
          type: "sse",
          url: "http://localhost:8081/sse",
          mcpUrl: "http://localhost:8081/mcp",
          timeout: 30000
        }
      }
    }, null, 2));
    console.log('```');
    
  } else {
    console.log('\n❌ 发现问题，需要修复:');
    failedTests.forEach(test => {
      console.log(`- ${test.name}: ${test.error}`);
    });
  }
  
  console.log('\n📞 进一步调试建议:');
  console.log('1. 检查 Cherry Studio 版本和 SSE 支持');
  console.log('2. 查看 Cherry Studio 控制台错误日志');
  console.log('3. 尝试使用 HTTP 模式而不是 SSE 模式');
  console.log('4. 检查防火墙和网络设置');
  console.log('5. 尝试不同的端口或 URL 格式');
}

// 运行诊断
main().catch(error => {
  console.error('❌ 诊断工具执行失败:', error);
  process.exit(1);
});
