#!/bin/bash

# 设置环境变量
export HITRIP_CID="1086910840"
export HITRIP_SECRET_KEY="12345678"
export HITRIP_BASE_URL="https://api-gw.test.huangbaoche.com/"
export NODE_ENV="production"
export LOG_LEVEL="info"
export PORT="8081"

echo "🚀 启动 Hitrip MCP SSE 服务器..."
echo "📍 端口: $PORT"
echo "🔗 基础 URL: $HITRIP_BASE_URL"
echo "🆔 渠道 ID: $HITRIP_CID"
echo "📡 SSE 端点: http://localhost:$PORT/sse"
echo ""

# 启动服务器
npm run start:sse
