#!/usr/bin/env node

/**
 * 验证MCP服务器配置
 */

const { spawn } = require('child_process');
const path = require('path');

const serverPath = path.join(__dirname, '../dist/stdio-launcher.js');

console.log('🔍 验证 Hitrip MCP Server 配置...');
console.log(`服务器路径: ${serverPath}`);

// 设置环境变量
const env = {
  ...process.env,
  HITRIP_CID: '1086910840',
  HITRIP_SECRET_KEY: '12345678',
  HITRIP_BASE_URL: 'https://api-gw.test.huangbaoche.com/',
  NODE_ENV: 'production',
  LOG_LEVEL: 'info'
};

// 启动服务器进程
const server = spawn('node', [serverPath], {
  env,
  stdio: ['pipe', 'pipe', 'pipe']
});

let output = '';
let errorOutput = '';

server.stdout.on('data', (data) => {
  output += data.toString();
});

server.stderr.on('data', (data) => {
  errorOutput += data.toString();
});

// 发送MCP初始化消息
const initMessage = {
  jsonrpc: '2.0',
  id: 1,
  method: 'initialize',
  params: {
    protocolVersion: '2024-11-05',
    capabilities: {
      tools: {}
    },
    clientInfo: {
      name: 'test-client',
      version: '1.0.0'
    }
  }
};

setTimeout(() => {
  console.log('📤 发送初始化消息...');
  server.stdin.write(JSON.stringify(initMessage) + '\n');
}, 1000);

// 请求工具列表
setTimeout(() => {
  const listToolsMessage = {
    jsonrpc: '2.0',
    id: 2,
    method: 'tools/list'
  };
  
  console.log('📤 请求工具列表...');
  server.stdin.write(JSON.stringify(listToolsMessage) + '\n');
}, 2000);

// 5秒后结束测试
setTimeout(() => {
  server.kill();
  
  console.log('\n📊 测试结果:');
  console.log('='.repeat(50));
  
  if (output) {
    console.log('✅ 服务器输出:');
    console.log(output);
  }
  
  if (errorOutput) {
    console.log('❌ 错误输出:');
    console.log(errorOutput);
  }
  
  if (output.includes('tools')) {
    console.log('🎉 配置验证成功！MCP服务器正常运行');
  } else {
    console.log('⚠️  配置可能有问题，请检查环境变量和路径');
  }
  
  process.exit(0);
}, 5000);

server.on('error', (error) => {
  console.error('❌ 服务器启动失败:', error.message);
  process.exit(1);
});

server.on('close', (code) => {
  console.log(`\n🔚 服务器进程结束，退出码: ${code}`);
});
