#!/usr/bin/env node

import { EventSource } from 'eventsource';
import fetch from 'node-fetch';
import fs from 'fs';
import path from 'path';

const SSE_URL = 'http://localhost:8081/sse';
const MCP_URL = 'http://localhost:8081/mcp';
const HEALTH_URL = 'http://localhost:8081/health';

console.log('🍒 Cherry Studio SSE 连接测试');
console.log('📍 测试 Hitrip MCP 服务器 SSE 模式连接\n');

// 测试步骤
const tests = [
  {
    name: '服务器健康检查',
    test: testHealth
  },
  {
    name: 'SSE 连接测试',
    test: testSSEConnection
  },
  {
    name: 'MCP 协议测试',
    test: testMCPProtocol
  },
  {
    name: '工具调用测试',
    test: testToolCalls
  }
];

let testResults = [];

async function main() {
  console.log('🚀 开始测试...\n');
  
  for (const test of tests) {
    console.log(`📋 ${test.name}...`);
    try {
      const result = await test.test();
      testResults.push({ name: test.name, status: 'PASS', result });
      console.log(`   ✅ ${test.name} - 通过\n`);
    } catch (error) {
      testResults.push({ name: test.name, status: 'FAIL', error: error.message });
      console.log(`   ❌ ${test.name} - 失败: ${error.message}\n`);
    }
  }
  
  // 生成测试报告
  generateReport();
}

async function testHealth() {
  const response = await fetch(HEALTH_URL);
  if (!response.ok) {
    throw new Error(`健康检查失败: HTTP ${response.status}`);
  }
  
  const data = await response.json();
  return {
    status: data.status,
    service: data.service,
    activeConnections: data.activeConnections
  };
}

async function testSSEConnection() {
  return new Promise((resolve, reject) => {
    const eventSource = new EventSource(SSE_URL);
    let connectionId = null;
    let toolsReceived = false;
    
    const timeout = setTimeout(() => {
      eventSource.close();
      reject(new Error('SSE 连接超时'));
    }, 10000);
    
    eventSource.addEventListener('connected', (event) => {
      const data = JSON.parse(event.data);
      connectionId = data.connectionId;
      console.log(`   🔗 连接ID: ${connectionId}`);
    });
    
    eventSource.addEventListener('tools', (event) => {
      const data = JSON.parse(event.data);
      toolsReceived = true;
      console.log(`   📋 收到工具列表: ${data.tools.length} 个工具`);
      
      clearTimeout(timeout);
      eventSource.close();
      resolve({
        connectionId,
        toolsCount: data.tools.length,
        tools: data.tools.slice(0, 5).map(t => t.name)
      });
    });
    
    eventSource.onerror = (error) => {
      clearTimeout(timeout);
      eventSource.close();
      reject(new Error('SSE 连接错误'));
    };
  });
}

async function testMCPProtocol() {
  // 测试工具列表请求
  const request = {
    jsonrpc: '2.0',
    id: 1,
    method: 'tools/list',
    params: {}
  };
  
  const response = await fetch(MCP_URL, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(request)
  });
  
  if (!response.ok) {
    throw new Error(`MCP 请求失败: HTTP ${response.status}`);
  }
  
  const data = await response.json();
  
  if (data.error) {
    throw new Error(`MCP 错误: ${data.error.message}`);
  }
  
  return {
    toolsCount: data.result.tools.length,
    sampleTools: data.result.tools.slice(0, 3).map(t => ({
      name: t.name,
      description: t.description
    }))
  };
}

async function testToolCalls() {
  const tests = [
    {
      name: '获取令牌',
      request: {
        jsonrpc: '2.0',
        id: 2,
        method: 'tools/call',
        params: {
          name: 'hitrip_get_token',
          arguments: {}
        }
      }
    },
    {
      name: '查询城市',
      request: {
        jsonrpc: '2.0',
        id: 3,
        method: 'tools/call',
        params: {
          name: 'hitrip_get_cities',
          arguments: {
            keyword: '东京'
          }
        }
      }
    }
  ];
  
  const results = [];
  
  for (const test of tests) {
    const response = await fetch(MCP_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(test.request)
    });
    
    if (!response.ok) {
      throw new Error(`${test.name} 请求失败: HTTP ${response.status}`);
    }
    
    const data = await response.json();
    
    if (data.error) {
      throw new Error(`${test.name} 错误: ${data.error.message}`);
    }
    
    results.push({
      name: test.name,
      success: true,
      contentLength: data.result.content[0].text.length
    });
    
    console.log(`   ✅ ${test.name} - 响应长度: ${data.result.content[0].text.length} 字符`);
  }
  
  return results;
}

function generateReport() {
  console.log('📊 测试报告');
  console.log('=' * 50);
  
  const passCount = testResults.filter(r => r.status === 'PASS').length;
  const failCount = testResults.filter(r => r.status === 'FAIL').length;
  
  console.log(`总测试数: ${testResults.length}`);
  console.log(`通过: ${passCount}`);
  console.log(`失败: ${failCount}`);
  console.log(`成功率: ${((passCount / testResults.length) * 100).toFixed(1)}%\n`);
  
  testResults.forEach(result => {
    const status = result.status === 'PASS' ? '✅' : '❌';
    console.log(`${status} ${result.name}`);
    if (result.status === 'FAIL') {
      console.log(`   错误: ${result.error}`);
    }
  });
  
  // 生成 Cherry Studio 配置建议
  if (passCount === testResults.length) {
    console.log('\n🎉 所有测试通过！');
    console.log('\n📋 Cherry Studio 配置建议:');
    console.log('1. 使用配置文件: config/cherry-studio-sse-simple.json');
    console.log('2. 或者手动配置:');
    console.log('   - SSE URL: http://localhost:8081/sse');
    console.log('   - MCP URL: http://localhost:8081/mcp');
    console.log('   - 类型: SSE');
    console.log('   - 超时: 30000ms');
    console.log('   - 重连: 启用');
  } else {
    console.log('\n⚠️ 部分测试失败，请检查服务器状态');
    console.log('1. 确保 SSE 服务器正在运行: ./scripts/start-sse-server.sh');
    console.log('2. 检查端口 8081 是否可访问');
    console.log('3. 查看服务器日志');
  }
  
  // 保存详细报告
  const reportPath = 'test-results/cherry-studio-connection-test.json';
  const reportDir = path.dirname(reportPath);
  
  if (!fs.existsSync(reportDir)) {
    fs.mkdirSync(reportDir, { recursive: true });
  }
  
  fs.writeFileSync(reportPath, JSON.stringify({
    timestamp: new Date().toISOString(),
    summary: {
      total: testResults.length,
      passed: passCount,
      failed: failCount,
      successRate: ((passCount / testResults.length) * 100).toFixed(1) + '%'
    },
    results: testResults
  }, null, 2));
  
  console.log(`\n📄 详细报告已保存到: ${reportPath}`);
}

// 运行测试
main().catch(error => {
  console.error('❌ 测试执行失败:', error);
  process.exit(1);
});
