#!/usr/bin/env node

/**
 * MCP 服务器调试脚本
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🔍 MCP 服务器调试诊断');
console.log('='.repeat(50));

// 1. 检查文件路径
const serverPath = path.join(__dirname, '../dist/stdio-launcher.js');
console.log(`1. 检查服务器文件: ${serverPath}`);

if (fs.existsSync(serverPath)) {
  console.log('✅ 服务器文件存在');
  const stats = fs.statSync(serverPath);
  console.log(`   文件大小: ${stats.size} bytes`);
  console.log(`   修改时间: ${stats.mtime}`);
} else {
  console.log('❌ 服务器文件不存在');
  console.log('请运行: npm run build');
  process.exit(1);
}

// 2. 检查Node.js版本
console.log(`\n2. Node.js 版本: ${process.version}`);
if (parseInt(process.version.slice(1)) < 18) {
  console.log('⚠️  建议使用 Node.js 18+');
}

// 3. 检查环境变量
console.log('\n3. 环境变量检查:');
const requiredEnvs = ['HITRIP_CID', 'HITRIP_SECRET_KEY', 'HITRIP_BASE_URL'];
const env = {
  ...process.env,
  HITRIP_CID: '1086910840',
  HITRIP_SECRET_KEY: '12345678',
  HITRIP_BASE_URL: 'https://api-gw.test.huangbaoche.com/',
  NODE_ENV: 'production',
  LOG_LEVEL: 'debug'
};

requiredEnvs.forEach(key => {
  if (env[key]) {
    console.log(`✅ ${key}: ${env[key]}`);
  } else {
    console.log(`❌ ${key}: 未设置`);
  }
});

// 4. 测试服务器启动
console.log('\n4. 测试服务器启动...');

const server = spawn('node', [serverPath], {
  env,
  stdio: ['pipe', 'pipe', 'pipe']
});

let stdout = '';
let stderr = '';
let hasStarted = false;

server.stdout.on('data', (data) => {
  const output = data.toString();
  stdout += output;
  console.log('📤 STDOUT:', output.trim());
  
  if (output.includes('MCP Server started') || output.includes('info')) {
    hasStarted = true;
  }
});

server.stderr.on('data', (data) => {
  const output = data.toString();
  stderr += output;
  console.log('📥 STDERR:', output.trim());
});

server.on('error', (error) => {
  console.log('❌ 进程启动错误:', error.message);
});

server.on('close', (code, signal) => {
  console.log(`\n🔚 进程结束: 退出码=${code}, 信号=${signal}`);
});

// 5. 发送MCP协议消息
setTimeout(() => {
  if (!hasStarted) {
    console.log('\n5. 发送MCP初始化消息...');
    
    const initMessage = {
      jsonrpc: '2.0',
      id: 1,
      method: 'initialize',
      params: {
        protocolVersion: '2024-11-05',
        capabilities: {
          tools: {}
        },
        clientInfo: {
          name: 'debug-client',
          version: '1.0.0'
        }
      }
    };
    
    try {
      server.stdin.write(JSON.stringify(initMessage) + '\n');
      console.log('📤 已发送初始化消息');
    } catch (error) {
      console.log('❌ 发送消息失败:', error.message);
    }
  }
}, 2000);

// 6. 请求工具列表
setTimeout(() => {
  const listMessage = {
    jsonrpc: '2.0',
    id: 2,
    method: 'tools/list',
    params: {}
  };
  
  try {
    server.stdin.write(JSON.stringify(listMessage) + '\n');
    console.log('📤 已发送工具列表请求');
  } catch (error) {
    console.log('❌ 发送工具列表请求失败:', error.message);
  }
}, 4000);

// 7. 生成诊断报告
setTimeout(() => {
  console.log('\n' + '='.repeat(50));
  console.log('📊 诊断报告');
  console.log('='.repeat(50));
  
  if (hasStarted) {
    console.log('✅ 服务器启动成功');
  } else {
    console.log('❌ 服务器启动失败');
  }
  
  if (stdout) {
    console.log('\n📤 标准输出:');
    console.log(stdout);
  }
  
  if (stderr) {
    console.log('\n📥 错误输出:');
    console.log(stderr);
  }
  
  // 生成配置建议
  console.log('\n💡 配置建议:');
  console.log('Claude Desktop 配置文件路径:');
  console.log('  macOS: ~/Library/Application Support/Claude/claude_desktop_config.json');
  console.log('  Windows: %APPDATA%\\Claude\\claude_desktop_config.json');
  
  console.log('\n推荐配置:');
  const config = {
    mcpServers: {
      hitrip: {
        command: 'node',
        args: [serverPath],
        env: {
          HITRIP_CID: '1086910840',
          HITRIP_SECRET_KEY: '12345678',
          HITRIP_BASE_URL: 'https://api-gw.test.huangbaoche.com/',
          NODE_ENV: 'production',
          LOG_LEVEL: 'info'
        }
      }
    }
  };
  
  console.log(JSON.stringify(config, null, 2));
  
  server.kill();
  process.exit(0);
}, 6000);
