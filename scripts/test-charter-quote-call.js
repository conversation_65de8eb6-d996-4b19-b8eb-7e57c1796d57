#!/usr/bin/env node

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

console.log('🧪 测试 Charter Quote 工具调用...\n');

// 启动 MCP 服务器
const mcpProcess = spawn('node', [join(projectRoot, 'dist/mcp-launcher.js')], {
  env: {
    ...process.env,
    HITRIP_CID: '1086910840',
    HITRIP_SECRET_KEY: '12345678',
    HITRIP_BASE_URL: 'https://api-gw.test.huangbaoche.com/',
    NODE_ENV: 'production',
    LOG_LEVEL: 'debug'
  },
  stdio: ['pipe', 'pipe', 'pipe']
});

let responseBuffer = '';

mcpProcess.stdout.on('data', (data) => {
  responseBuffer += data.toString();
  
  // 检查是否收到完整的 JSON-RPC 响应
  const lines = responseBuffer.split('\n');
  for (const line of lines) {
    if (line.trim() && line.startsWith('{')) {
      try {
        const response = JSON.parse(line);
        console.log('📨 收到响应:', JSON.stringify(response, null, 2));
      } catch (e) {
        // 忽略解析错误
      }
    }
  }
});

mcpProcess.stderr.on('data', (data) => {
  console.error('❌ 错误输出:', data.toString());
});

// 等待服务器启动
setTimeout(() => {
  console.log('📋 发送工具列表请求...');
  
  // 1. 首先获取工具列表
  const listToolsRequest = {
    jsonrpc: '2.0',
    id: 1,
    method: 'tools/list',
    params: {}
  };
  
  mcpProcess.stdin.write(JSON.stringify(listToolsRequest) + '\n');
  
  // 2. 等待一下，然后调用 charter_quote 工具
  setTimeout(() => {
    console.log('🚗 发送包车报价请求...');
    
    const charterQuoteRequest = {
      jsonrpc: '2.0',
      id: 2,
      method: 'tools/call',
      params: {
        name: 'hitrip_charter_quote',
        arguments: {
          serviceTime: '2025-09-10 09:00:00',
          origin: {
            address: '北京首都国际机场',
            location: '40.080111,116.584556',
            cityId: 1,
            cityName: '北京'
          },
          destination: {
            address: '北京市朝阳区国贸中心',
            location: '39.908722,116.447861',
            cityId: 1,
            cityName: '北京'
          },
          serviceHours: 8,
          tourType: 1,
          distance: 35000,
          duration: 60
        }
      }
    };
    
    mcpProcess.stdin.write(JSON.stringify(charterQuoteRequest) + '\n');
    
    // 3. 等待响应后关闭
    setTimeout(() => {
      console.log('\n✅ 测试完成，关闭服务器...');
      mcpProcess.kill();
    }, 5000);
    
  }, 2000);
  
}, 1000);

mcpProcess.on('close', (code) => {
  console.log(`\n🔚 MCP 服务器已关闭，退出码: ${code}`);
});

mcpProcess.on('error', (error) => {
  console.error('❌ 启动 MCP 服务器失败:', error);
});
