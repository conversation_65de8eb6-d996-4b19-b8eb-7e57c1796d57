#!/usr/bin/env node

/**
 * 测试MCP交互，模拟Claude Desktop的行为
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const serverPath = path.join(__dirname, '../dist/debug-mcp-launcher.js');

console.log('🧪 测试MCP交互');
console.log('='.repeat(40));

const env = {
  ...process.env,
  HITRIP_CID: '1086910840',
  HITRIP_SECRET_KEY: '12345678',
  HITRIP_BASE_URL: 'https://api-gw.test.huangbaoche.com/',
  NODE_ENV: 'development',
  LOG_LEVEL: 'debug'
};

console.log('🚀 启动MCP服务器...');
const server = spawn('node', [serverPath], {
  env,
  stdio: ['pipe', 'pipe', 'pipe']
});

let responses = [];
let messageId = 1;

server.stdout.on('data', (data) => {
  const output = data.toString();
  const lines = output.split('\n').filter(line => line.trim());
  
  lines.forEach(line => {
    try {
      const response = JSON.parse(line);
      responses.push(response);
      console.log('📥 收到响应:', JSON.stringify(response, null, 2));
    } catch (error) {
      console.log('📥 非JSON输出:', line);
    }
  });
});

server.stderr.on('data', (data) => {
  console.log('🔍 服务器日志:', data.toString().trim());
});

server.on('error', (error) => {
  console.error('❌ 服务器错误:', error.message);
  process.exit(1);
});

function sendMessage(message) {
  const jsonMessage = JSON.stringify(message);
  console.log('📤 发送消息:', jsonMessage);
  server.stdin.write(jsonMessage + '\n');
}

// 测试序列
console.log('\n1️⃣ 等待服务器启动...');
setTimeout(() => {
  console.log('\n2️⃣ 发送初始化消息...');
  sendMessage({
    jsonrpc: '2.0',
    id: messageId++,
    method: 'initialize',
    params: {
      protocolVersion: '2024-11-05',
      capabilities: {
        tools: {}
      },
      clientInfo: {
        name: 'test-client',
        version: '1.0.0'
      }
    }
  });
}, 2000);

setTimeout(() => {
  console.log('\n3️⃣ 发送初始化完成通知...');
  sendMessage({
    jsonrpc: '2.0',
    method: 'notifications/initialized'
  });
}, 3000);

setTimeout(() => {
  console.log('\n4️⃣ 请求工具列表...');
  sendMessage({
    jsonrpc: '2.0',
    id: messageId++,
    method: 'tools/list'
  });
}, 4000);

setTimeout(() => {
  console.log('\n5️⃣ 测试简单工具调用 (获取Token)...');
  sendMessage({
    jsonrpc: '2.0',
    id: messageId++,
    method: 'tools/call',
    params: {
      name: 'hitrip_get_token',
      arguments: {}
    }
  });
}, 5000);

setTimeout(() => {
  console.log('\n6️⃣ 测试复杂工具调用 (获取城市)...');
  sendMessage({
    jsonrpc: '2.0',
    id: messageId++,
    method: 'tools/call',
    params: {
      name: 'hitrip_get_cities',
      arguments: {
        countryId: 1
      }
    }
  });
}, 6000);

setTimeout(() => {
  console.log('\n📊 测试总结:');
  console.log('='.repeat(40));
  
  if (responses.length > 0) {
    console.log(`✅ 收到 ${responses.length} 个响应`);
    
    responses.forEach((response, index) => {
      console.log(`\n响应 ${index + 1}:`);
      if (response.error) {
        console.log(`❌ 错误: ${response.error.message}`);
        if (response.error.data) {
          console.log(`   详情: ${JSON.stringify(response.error.data)}`);
        }
      } else if (response.result) {
        console.log(`✅ 成功`);
        if (response.result.tools) {
          console.log(`   工具数量: ${response.result.tools.length}`);
        }
        if (response.result.content) {
          console.log(`   内容长度: ${JSON.stringify(response.result.content).length} 字符`);
        }
      }
    });
  } else {
    console.log('❌ 没有收到任何响应');
  }
  
  console.log('\n💡 现在查看详细日志:');
  console.log('node scripts/view-logs.js');
  
  server.kill();
  process.exit(0);
}, 8000);
