{"mcpServers": {"hitrip": {"name": "Hitrip MCP Server (SSE)", "description": "黄包车接机送机包车服务 - SSE 模式", "type": "sse", "config": {"sseUrl": "http://localhost:8081/sse", "mcpUrl": "http://localhost:8081/mcp", "healthUrl": "http://localhost:8081/health", "timeout": 30000, "reconnect": true, "reconnectInterval": 5000, "maxReconnectAttempts": 10, "withCredentials": false, "headers": {"Accept": "text/event-stream", "Cache-Control": "no-cache", "Connection": "keep-alive"}, "heartbeat": {"enabled": true, "interval": 30000, "timeout": 10000}, "logging": {"enabled": true, "level": "info"}}, "tools": {"include": ["hitrip_get_token", "hitrip_get_cities", "hitrip_get_airports", "hitrip_get_vehicle_types", "hitrip_pickup_quote", "hitrip_dropoff_quote", "hitrip_charter_quote", "hitrip_create_pickup_order", "hitrip_create_dropoff_order", "hitrip_create_charter_order", "hitrip_get_order_list", "hitrip_get_order_detail", "hitrip_cancel_order"], "exclude": []}, "metadata": {"version": "1.0.0", "author": "rockenlee", "homepage": "https://github.com/your-repo/hitrip-mcp-server", "documentation": "https://github.com/your-repo/hitrip-mcp-server/blob/main/docs/SSE_DEPLOYMENT.md"}}}, "settings": {"autoConnect": true, "retryOnFailure": true, "maxRetries": 5, "retryDelay": 3000, "healthCheckInterval": 60000, "enableNotifications": true, "logLevel": "info"}}