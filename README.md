# Hitrip MCP Server

黄包车 Open API 的 MCP (Model Context Protocol) 服务器实现，为 AI 助手提供旅游预订服务能力。

## 功能特性

- 🚗 **接送机服务** - 机场接送预订和报价
- 🚌 **包车服务** - 日租包车预订和报价  
- 🏙️ **目的地查询** - 城市、机场、车型信息查询
- 📋 **订单管理** - 订单创建、查询、取消
- 🔐 **安全认证** - 支持签名验证和DES加密
- 💾 **智能缓存** - Token自动管理和数据缓存
- 📊 **完整日志** - 结构化日志记录

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 配置环境变量

```bash
cp .env.example .env
# 编辑 .env 文件，填入黄包车API配置
```

### 3. 构建项目

```bash
npm run build
```

### 4. 启动服务

```bash
# STDIO 模式 (推荐用于AI助手集成)
npm run start:stdio

# SSE 模式 (用于Web集成)
npm run start:sse
```

## 开发模式

```bash
# 开发模式 (热重载)
npm run dev

# 类型检查
npm run type-check

# 运行测试
npm run test
```

## MCP 工具列表

### 认证工具
- `hitrip_get_token` - 获取访问令牌

### 基础数据工具  
- `hitrip_get_cities` - 查询城市列表
- `hitrip_get_airports` - 查询机场信息
- `hitrip_get_vehicle_types` - 查询车型信息

### 报价工具
- `hitrip_pickup_quote` - 接机报价
- `hitrip_dropoff_quote` - 送机报价  
- `hitrip_charter_quote` - 包车报价

### 预订工具
- `hitrip_create_pickup_order` - 创建接机订单
- `hitrip_create_dropoff_order` - 创建送机订单
- `hitrip_create_charter_order` - 创建包车订单

### 订单管理工具
- `hitrip_get_order_list` - 订单列表
- `hitrip_get_order_detail` - 订单详情
- `hitrip_cancel_order` - 取消订单

## 配置说明

详见 `.env.example` 文件中的配置项说明。

## API 文档

详细的API映射和使用说明请参考 `docs/` 目录。

## 许可证

MIT License