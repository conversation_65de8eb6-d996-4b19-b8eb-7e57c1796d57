# Hitrip MCP Server

黄包车 Open API 的 MCP (Model Context Protocol) 服务器实现，为 AI 助手提供旅游预订服务能力。

## 功能特性

- 🚗 **接送机服务** - 机场接送预订和报价
- 🚌 **包车服务** - 日租包车预订和报价  
- 🏙️ **目的地查询** - 城市、机场、车型信息查询
- 📋 **订单管理** - 订单创建、查询、取消
- 🔐 **安全认证** - 支持签名验证和DES加密
- 💾 **智能缓存** - Token自动管理和数据缓存
- 📊 **完整日志** - 结构化日志记录

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 配置环境变量

```bash
cp .env.example .env
# 编辑 .env 文件，填入黄包车API配置
```

**测试环境配置示例：**
```env
HITRIP_CID=1086910840
HITRIP_SECRET_KEY=12345678
HITRIP_BASE_URL=https://api-gw.test.huangbaoche.com/
```

### 3. 构建项目

```bash
npm run build
```

### 4. 启动服务

#### STDIO 模式 (本地开发和 MCP 客户端集成)
```bash
npm run start:stdio
```

#### HTTP 模式 (在线服务)
```bash
# 使用启动脚本 (推荐)
./scripts/start-http-server.sh

# 或手动启动
export PORT="8080"
npm run start:http
```

#### SSE 模式 (暂未完全实现)
```bash
# npm run start:sse
```

## 开发模式

```bash
# STDIO 开发模式 (热重载)
npm run dev

# HTTP 开发模式
npm run dev:http

# 类型检查
npm run type-check

# 运行测试
npm run test
```

## 🧪 测试

### HTTP 服务器测试
```bash
# 完整的 HTTP 服务器测试
./scripts/test-http-server.sh

# WebSocket 连接测试
node scripts/test-websocket.js
```

### STDIO 模式测试
```bash
# 测试接机报价
node scripts/test-pickup-quote.js

# 测试送机报价
node scripts/test-dropoff-quote.js

# 测试包车报价
node scripts/test-charter-quote-call.js
```

## 🌐 HTTP API 端点

当运行在 HTTP 模式时，服务器提供以下端点：

### 基础端点
- `GET /` - 服务信息
- `GET /health` - 健康检查
- `GET /tools` - 获取工具列表

### MCP 协议端点
- `POST /mcp` - MCP JSON-RPC 协议端点
- `WebSocket /ws` - MCP WebSocket 连接

### 工具调用端点
- `POST /tools/{toolName}` - 直接调用指定工具

详细的 HTTP 部署指南请参考：[HTTP_DEPLOYMENT.md](docs/HTTP_DEPLOYMENT.md)

## MCP 工具列表

### 认证工具
- `hitrip_get_token` - 获取访问令牌

### 基础数据工具  
- `hitrip_get_cities` - 查询城市列表
- `hitrip_get_airports` - 查询机场信息
- `hitrip_get_vehicle_types` - 查询车型信息

### 报价工具
- `hitrip_pickup_quote` - 接机报价
- `hitrip_dropoff_quote` - 送机报价  
- `hitrip_charter_quote` - 包车报价

### 预订工具
- `hitrip_create_pickup_order` - 创建接机订单
- `hitrip_create_dropoff_order` - 创建送机订单
- `hitrip_create_charter_order` - 创建包车订单

### 订单管理工具
- `hitrip_get_order_list` - 订单列表
- `hitrip_get_order_detail` - 订单详情
- `hitrip_cancel_order` - 取消订单

## 项目状态

### ✅ 已完成
- MCP 服务器框架完整实现
- 13个工具的完整定义和接口
- 加密签名算法实现
- 配置管理和日志系统
- 基础功能测试

### ⚠️ 注意事项
- API 端点路径可能需要根据实际文档调整
- DES 加密使用了简化实现
- 需要有效的黄包车 API 凭据进行测试

## 配置说明

详见 `.env.example` 文件中的配置项说明。

## 文档

- `docs/api-mapping.md` - API 映射文档
- `docs/usage-guide.md` - 使用指南
- `PROJECT_SUMMARY.md` - 项目总结

## 许可证

MIT License