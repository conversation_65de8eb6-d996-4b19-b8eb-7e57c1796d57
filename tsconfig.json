{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "Node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "outDir": "./dist", "rootDir": "./src", "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "resolveJsonModule": true, "isolatedModules": true, "noEmitOnError": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "tests"]}