import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { SSEServerTransport } from '@modelcontextprotocol/sdk/server/sse.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
  Tool,
} from '@modelcontextprotocol/sdk/types.js';
import { z } from 'zod';

import { config } from '../config/index.js';
import { logger } from '../utils/logger.js';
import { HitripApiClient } from '../api/client.js';
import { createHitripTools } from '../tools/index.js';

export class HitripMcpServer {
  private server: Server;
  private apiClient: HitripApiClient;
  private tools: Tool[];

  constructor() {
    this.server = new Server(
      {
        name: 'hitrip-mcp-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.apiClient = new HitripApiClient(config);
    this.tools = createHitripTools(this.apiClient);
    
    this.setupHandlers();
  }

  private setupHandlers(): void {
    // 列出可用工具
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      logger.debug('Listing available tools', { count: this.tools.length });
      return {
        tools: this.tools,
      };
    });

    // 执行工具调用
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;
      
      logger.info('Tool call received', { 
        tool: name, 
        args: Object.keys(args || {}) 
      });

      try {
        // 查找对应的工具
        const tool = this.tools.find(t => t.name === name);
        if (!tool) {
          throw new Error(`Unknown tool: ${name}`);
        }

        // 执行工具逻辑
        const result = await this.executeTool(name, args || {});
        
        logger.info('Tool call completed', { 
          tool: name, 
          success: true 
        });

        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(result, null, 2),
            },
          ],
        };
      } catch (error) {
        logger.error('Tool call failed', { 
          tool: name, 
          error: error instanceof Error ? error.message : String(error) 
        });

        return {
          content: [
            {
              type: 'text',
              text: `Error: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
          isError: true,
        };
      }
    });
  }

  private async executeTool(name: string, args: Record<string, any>): Promise<any> {
    switch (name) {
      // 认证工具
      case 'hitrip_get_token':
        return await this.apiClient.getToken();

      // 基础数据工具
      case 'hitrip_get_cities':
        return await this.apiClient.getCities(args);

      case 'hitrip_get_airports':
        return await this.apiClient.getAirports(args);

      case 'hitrip_get_vehicle_types':
        return await this.apiClient.getVehicleTypes(args);

      // 报价工具
      case 'hitrip_pickup_quote':
        return await this.apiClient.getPickupQuote(args);

      case 'hitrip_dropoff_quote':
        return await this.apiClient.getDropoffQuote(args);

      case 'hitrip_charter_quote':
        return await this.apiClient.getCharterQuote(args);

      // 预订工具
      case 'hitrip_create_pickup_order':
        return await this.apiClient.createPickupOrder(args);

      case 'hitrip_create_dropoff_order':
        return await this.apiClient.createDropoffOrder(args);

      case 'hitrip_create_charter_order':
        return await this.apiClient.createCharterOrder(args);

      // 订单管理工具
      case 'hitrip_get_order_list':
        return await this.apiClient.getOrderList(args);

      case 'hitrip_get_order_detail':
        return await this.apiClient.getOrderDetail(args);

      case 'hitrip_cancel_order':
        return await this.apiClient.cancelOrder(args);

      default:
        throw new Error(`Tool not implemented: ${name}`);
    }
  }

  async start(mode: 'stdio' | 'sse' = 'stdio'): Promise<void> {
    if (mode === 'stdio') {
      const transport = new StdioServerTransport();
      await this.server.connect(transport);
      logger.info('MCP Server started in STDIO mode');
    } else {
      // TODO: 实现SSE模式
      throw new Error('SSE mode not implemented yet. Please use STDIO mode.');
    }
  }

  async stop(): Promise<void> {
    await this.server.close();
    logger.info('MCP Server stopped');
  }
}
