export interface HitripConfig {
  // API配置
  baseUrl: string;
  cid: string;
  secretKey: string;
  
  // 环境配置
  environment: 'development' | 'test' | 'production';
  
  // 服务配置
  serverMode: 'stdio' | 'sse';
  port?: number;
  
  // 缓存配置
  cache: {
    enabled: boolean;
    tokenTtl: number;
  };
  
  // 日志配置
  logging: {
    level: 'error' | 'warn' | 'info' | 'debug';
    file?: string;
  };
  
  // 回调配置
  webhook?: {
    domain: string;
    endpoints: {
      confirmOrder?: string;
      pushDriver?: string;
      orderComplete?: string;
    };
  };
}