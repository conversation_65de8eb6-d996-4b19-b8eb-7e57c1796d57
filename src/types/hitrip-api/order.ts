import { AddressInfo } from './common.js';

// 订单状态枚举
export enum OrderStatus {
  PENDING = 'pending',           // 待确认
  CONFIRMED = 'confirmed',       // 已确认
  ASSIGNED = 'assigned',         // 已派单
  IN_PROGRESS = 'in_progress',   // 进行中
  COMPLETED = 'completed',       // 已完成
  CANCELLED = 'cancelled',       // 已取消
  REFUNDED = 'refunded',         // 已退款
}

// 订单类型枚举
export enum OrderType {
  PICKUP = 'pickup',     // 接机
  DROPOFF = 'dropoff',   // 送机
  CHARTER = 'charter',   // 包车
}

// 订单基础信息
export interface BaseOrderRequest {
  vehicleTypeId: number;
  contactName: string;
  contactPhone: string;
  guestNum?: number;
  luggageNum?: number;
  remark?: string;
  specialRequirements?: string;
}

// 接机订单请求
export interface PickupOrderRequest {
  // 航班信息
  flightNo: string; // 航班编号
  flightDestCode: string; // 降落机场三字码
  flightDestAirportName: string; // 降落机场名称
  flightDestBuilding?: string; // 降落机场航站楼
  flightArriveTime?: string; // 航班计划到达时间
  flightFlyTime?: string; // 航班起飞时间
  flightAirportCode?: string; // 起飞机场三字码
  flightAirportName?: string; // 起飞机场名称

  // 订单信息
  orderThirdNo: string; // 第三方订单号
  priceActual: number; // 实际支付价格，单位：分
  priceChannel: number; // 渠道价格，单位：分

  // 服务信息
  serviceCarModel: string; // 车型ID
  serviceCarModelName: string; // 车型名称
  serviceCityId: number; // 服务城市ID
  serviceCityName: string; // 服务城市名称
  serviceTime: string; // 服务时间

  // 地址信息
  serviceStartAddress: string; // 出发地（机场地址）
  serviceStartAddressDetail: string; // 出发地详细地址
  serviceStartPoint: string; // 出发地经纬度
  serviceDestAddress: string; // 终止地（目的地地址）
  serviceDestAddressDetail: string; // 终止地详细地址
  serviceDestPoint: string; // 终止地经纬度

  // 乘客信息
  servicePassagerName: string; // 乘车人名称
  servicePassagerMobile: string; // 乘车人手机号码
  servicePassagerAreacode: string; // 乘车人手机区号

  // 商户信息
  orderChannel: string; // 商户ID
  orderChannelName: string; // 商户名称

  // 报价参数（必需）
  airportPriceQuestParam: {
    airportCode: string; // 机场三字码
    airportName: string; // 机场名称
    channelId: number; // 渠道ID
    endAddress: string; // 目的地地址
    endDetailAddress: string; // 目的地详细地址
    endLocation: string; // 目的地坐标点
    serviceCityId: number; // 服务城市ID
    serviceCityName: string; // 服务城市名称
    serviceTime: string; // 服务时间
    startAddress: string; // 起始地址
    startCityId: number; // 起始城市ID
    startCityName: string; // 起始城市名称
    startDetailAddress: string; // 起始详细地址
    startLocation: string; // 起始坐标点
  };

  // 可选信息
  userRemark?: string; // 用户留言
  serviceStartPoi?: string; // POI起点ID
  serviceDestPoi?: string; // POI终点ID
  serviceEndCityId?: number; // 终止地城市ID
  serviceEndCityName?: string; // 终止地城市名
  serviceContinentId?: number; // 服务大洲ID
  serviceContinentName?: string; // 服务大洲名称
  serviceCountryId?: number; // 服务国家ID
  serviceCountryName?: string; // 服务国家名称
}

// 送机订单请求
export interface DropoffOrderRequest {
  // 航班信息
  flightNo?: string; // 航班编号
  flightDestCode: string; // 降落机场三字码
  flightDestAirportName: string; // 降落机场名称
  flightDestBuilding?: string; // 降落机场航站楼
  flightArriveTime?: string; // 航班计划到达时间
  flightFlyTime?: string; // 航班起飞时间
  flightAirportCode?: string; // 起飞机场三字码
  flightAirportName?: string; // 起飞机场名称

  // 订单信息
  orderNo: string; // 订单号
  orderThirdNo: string; // 第三方订单号
  priceActual: number; // 实际支付价格，单位：分
  priceChannel: number; // 渠道价格，单位：分

  // 服务信息
  serviceCarModel: string; // 车型ID
  serviceCarModelName: string; // 车型名称
  serviceCityId: number; // 服务城市ID
  serviceCityName: string; // 服务城市名称
  serviceTime: string; // 服务时间

  // 地址信息
  serviceStartAddress: string; // 出发地地址
  serviceStartAddressDetail: string; // 出发地详细地址
  serviceStartPoint: string; // 出发地经纬度
  serviceDestAddress: string; // 终止地（机场地址）
  serviceDestAddressDetail: string; // 终止地详细地址
  serviceDestPoint: string; // 终止地经纬度

  // 乘客信息
  servicePassagerName: string; // 乘车人名称
  servicePassagerMobile: string; // 乘车人手机号码
  servicePassagerAreacode: string; // 乘车人手机区号

  // 商户信息
  orderChannel: string; // 商户ID
  orderChannelName: string; // 商户名称

  // 报价参数（必需）
  airportPriceQuestParam: {
    airportCode: string; // 机场三字码
    airportName: string; // 机场名称
    channelId: number; // 渠道ID
    endAddress: string; // 目的地地址
    endDetailAddress: string; // 目的地详细地址
    endLocation: string; // 目的地坐标点
    serviceCityId: number; // 服务城市ID
    serviceCityName: string; // 服务城市名称
    serviceTime: string; // 服务时间
    startAddress: string; // 起始地址
    startCityId: number; // 起始城市ID
    startCityName: string; // 起始城市名称
    startDetailAddress: string; // 起始详细地址
    startLocation: string; // 起始坐标点
  };

  // 可选信息
  userRemark?: string; // 用户留言
  serviceStartPoi?: string; // POI起点ID
  serviceDestPoi?: string; // POI终点ID
  serviceEndCityId?: number; // 终止地城市ID
  serviceEndCityName?: string; // 终止地城市名
  serviceContinentId?: number; // 服务大洲ID
  serviceContinentName?: string; // 服务大洲名称
  serviceCountryId?: number; // 服务国家ID
  serviceCountryName?: string; // 服务国家名称
}

// 包车订单请求（简化版本）
export interface CharterOrderRequest {
  // 订单基础信息
  orderNoThird: string; // 第三方订单号
  travelStartTimeLocal: string; // 行程开始时间（当地）
  travelEndTimeLocal: string; // 行程结束时间（当地）
  travelStartTimeBeijing: string; // 行程开始时间（北京）
  travelEndTimeBeijing: string; // 行程结束时间（北京）
  totalDay: number; // 本次行程的总天数

  // 城市信息
  startCityId: number; // 出发城市ID
  startCityName?: string; // 出发城市名称
  endCityId: number; // 结束城市ID
  endCityName?: string; // 结束城市名称

  // 价格信息
  amountChannelSys: number; // 渠道上的系统建议卖价，单位：分
  amountShouldPay: number; // 总应付金额，单位：分
  amountShouldReceive: number; // 总应收金额，单位：分
  amountActualPay: number; // 总实付金额，单位：分
  amountActualReceive: number; // 总实收金额，单位：分
  amountCouponTotal: number; // 总渠道优惠金额，单位：分

  // 联系人信息
  customerName: string; // 主联系人姓名
  customerAreaCode: string; // 主联系人手机区号
  customerMobile: string; // 主联系人手机号
  customerWechat?: string; // 主联系人微信号

  // 支付信息
  payTime: string; // 支付时间
  thirdPayNo?: string; // 第三方支付单号

  // 其他信息
  priceMarkCity: number; // 报价城市ID
  remark?: string; // 订单备注

  // 每日行程（简化为单日）
  travelDailyBaseReq: {
    dayIndex: number; // 第几天，从1开始
    goodsType: number; // 商品细分类型
    goodsTypeName: string; // 商品细分类型名称
    serviceStartTimeLocal: string; // 服务开始时间（当地）
    serviceEndTimeLocal: string; // 服务结束时间（当地）
    serviceStartTimeBeijing: string; // 服务开始时间（北京）
    serviceEndTimeBeijing: string; // 服务结束时间（北京）
    carModelId: number; // 车型ID
    carModelName: string; // 车型名称
    startCityId: number; // 出发城市ID
    startCityName: string; // 出发城市名称
    endCityId: number; // 结束城市ID
    endCityName: string; // 结束城市名称
    serviceMaxTime: number; // 服务最大时间（小时）
    serviceMaxDistance: number; // 服务最大距离
  };
}

// 订单响应
export interface OrderResponse {
  orderId: string;
  orderType: OrderType;
  orderStatus: OrderStatus;
  
  // 基础信息
  vehicleTypeId: number;
  vehicleTypeName: string;
  contactName: string;
  contactPhone: string;
  guestNum: number;
  luggageNum?: number;
  
  // 服务信息
  serviceDate: string;
  flightNo?: string;
  flightDate?: string;
  airportCode?: string;
  origin?: AddressInfo;
  destination?: AddressInfo;
  serviceHours?: number;
  
  // 价格信息
  totalPrice: number;
  currency: string;
  
  // 司机信息
  driver?: DriverInfo;
  
  // 车辆信息
  vehicle?: VehicleInfo;
  
  // 时间信息
  createdAt: string;
  updatedAt: string;
  confirmedAt?: string;
  completedAt?: string;
  cancelledAt?: string;
  
  // 其他信息
  remark?: string;
  specialRequirements?: string;
  cancelReason?: string;
}

// 司机信息
export interface DriverInfo {
  driverId: string;
  driverName: string;
  driverPhone: string;
  driverPhoto?: string;
  rating?: number;
  vehiclePlateNumber?: string;
}

// 车辆信息
export interface VehicleInfo {
  vehicleId: string;
  plateNumber: string;
  brand: string;
  model: string;
  color: string;
  year?: number;
  photos?: string[];
}

// 订单列表请求
export interface OrderListRequest {
  limit: number; // 每页记录数
  offset: number; // 偏移量
  listType: number; // 列表类型：1-全部；2-待支付；3-服务中；4-已取消
  userId: string; // 用户ID
}

// 订单列表响应
export interface OrderListResponse {
  orders: OrderResponse[];
  totalCount: number;
  pageNum: number;
  pageSize: number;
  totalPages: number;
}

// 取消订单请求
export interface CancelOrderRequest {
  orderTravelNo: string; // 订单号
  cancelReason: string; // 取消原因
  cancelType: number; // 取消类型（1:用户原因 2:工作室原因 3:平台原因 4:不可抗力）
  cancelRemark?: string; // 备注（可选）
  actuallyPaidPrice?: number; // 实付金额（可选）
}

// 订单详情请求
export interface OrderDetailRequest {
  orderTravelNo?: string; // 订单号（与orderNoThird二选一）
  orderNoThird?: string; // 第三方订单号（与orderTravelNo二选一）
}
