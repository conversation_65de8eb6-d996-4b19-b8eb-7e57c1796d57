// API 响应基础结构
export interface HitripApiResponse<T = any> {
  status: number;
  success: boolean;
  data: T;
  message?: string;
  traceId?: string;
}

// 城市信息
export interface CityInfo {
  cityId: number;
  cityName: string;
  countryId: number;
  countryName: string;
  pinyin?: string;
  pinyinShort?: string;
}

// 机场信息
export interface AirportInfo {
  airportCode: string;
  airportName: string;
  cityId: number;
  cityName: string;
  countryId: number;
  countryName: string;
}

// 车型信息
export interface VehicleType {
  id: number;
  vehicleTypeName: string;
  vehicleTypeFullName: string;
  vehicleTypeRank: number;
  vehicleTypeRankName: string;
  seatNum: number;
  guestNum: number;
  luggageNum: number;
  pics?: string;
  countryVos: Array<{
    countryId: number;
    countryName: string;
  }>;
}

// 地址信息
export interface AddressInfo {
  address: string;
  addressDetail?: string;
  location?: string;
  poiId?: string;
}

// 公共请求参数
export interface CommonRequestParams {
  cid: string;
  timestamp: string;
  sign: string;
}