import { AddressInfo } from './common.js';

// 报价请求基础参数
export interface BaseQuoteRequest {
  vehicleTypeId: number;
  guestNum?: number;
  luggageNum?: number;
}

// 接机报价请求
export interface PickupQuoteRequest extends BaseQuoteRequest {
  flightNo: string;
  flightDate: string;
  airportCode: string;
  destination: AddressInfo;
}

// 送机报价请求
export interface DropoffQuoteRequest extends BaseQuoteRequest {
  flightNo: string;
  flightDate: string;
  airportCode: string;
  origin: AddressInfo;
}

// 包车报价请求
export interface CharterQuoteRequest extends BaseQuoteRequest {
  serviceTime: string; // 服务时间，格式：YYYY-MM-DD HH:mm:ss
  origin: CharterAddressInfo;
  destination: CharterAddressInfo;
  serviceHours?: number;
  tourType?: number; // 旅游类型，默认为1
  distance?: number; // 预估距离（米）
  duration?: number; // 预估时长（分钟）
}

// 包车地址信息（比普通地址信息多了城市ID和名称）
export interface CharterAddressInfo extends AddressInfo {
  cityId?: number;
  cityName?: string;
}

// 报价响应
export interface QuoteResponse {
  quoteId: string;
  vehicleTypeId: number;
  vehicleTypeName: string;
  totalPrice: number;
  currency: string;
  priceDetails: PriceDetail[];
  validUntil: string;
  estimatedDuration?: number;
  estimatedDistance?: number;
  remarks?: string;
}

// 价格明细
export interface PriceDetail {
  itemName: string;
  itemType: 'base' | 'extra' | 'tax' | 'fee';
  amount: number;
  currency: string;
  description?: string;
}

// 报价列表响应
export interface QuoteListResponse {
  quotes: QuoteResponse[];
  totalCount: number;
  recommendedQuoteId?: string;
}
