#!/usr/bin/env node

/**
 * 调试版MCP启动器，包含详细日志记录
 */

import fs from 'fs';
import path from 'path';
import { config, validateConfig } from './config/index.js';
import { HitripMcpServer } from './server/mcp-server.js';

// 创建日志目录
const logDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

// 创建日志文件
const logFile = path.join(logDir, `mcp-debug-${new Date().toISOString().slice(0, 10)}.log`);
const logStream = fs.createWriteStream(logFile, { flags: 'a' });

// 日志函数
function writeLog(level: string, message: string, data?: any) {
  const timestamp = new Date().toISOString();
  const logEntry = {
    timestamp,
    level,
    message,
    data,
    pid: process.pid
  };
  
  // 写入文件
  logStream.write(JSON.stringify(logEntry) + '\n');
  
  // 写入stderr（不影响MCP通信）
  console.error(`[${timestamp}] ${level}: ${message}`, data ? JSON.stringify(data) : '');
}

// 设置进程标题
process.title = 'hitrip-mcp-debug';

// 记录启动信息
writeLog('INFO', 'Starting Hitrip MCP Server (Debug Mode)');
writeLog('INFO', 'Node.js version', { version: process.version });
writeLog('INFO', 'Working directory', { cwd: process.cwd() });
writeLog('INFO', 'Log file', { path: logFile });

// 监听所有进程事件
process.on('uncaughtException', (error) => {
  writeLog('ERROR', 'Uncaught Exception', { 
    message: error.message, 
    stack: error.stack 
  });
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  writeLog('ERROR', 'Unhandled Rejection', { 
    reason: String(reason), 
    promise: String(promise) 
  });
  process.exit(1);
});

process.on('SIGINT', () => {
  writeLog('INFO', 'Received SIGINT, shutting down');
  logStream.end();
  process.exit(0);
});

process.on('SIGTERM', () => {
  writeLog('INFO', 'Received SIGTERM, shutting down');
  logStream.end();
  process.exit(0);
});

// 监听stdin数据（MCP消息）
let messageCount = 0;
process.stdin.on('data', (data) => {
  messageCount++;
  const message = data.toString().trim();
  writeLog('DEBUG', 'Received MCP message', { 
    messageId: messageCount,
    length: message.length,
    message: message.substring(0, 500) // 只记录前500字符
  });
  
  try {
    const parsed = JSON.parse(message);
    writeLog('DEBUG', 'Parsed MCP message', {
      messageId: messageCount,
      method: parsed.method,
      id: parsed.id,
      hasParams: !!parsed.params
    });
  } catch (error) {
    writeLog('WARN', 'Failed to parse MCP message', {
      messageId: messageCount,
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

// 监听stdout写入（MCP响应）
const originalWrite = process.stdout.write;
process.stdout.write = function(chunk: any, encoding?: any, callback?: any) {
  if (typeof chunk === 'string') {
    try {
      const parsed = JSON.parse(chunk);
      writeLog('DEBUG', 'Sending MCP response', {
        id: parsed.id,
        hasResult: !!parsed.result,
        hasError: !!parsed.error,
        method: parsed.method
      });
    } catch (error) {
      writeLog('DEBUG', 'Sending non-JSON response', { 
        length: chunk.length,
        content: chunk.substring(0, 200)
      });
    }
  }
  
  return originalWrite.call(this, chunk, encoding, callback);
};

async function main() {
  try {
    writeLog('INFO', 'Validating configuration');
    
    // 记录环境变量（不包含敏感信息）
    writeLog('DEBUG', 'Environment variables', {
      NODE_ENV: process.env.NODE_ENV,
      LOG_LEVEL: process.env.LOG_LEVEL,
      HITRIP_BASE_URL: process.env.HITRIP_BASE_URL,
      HITRIP_CID: process.env.HITRIP_CID ? '***' : 'not set',
      HITRIP_SECRET_KEY: process.env.HITRIP_SECRET_KEY ? '***' : 'not set'
    });
    
    validateConfig();
    writeLog('INFO', 'Configuration validated successfully');
    
    writeLog('INFO', 'Creating MCP server instance');
    const server = new HitripMcpServer();
    
    writeLog('INFO', 'Starting MCP server in STDIO mode');
    await server.start('stdio');
    
    writeLog('INFO', 'Hitrip MCP Server started successfully');
    
  } catch (error) {
    writeLog('ERROR', 'Failed to start Hitrip MCP Server', {
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });
    process.exit(1);
  }
}

// 启动服务器
main();
