#!/usr/bin/env node

import { config, validateConfig } from './config/index.js';
import { logger } from './utils/logger.js';
import { HitripMcpServer } from './server/mcp-server.js';

async function main() {
  try {
    // 验证配置
    validateConfig();
    
    logger.info('Starting Hitrip MCP Server', {
      mode: config.serverMode,
      environment: config.environment,
      baseUrl: config.baseUrl,
    });

    // 创建并启动服务器
    const server = new HitripMcpServer();
    await server.start(config.serverMode);
    
    logger.info('Hitrip MCP Server started successfully');
    
  } catch (error) {
    logger.error('Failed to start Hitrip MCP Server', { error });
    process.exit(1);
  }
}

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception', { error });
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection', { reason, promise });
  process.exit(1);
});

// 优雅关闭
process.on('SIGINT', () => {
  logger.info('Received SIGINT, shutting down gracefully');
  process.exit(0);
});

process.on('SIGTERM', () => {
  logger.info('Received SIGTERM, shutting down gracefully');
  process.exit(0);
});

// 启动应用
main();