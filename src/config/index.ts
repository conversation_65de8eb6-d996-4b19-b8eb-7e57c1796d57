import { HitripConfig } from '../types/config.js';

function getEnvVar(key: string, defaultValue?: string): string {
  const value = process.env[key];
  if (!value && !defaultValue) {
    throw new Error(`Environment variable ${key} is required`);
  }
  return value || defaultValue!;
}

function getEnvNumber(key: string, defaultValue: number): number {
  const value = process.env[key];
  return value ? parseInt(value, 10) : defaultValue;
}

function getEnvBoolean(key: string, defaultValue: boolean): boolean {
  const value = process.env[key];
  return value ? value.toLowerCase() === 'true' : defaultValue;
}

export const config: HitripConfig = {
  // API配置
  baseUrl: getEnvVar('HITRIP_BASE_URL', 'https://api-gw.test.huangbaoche.com/'),
  cid: getEnvVar('HITRIP_CID'),
  secretKey: getEnvVar('HITRIP_SECRET_KEY'),
  
  // 环境配置
  environment: (process.env.NODE_ENV as any) || 'development',
  
  // 服务配置
  serverMode: (process.env.SERVER_MODE as 'stdio' | 'sse') || 'stdio',
  port: getEnvNumber('PORT', 3000),
  
  // 缓存配置
  cache: {
    enabled: getEnvBoolean('CACHE_ENABLED', true),
    tokenTtl: getEnvNumber('TOKEN_CACHE_TTL', 3600), // 1小时
  },
  
  // 日志配置
  logging: {
    level: (process.env.LOG_LEVEL as any) || 'info',
    file: process.env.LOG_FILE,
  },
  
  // 回调配置
  webhook: process.env.WEBHOOK_DOMAIN ? {
    domain: process.env.WEBHOOK_DOMAIN,
    endpoints: {
      confirmOrder: process.env.WEBHOOK_CONFIRM_ORDER,
      pushDriver: process.env.WEBHOOK_PUSH_DRIVER,
      orderComplete: process.env.WEBHOOK_ORDER_COMPLETE,
    }
  } : undefined,
};

// 验证配置
export function validateConfig(): void {
  if (!config.cid) {
    throw new Error('HITRIP_CID is required');
  }
  
  if (!config.secretKey) {
    throw new Error('HITRIP_SECRET_KEY is required');
  }
  
  if (config.serverMode === 'sse' && !config.port) {
    throw new Error('PORT is required for SSE mode');
  }
}