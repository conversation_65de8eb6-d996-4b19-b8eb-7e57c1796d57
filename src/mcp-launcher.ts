#!/usr/bin/env node

/**
 * 简化的MCP启动器，专门用于MCP客户端集成
 */

import { config, validateConfig } from './config/index.js';
import { logger } from './utils/logger.js';
import { HitripMcpServer } from './server/mcp-server.js';

// 设置进程标题
process.title = 'hitrip-mcp-server';

// 错误处理
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error.message);
  process.exit(1);
});

process.on('unhandledRejection', (reason) => {
  console.error('Unhandled Rejection:', reason);
  process.exit(1);
});

// 优雅关闭
process.on('SIGINT', () => {
  console.error('Received SIGINT, shutting down');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.error('Received SIGTERM, shutting down');
  process.exit(0);
});

async function main() {
  try {
    // 输出启动信息到stderr（不影响MCP通信）
    console.error('Starting Hitrip MCP Server...');
    console.error(`Node.js version: ${process.version}`);
    console.error(`Working directory: ${process.cwd()}`);
    
    // 验证配置
    validateConfig();
    console.error('Configuration validated');
    
    // 创建并启动服务器
    const server = new HitripMcpServer();
    await server.start('stdio');
    
    console.error('Hitrip MCP Server started successfully');
    
  } catch (error) {
    console.error('Failed to start Hitrip MCP Server:', error instanceof Error ? error.message : String(error));
    process.exit(1);
  }
}

// 启动服务器
main();
