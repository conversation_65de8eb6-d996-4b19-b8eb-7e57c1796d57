import { describe, it, expect, beforeEach, vi } from 'vitest';
import axios from 'axios';
import { HitripApiClient } from '../api/client.js';
import { HitripConfig } from '../types/config.js';

// Mock config module to avoid environment variable requirements
vi.mock('../config/index.js', () => ({
  config: {
    baseUrl: 'https://api-gw.test.huangbaoche.com/',
    cid: '1086910840',
    secretKey: '12345678',
    environment: 'test',
    serverMode: 'stdio',
    cache: { enabled: true, tokenTtl: 3600 },
    logging: { level: 'info' },
  },
}));

// Mock axios
vi.mock('axios', () => ({
  default: {
    create: vi.fn(),
  },
}));
const mockedAxios = vi.mocked(axios);

describe('HitripApiClient', () => {
  let client: HitripApiClient;
  let mockConfig: HitripConfig;

  beforeEach(() => {
    mockConfig = {
      baseUrl: 'https://api-gw.test.huangbaoche.com/',
      cid: 'test_cid',
      secretKey: 'test_secret',
      environment: 'test',
      serverMode: 'stdio',
      cache: {
        enabled: true,
        tokenTtl: 3600,
      },
      logging: {
        level: 'info',
      },
    };

    // Mock axios.create
    const mockAxiosInstance = {
      post: vi.fn(),
      interceptors: {
        request: { use: vi.fn() },
        response: { use: vi.fn() },
      },
    };

    (mockedAxios.create as any).mockReturnValue(mockAxiosInstance);
    
    client = new HitripApiClient(mockConfig);
  });

  describe('getToken', () => {
    it('should fetch and return token successfully', async () => {
      const mockResponse = {
        data: {
          success: true,
          data: 'mock_token_123',
          status: 200,
        },
      };

      const mockAxiosInstance = mockedAxios.create();
      vi.mocked(mockAxiosInstance.post).mockResolvedValue(mockResponse);

      const token = await client.getToken();
      
      expect(token).toBe('mock_token_123');
      expect(mockAxiosInstance.post).toHaveBeenCalledWith(
        '/getToken',
        expect.objectContaining({
          cid: 'test_cid',
          timestamp: expect.any(String),
          sign: expect.any(String),
        })
      );
    });

    it('should throw error when token request fails', async () => {
      const mockResponse = {
        data: {
          success: false,
          status: 400,
          message: 'Invalid credentials',
        },
      };

      const mockAxiosInstance = mockedAxios.create();
      vi.mocked(mockAxiosInstance.post).mockResolvedValue(mockResponse);

      await expect(client.getToken()).rejects.toThrow('Token request failed: 400');
    });
  });

  describe('getCities', () => {
    it('should fetch cities successfully', async () => {
      const mockCities = [
        {
          cityId: 1,
          cityName: 'Beijing',
          countryId: 1,
          countryName: 'China',
        },
      ];

      const mockResponse = {
        data: {
          success: true,
          data: mockCities,
        },
      };

      const mockAxiosInstance = mockedAxios.create();
      vi.mocked(mockAxiosInstance.post).mockResolvedValue(mockResponse);

      const cities = await client.getCities();
      
      expect(cities).toEqual(mockCities);
      expect(mockAxiosInstance.post).toHaveBeenCalledWith(
        '/getCityList',
        expect.objectContaining({
          cid: 'test_cid',
          timestamp: expect.any(String),
          sign: expect.any(String),
        })
      );
    });

    it('should fetch cities with country filter', async () => {
      const mockCities = [
        {
          cityId: 1,
          cityName: 'Beijing',
          countryId: 1,
          countryName: 'China',
        },
      ];

      const mockResponse = {
        data: {
          success: true,
          data: mockCities,
        },
      };

      const mockAxiosInstance = mockedAxios.create();
      vi.mocked(mockAxiosInstance.post).mockResolvedValue(mockResponse);

      const cities = await client.getCities({ countryId: 1 });
      
      expect(cities).toEqual(mockCities);
      expect(mockAxiosInstance.post).toHaveBeenCalledWith(
        '/getCityList',
        expect.objectContaining({
          cid: 'test_cid',
          countryId: 1,
          timestamp: expect.any(String),
          sign: expect.any(String),
        })
      );
    });
  });

  describe('getAirports', () => {
    it('should fetch airports successfully', async () => {
      const mockAirports = [
        {
          airportCode: 'PEK',
          airportName: 'Beijing Capital International Airport',
          cityId: 1,
          cityName: 'Beijing',
          countryId: 1,
          countryName: 'China',
        },
      ];

      const mockResponse = {
        data: {
          success: true,
          data: mockAirports,
        },
      };

      const mockAxiosInstance = mockedAxios.create();
      vi.mocked(mockAxiosInstance.post).mockResolvedValue(mockResponse);

      const airports = await client.getAirports();
      
      expect(airports).toEqual(mockAirports);
    });
  });

  describe('getVehicleTypes', () => {
    it('should fetch vehicle types successfully', async () => {
      const mockVehicleTypes = [
        {
          id: 1,
          vehicleTypeName: 'Economy',
          vehicleTypeFullName: 'Economy Car',
          vehicleTypeRank: 1,
          vehicleTypeRankName: 'Standard',
          seatNum: 4,
          guestNum: 3,
          luggageNum: 2,
          countryVos: [
            {
              countryId: 1,
              countryName: 'China',
            },
          ],
        },
      ];

      const mockResponse = {
        data: {
          success: true,
          data: mockVehicleTypes,
        },
      };

      const mockAxiosInstance = mockedAxios.create();
      vi.mocked(mockAxiosInstance.post).mockResolvedValue(mockResponse);

      const vehicleTypes = await client.getVehicleTypes();
      
      expect(vehicleTypes).toEqual(mockVehicleTypes);
    });
  });

  describe('error handling', () => {
    it('should handle network errors', async () => {
      const mockAxiosInstance = mockedAxios.create();
      vi.mocked(mockAxiosInstance.post).mockRejectedValue(new Error('Network error'));

      await expect(client.getCities()).rejects.toThrow('Network error');
    });

    it('should handle API errors', async () => {
      const mockResponse = {
        data: {
          success: false,
          message: 'API Error',
          status: 500,
        },
      };

      const mockAxiosInstance = mockedAxios.create();
      vi.mocked(mockAxiosInstance.post).mockResolvedValue(mockResponse);

      await expect(client.getCities()).rejects.toThrow('Get cities failed: API Error');
    });
  });
});
