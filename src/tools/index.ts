import { Tool } from '@modelcontextprotocol/sdk/types.js';
import { HitripApiClient } from '../api/client.js';

export function createHitripTools(apiClient: HitripApiClient): Tool[] {
  return [
    // 认证工具
    {
      name: 'hitrip_get_token',
      description: '获取黄包车API访问令牌',
      inputSchema: {
        type: 'object',
        properties: {},
        required: [],
      },
    },

    // 基础数据工具
    {
      name: 'hitrip_get_cities',
      description: '查询城市列表',
      inputSchema: {
        type: 'object',
        properties: {
          keyword: {
            type: 'string',
            description: '搜索关键词，可选参数用于按城市名称搜索',
          },
        },
        required: [],
      },
    },

    {
      name: 'hitrip_get_airports',
      description: '查询机场信息',
      inputSchema: {
        type: 'object',
        properties: {
          cityId: {
            type: 'number',
            description: '城市ID，可选参数用于筛选特定城市的机场',
          },
          countryId: {
            type: 'number',
            description: '国家ID，可选参数用于筛选特定国家的机场',
          },
        },
        required: [],
      },
    },

    {
      name: 'hitrip_get_vehicle_types',
      description: '查询车型信息',
      inputSchema: {
        type: 'object',
        properties: {
          countryId: {
            type: 'number',
            description: '国家ID，可选参数用于筛选特定国家的车型',
          },
        },
        required: [],
      },
    },

    // 报价工具
    {
      name: 'hitrip_pickup_quote',
      description: '获取接机服务报价',
      inputSchema: {
        type: 'object',
        properties: {
          flightNo: {
            type: 'string',
            description: '航班号',
          },
          flightDate: {
            type: 'string',
            description: '航班日期，格式：YYYY-MM-DD',
          },
          airportCode: {
            type: 'string',
            description: '机场代码',
          },
          destination: {
            type: 'object',
            description: '目的地信息',
            properties: {
              address: { type: 'string', description: '目的地地址' },
              addressDetail: { type: 'string', description: '详细地址' },
              location: { type: 'string', description: '经纬度坐标' },
              poiId: { type: 'string', description: 'POI ID' },
            },
            required: ['address'],
          },
          vehicleTypeId: {
            type: 'number',
            description: '车型ID',
          },
          guestNum: {
            type: 'number',
            description: '乘客人数',
          },
          luggageNum: {
            type: 'number',
            description: '行李件数',
          },
        },
        required: ['flightNo', 'flightDate', 'airportCode', 'destination', 'vehicleTypeId'],
      },
    },

    {
      name: 'hitrip_dropoff_quote',
      description: '获取送机服务报价',
      inputSchema: {
        type: 'object',
        properties: {
          flightNo: {
            type: 'string',
            description: '航班号',
          },
          flightDate: {
            type: 'string',
            description: '航班日期，格式：YYYY-MM-DD',
          },
          airportCode: {
            type: 'string',
            description: '机场代码',
          },
          origin: {
            type: 'object',
            description: '出发地信息',
            properties: {
              address: { type: 'string', description: '出发地地址' },
              addressDetail: { type: 'string', description: '详细地址' },
              location: { type: 'string', description: '经纬度坐标' },
              poiId: { type: 'string', description: 'POI ID' },
            },
            required: ['address'],
          },
          vehicleTypeId: {
            type: 'number',
            description: '车型ID',
          },
          guestNum: {
            type: 'number',
            description: '乘客人数',
          },
          luggageNum: {
            type: 'number',
            description: '行李件数',
          },
        },
        required: ['flightNo', 'flightDate', 'airportCode', 'origin', 'vehicleTypeId'],
      },
    },

    {
      name: 'hitrip_charter_quote',
      description: '获取包车服务报价',
      inputSchema: {
        type: 'object',
        properties: {
          serviceDate: {
            type: 'string',
            description: '服务日期，格式：YYYY-MM-DD',
          },
          origin: {
            type: 'object',
            description: '出发地信息',
            properties: {
              address: { type: 'string', description: '出发地地址' },
              addressDetail: { type: 'string', description: '详细地址' },
              location: { type: 'string', description: '经纬度坐标' },
              poiId: { type: 'string', description: 'POI ID' },
            },
            required: ['address'],
          },
          destination: {
            type: 'object',
            description: '目的地信息',
            properties: {
              address: { type: 'string', description: '目的地地址' },
              addressDetail: { type: 'string', description: '详细地址' },
              location: { type: 'string', description: '经纬度坐标' },
              poiId: { type: 'string', description: 'POI ID' },
            },
            required: ['address'],
          },
          vehicleTypeId: {
            type: 'number',
            description: '车型ID',
          },
          guestNum: {
            type: 'number',
            description: '乘客人数',
          },
          serviceHours: {
            type: 'number',
            description: '服务小时数',
          },
        },
        required: ['serviceDate', 'origin', 'destination', 'vehicleTypeId'],
      },
    },

    // 预订工具
    {
      name: 'hitrip_create_pickup_order',
      description: '创建接机订单',
      inputSchema: {
        type: 'object',
        properties: {
          // 基础信息
          flightNo: { type: 'string', description: '航班号' },
          flightDate: { type: 'string', description: '航班日期，格式：YYYY-MM-DD' },
          airportCode: { type: 'string', description: '机场代码' },
          destination: {
            type: 'object',
            description: '目的地信息',
            properties: {
              address: { type: 'string', description: '目的地地址' },
              addressDetail: { type: 'string', description: '详细地址' },
              location: { type: 'string', description: '经纬度坐标' },
              poiId: { type: 'string', description: 'POI ID' },
            },
            required: ['address'],
          },
          vehicleTypeId: { type: 'number', description: '车型ID' },
          
          // 乘客信息
          contactName: { type: 'string', description: '联系人姓名' },
          contactPhone: { type: 'string', description: '联系人电话' },
          guestNum: { type: 'number', description: '乘客人数' },
          luggageNum: { type: 'number', description: '行李件数' },
          
          // 可选信息
          remark: { type: 'string', description: '备注信息' },
          specialRequirements: { type: 'string', description: '特殊要求' },
        },
        required: ['flightNo', 'flightDate', 'airportCode', 'destination', 'vehicleTypeId', 'contactName', 'contactPhone'],
      },
    },

    {
      name: 'hitrip_create_dropoff_order',
      description: '创建送机订单',
      inputSchema: {
        type: 'object',
        properties: {
          // 基础信息
          flightNo: { type: 'string', description: '航班号' },
          flightDate: { type: 'string', description: '航班日期，格式：YYYY-MM-DD' },
          airportCode: { type: 'string', description: '机场代码' },
          origin: {
            type: 'object',
            description: '出发地信息',
            properties: {
              address: { type: 'string', description: '出发地地址' },
              addressDetail: { type: 'string', description: '详细地址' },
              location: { type: 'string', description: '经纬度坐标' },
              poiId: { type: 'string', description: 'POI ID' },
            },
            required: ['address'],
          },
          vehicleTypeId: { type: 'number', description: '车型ID' },
          
          // 乘客信息
          contactName: { type: 'string', description: '联系人姓名' },
          contactPhone: { type: 'string', description: '联系人电话' },
          guestNum: { type: 'number', description: '乘客人数' },
          luggageNum: { type: 'number', description: '行李件数' },
          
          // 可选信息
          remark: { type: 'string', description: '备注信息' },
          specialRequirements: { type: 'string', description: '特殊要求' },
        },
        required: ['flightNo', 'flightDate', 'airportCode', 'origin', 'vehicleTypeId', 'contactName', 'contactPhone'],
      },
    },

    {
      name: 'hitrip_create_charter_order',
      description: '创建包车订单',
      inputSchema: {
        type: 'object',
        properties: {
          // 基础信息
          serviceDate: { type: 'string', description: '服务日期，格式：YYYY-MM-DD' },
          origin: {
            type: 'object',
            description: '出发地信息',
            properties: {
              address: { type: 'string', description: '出发地地址' },
              addressDetail: { type: 'string', description: '详细地址' },
              location: { type: 'string', description: '经纬度坐标' },
              poiId: { type: 'string', description: 'POI ID' },
            },
            required: ['address'],
          },
          destination: {
            type: 'object',
            description: '目的地信息',
            properties: {
              address: { type: 'string', description: '目的地地址' },
              addressDetail: { type: 'string', description: '详细地址' },
              location: { type: 'string', description: '经纬度坐标' },
              poiId: { type: 'string', description: 'POI ID' },
            },
            required: ['address'],
          },
          vehicleTypeId: { type: 'number', description: '车型ID' },
          serviceHours: { type: 'number', description: '服务小时数' },
          
          // 乘客信息
          contactName: { type: 'string', description: '联系人姓名' },
          contactPhone: { type: 'string', description: '联系人电话' },
          guestNum: { type: 'number', description: '乘客人数' },
          
          // 可选信息
          remark: { type: 'string', description: '备注信息' },
          specialRequirements: { type: 'string', description: '特殊要求' },
        },
        required: ['serviceDate', 'origin', 'destination', 'vehicleTypeId', 'contactName', 'contactPhone'],
      },
    },

    // 订单管理工具
    {
      name: 'hitrip_get_order_list',
      description: '获取订单列表',
      inputSchema: {
        type: 'object',
        properties: {
          pageNum: { type: 'number', description: '页码，默认为1' },
          pageSize: { type: 'number', description: '每页数量，默认为10' },
          orderStatus: { type: 'string', description: '订单状态筛选' },
          startDate: { type: 'string', description: '开始日期，格式：YYYY-MM-DD' },
          endDate: { type: 'string', description: '结束日期，格式：YYYY-MM-DD' },
        },
        required: [],
      },
    },

    {
      name: 'hitrip_get_order_detail',
      description: '获取订单详情',
      inputSchema: {
        type: 'object',
        properties: {
          orderId: {
            type: 'string',
            description: '订单ID',
          },
        },
        required: ['orderId'],
      },
    },

    {
      name: 'hitrip_cancel_order',
      description: '取消订单',
      inputSchema: {
        type: 'object',
        properties: {
          orderId: {
            type: 'string',
            description: '订单ID',
          },
          cancelReason: {
            type: 'string',
            description: '取消原因',
          },
        },
        required: ['orderId', 'cancelReason'],
      },
    },
  ];
}
