import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import NodeCache from 'node-cache';

import { HitripConfig } from '../types/config.js';
import { CryptoUtils } from '../utils/crypto.js';
import { logger } from '../utils/logger.js';
import {
  HitripApiResponse,
  CityInfo,
  AirportInfo,
  VehicleType
} from '../types/hitrip-api/common.js';
import {
  TokenResponse,
  CachedToken
} from '../types/hitrip-api/auth.js';
import {
  CharterQuoteRequest,
  QuoteListResponse,
} from '../types/hitrip-api/quote.js';
import {
  OrderResponse,
  OrderListResponse,
} from '../types/hitrip-api/order.js';

export class HitripApiClient {
  private config: HitripConfig;
  private httpClient: AxiosInstance;
  private crypto: CryptoUtils;
  private cache: NodeCache;

  constructor(config: HitripConfig) {
    this.config = config;
    this.crypto = new CryptoUtils(config.secretKey);
    
    // 初始化缓存
    this.cache = new NodeCache({
      stdTTL: config.cache.tokenTtl,
      checkperiod: 600, // 10分钟检查一次过期
      useClones: false,
    });

    // 初始化HTTP客户端
    this.httpClient = axios.create({
      baseURL: config.baseUrl,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Hitrip-MCP-Server/1.0.0',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    // 请求拦截器
    this.httpClient.interceptors.request.use(
      (config) => {
        logger.debug('API Request', {
          method: config.method?.toUpperCase(),
          url: config.url,
          params: config.params,
        });
        return config;
      },
      (error) => {
        logger.error('Request interceptor error', { error });
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.httpClient.interceptors.response.use(
      (response) => {
        logger.debug('API Response', {
          status: response.status,
          url: response.config.url,
          success: response.data?.success,
        });
        return response;
      },
      (error) => {
        logger.error('API Error', {
          status: error.response?.status,
          url: error.config?.url,
          message: error.response?.data?.message || error.message,
        });
        return Promise.reject(error);
      }
    );
  }

  private generateTimestamp(): string {
    return new Date().toISOString().replace('T', ' ').substring(0, 19);
  }

  private createSignedParams(params: Record<string, any>): Record<string, any> {
    const timestamp = this.generateTimestamp();
    const signedParams: Record<string, any> = {
      ...params,
      cid: this.config.cid,
      timestamp,
    };

    signedParams.sign = this.crypto.generateSignature(signedParams);
    return signedParams;
  }

  // Token管理
  async getToken(): Promise<string> {
    const cacheKey = `token_${this.config.cid}`;

    if (this.config.cache.enabled) {
      const cached = this.cache.get<CachedToken>(cacheKey);
      if (cached && cached.expiresAt > Date.now()) {
        logger.debug('Using cached token');
        return cached.token;
      }
    }

    logger.info('Fetching new token');

    try {
      // 黄包车getToken使用GET请求，参数在URL中
      const url = `/otaorder-api/access/getAccessToken?cid=${this.config.cid}&secretKey=${this.config.secretKey}`;
      const response = await this.httpClient.get<TokenResponse>(url);

      if (!response.data.success) {
        throw new Error(`Token request failed: ${response.data.status}`);
      }

      const token = response.data.data;

      if (this.config.cache.enabled) {
        const cachedToken: CachedToken = {
          token,
          expiresAt: Date.now() + (this.config.cache.tokenTtl * 1000),
          createdAt: Date.now(),
        };
        this.cache.set(cacheKey, cachedToken);
      }

      logger.info('Token obtained successfully');
      return token;
    } catch (error) {
      logger.error('Failed to get token', { error });
      throw error;
    }
  }

  // 基础数据查询
  async getCities(params: { keyword?: string } = {}): Promise<CityInfo[]> {
    const token = await this.getToken();
    const signedParams = this.createSignedParams(params);
    const response = await this.httpClient.post<HitripApiResponse<CityInfo[]>>(
      `/otaorder-api/common/v1.0/cities/listByNameAndSpell?token=${token}`,
      signedParams
    );

    if (!response.data.success) {
      throw new Error(`Get cities failed: ${response.data.message}`);
    }

    return response.data.data;
  }

  async getAirports(params: { keyword?: string } = {}): Promise<AirportInfo[]> {
    const token = await this.getToken();
    const signedParams = this.createSignedParams(params);
    const response = await this.httpClient.post<HitripApiResponse<AirportInfo[]>>(
      `/otaorder-api/common/v1.0/airport/listByNameAndSpell?token=${token}`,
      signedParams
    );

    if (!response.data.success) {
      throw new Error(`Get airports failed: ${response.data.message}`);
    }

    return response.data.data;
  }

  async getVehicleTypes(params: { countryId?: number } = {}): Promise<VehicleType[]> {
    const token = await this.getToken();
    const signedParams = this.createSignedParams(params);
    const response = await this.httpClient.post<HitripApiResponse<VehicleType[]>>(
      `/otaorder-api/common/v1.0/vehicleType/queryAllValid?token=${token}`,
      signedParams
    );

    if (!response.data.success) {
      throw new Error(`Get vehicle types failed: ${response.data.message}`);
    }

    return response.data.data;
  }

  // 报价相关方法
  async getPickupQuote(toolParams: any): Promise<QuoteListResponse> {
    const token = await this.getToken();

    // 将工具参数映射到 API 请求参数
    const requestBody = {
      airportCode: toolParams.airportCode,
      airportName: toolParams.airportName,
      serviceTime: toolParams.serviceTime,
      serviceCityId: toolParams.serviceCityId,
      serviceCityName: toolParams.serviceCityName,
      startAddress: toolParams.startAddress,
      startDetailAddress: toolParams.startDetailAddress,
      startLocation: toolParams.startLocation,
      endAddress: toolParams.destination.address,
      endDetailAddress: toolParams.destination.addressDetail,
      endLocation: toolParams.destination.location,
      carModelId: toolParams.carModelId
    };

    const signedParams = this.createSignedParams(requestBody);
    const response = await this.httpClient.post<HitripApiResponse<QuoteListResponse>>(
      `/otaorder-api/common/v1.0/quoteprice/pickup?token=${token}`,
      signedParams
    );

    if (!response.data.success) {
      throw new Error(`Get pickup quote failed: ${response.data.message}`);
    }

    return response.data.data;
  }

  async getDropoffQuote(toolParams: any): Promise<QuoteListResponse> {
    const token = await this.getToken();

    // 将工具参数映射到 API 请求参数
    const requestBody = {
      airportCode: toolParams.airportCode,
      airportName: toolParams.airportName,
      serviceTime: toolParams.serviceTime,
      serviceCityId: toolParams.serviceCityId,
      serviceCityName: toolParams.serviceCityName,
      startAddress: toolParams.origin.address,
      startDetailAddress: toolParams.origin.addressDetail,
      startLocation: toolParams.origin.location,
      endAddress: toolParams.endAddress,
      endDetailAddress: toolParams.endDetailAddress,
      endLocation: toolParams.endLocation,
      carModelId: toolParams.carModelId
    };

    const signedParams = this.createSignedParams(requestBody);
    const response = await this.httpClient.post<HitripApiResponse<QuoteListResponse>>(
      `/otaorder-api/common/v1.0/quoteprice/transfer?token=${token}`,
      signedParams
    );

    if (!response.data.success) {
      throw new Error(`Get dropoff quote failed: ${response.data.message}`);
    }

    return response.data.data;
  }

  async getCharterQuote(toolParams: any): Promise<QuoteListResponse> {
    const token = await this.getToken();

    // 将工具参数映射到 API 请求参数
    const requestBody = {
      channelId: parseInt(this.config.cid), // 使用 CID 作为 channelId
      serviceTime: toolParams.serviceTime,
      singleDailyParamList: [
        {
          startAddress: toolParams.origin.address,
          startDetailAddress: toolParams.origin.addressDetail || toolParams.origin.address,
          startLocation: toolParams.origin.location,
          startServiceCityId: toolParams.origin.cityId,
          startServiceCityName: toolParams.origin.cityName,
          startServiceTime: toolParams.serviceTime,
          endAddress: toolParams.destination.address,
          endDetailAddress: toolParams.destination.addressDetail || toolParams.destination.address,
          endLocation: toolParams.destination.location,
          endServiceCityId: toolParams.destination.cityId,
          endServiceCityName: toolParams.destination.cityName,
          tourType: toolParams.tourType || 1,
          halfDay: toolParams.serviceHours && toolParams.serviceHours <= 4 ? 1 : 0,
          distance: toolParams.distance || 0,
          duration: toolParams.duration || 0,
          passPoiList: [] // 暂时为空，后续可扩展
        }
      ]
    };

    const signedParams = this.createSignedParams(requestBody);
    const response = await this.httpClient.post<HitripApiResponse<QuoteListResponse>>(
      `/otaorder-api/common/v1.0/quoteprice/daily?token=${token}`,
      signedParams
    );

    if (!response.data.success) {
      throw new Error(`Get charter quote failed: ${response.data.message}`);
    }

    return response.data.data;
  }

  // 订单相关方法
  async createPickupOrder(toolParams: any): Promise<OrderResponse> {
    const token = await this.getToken();
    // 接机订单需要DES加密，直接使用工具参数
    const encryptedParams = this.crypto.desEncrypt(JSON.stringify(toolParams));
    const signedParams = this.createSignedParams({ encryptBody: encryptedParams });
    const response = await this.httpClient.post<HitripApiResponse<OrderResponse>>(
      `/otaorder-api/common/v1.0/order/pickup?token=${token}`,
      signedParams
    );

    if (!response.data.success) {
      throw new Error(`Create pickup order failed: ${response.data.message}`);
    }

    return response.data.data;
  }

  async createDropoffOrder(toolParams: any): Promise<OrderResponse> {
    const token = await this.getToken();
    // 送机订单需要DES加密，直接使用工具参数
    const encryptedParams = this.crypto.desEncrypt(JSON.stringify(toolParams));
    const signedParams = this.createSignedParams({ encryptBody: encryptedParams });
    const response = await this.httpClient.post<HitripApiResponse<OrderResponse>>(
      `/otaorder-api/common/v1.0/order/transfer?token=${token}`,
      signedParams
    );

    if (!response.data.success) {
      throw new Error(`Create dropoff order failed: ${response.data.message}`);
    }

    return response.data.data;
  }

  async createCharterOrder(toolParams: any): Promise<OrderResponse> {
    const token = await this.getToken();
    // 包车订单需要DES加密，直接使用工具参数
    const encryptedParams = this.crypto.desEncrypt(JSON.stringify(toolParams));
    const signedParams = this.createSignedParams({ encryptBody: encryptedParams });
    const response = await this.httpClient.post<HitripApiResponse<OrderResponse>>(
      `/otaorder-api/common/v1.0/order/car?token=${token}`,
      signedParams
    );

    if (!response.data.success) {
      throw new Error(`Create charter order failed: ${response.data.message}`);
    }

    return response.data.data;
  }

  async getOrderList(toolParams: any): Promise<OrderListResponse> {
    const token = await this.getToken();
    const signedParams = this.createSignedParams({
      limit: toolParams.limit,
      offset: toolParams.offset,
      listType: toolParams.listType,
      userId: toolParams.userId,
    });
    const response = await this.httpClient.post<HitripApiResponse<OrderListResponse>>(
      `/otaorder-api/common/v1.0/order/orderList?token=${token}`,
      signedParams
    );

    if (!response.data.success) {
      throw new Error(`Get order list failed: ${response.data.message}`);
    }

    return response.data.data;
  }

  async getOrderDetail(toolParams: any): Promise<OrderResponse> {
    const token = await this.getToken();
    const requestBody: any = {};

    // 添加订单号参数（二选一）
    if (toolParams.orderTravelNo) {
      requestBody.orderTravelNo = toolParams.orderTravelNo;
    }
    if (toolParams.orderNoThird) {
      requestBody.orderNoThird = toolParams.orderNoThird;
    }

    const signedParams = this.createSignedParams(requestBody);
    const response = await this.httpClient.post<HitripApiResponse<OrderResponse>>(
      `/otaorder-api/common/v1.0/order/orderDetail?token=${token}`,
      signedParams
    );

    if (!response.data.success) {
      throw new Error(`Get order detail failed: ${response.data.message}`);
    }

    return response.data.data;
  }

  async cancelOrder(toolParams: any): Promise<{ success: boolean; message?: string }> {
    const token = await this.getToken();
    const requestBody: any = {
      orderTravelNo: toolParams.orderTravelNo,
      cancelReason: toolParams.cancelReason,
      cancelType: toolParams.cancelType,
    };

    // 添加可选参数
    if (toolParams.cancelRemark) {
      requestBody.cancelRemark = toolParams.cancelRemark;
    }
    if (toolParams.actuallyPaidPrice) {
      requestBody.actuallyPaidPrice = toolParams.actuallyPaidPrice;
    }

    const signedParams = this.createSignedParams(requestBody);
    const response = await this.httpClient.post<HitripApiResponse<{ success: boolean; message?: string }>>(
      `/otaorder-api/common/v1.0/order/cancel?token=${token}`,
      signedParams
    );

    if (!response.data.success) {
      throw new Error(`Cancel order failed: ${response.data.message}`);
    }

    return response.data.data;
  }
}
