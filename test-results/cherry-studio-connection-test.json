{"timestamp": "2025-09-08T01:55:11.473Z", "summary": {"total": 4, "passed": 3, "failed": 1, "successRate": "75.0%"}, "results": [{"name": "服务器健康检查", "status": "PASS", "result": {"status": "healthy", "service": "Hitrip MCP SSE Server", "activeConnections": 0}}, {"name": "SSE 连接测试", "status": "PASS", "result": {"connectionId": "conn_1757296511311_66rc519ga", "toolsCount": 13, "tools": ["hitrip_get_token", "hitrip_get_cities", "hitrip_get_airports", "hitrip_get_vehicle_types", "hitrip_pickup_quote"]}}, {"name": "MCP 协议测试", "status": "PASS", "result": {"toolsCount": 13, "sampleTools": [{"name": "hitrip_get_token", "description": "获取黄包车API访问令牌"}, {"name": "hitrip_get_cities", "description": "查询城市列表"}, {"name": "hitrip_get_airports", "description": "查询机场信息"}]}}, {"name": "工具调用测试", "status": "FAIL", "error": "查询城市 错误: Internal error"}]}