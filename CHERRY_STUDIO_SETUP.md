# 🍒 Cherry Studio MCP 配置指南

## 🚨 问题确认

你遇到的错误 `Token request failed: 9330005` 的原因是：

**测试API凭据无效！**

- 错误码 `9330005` = "CID或密钥无效"
- 当前使用的测试凭据不是真实有效的黄包车API凭据

## 🔧 解决步骤

### 1. 获取真实API凭据

你需要联系黄包车获取真实的API凭据：

- **CID**: 真实的商户ID
- **SECRET_KEY**: 真实的密钥

### 2. 更新Cherry Studio配置

在Cherry Studio中配置MCP服务器时，使用以下配置：

```json
{
  "name": "Hitrip Travel Service",
  "description": "黄包车旅游预订服务",
  "command": "node",
  "args": [
    "/Users/<USER>/WorkSpace/hitrip_new_mcp/dist/mcp-launcher.js"
  ],
  "env": {
    "HITRIP_CID": "你的真实CID",
    "HITRIP_SECRET_KEY": "你的真实密钥",
    "HITRIP_BASE_URL": "https://api-gw.huangbaoche.com/",
    "NODE_ENV": "production",
    "LOG_LEVEL": "info"
  }
}
```

### 3. Cherry Studio MCP 配置位置

根据Cherry Studio的文档，MCP配置通常在：

- **设置** → **MCP服务器** → **添加服务器**
- 或者在配置文件中（具体位置取决于Cherry Studio版本）

### 4. 验证配置

配置完成后，你可以测试：

```
请帮我查询北京的机场信息
```

如果配置正确，应该能看到类似这样的响应：
```json
{
  "cityId": 255,
  "cityName": "北京",
  "airports": [...]
}
```

## 🔍 调试工具

### 使用我们的调试脚本

```bash
# 设置真实凭据后运行
HITRIP_CID="你的真实CID" HITRIP_SECRET_KEY="你的真实密钥" node scripts/debug-cherry-studio.js
```

### 查看日志

```bash
# 查看MCP服务器日志
tail -f /Users/<USER>/WorkSpace/hitrip_new_mcp/logs/mcp-debug-*.log
```

## 📋 当前项目状态

### ✅ 已完成
- MCP协议实现完美
- 所有13个工具已实现
- API端点映射正确
- 错误处理完善
- 日志系统完整

### ❌ 需要解决
- **获取真实API凭据** (这是唯一的问题)

## 🎯 下一步行动

1. **联系黄包车技术支持**
   - 申请真实的API凭据
   - 确认账户状态

2. **更新配置**
   - 在Cherry Studio中使用真实凭据
   - 重新测试MCP连接

3. **验证功能**
   - 测试Token获取
   - 测试城市/机场查询
   - 测试报价和订单功能

## 💡 重要提醒

**MCP服务器本身完全正常工作！** 

问题只是API凭据无效。一旦你获得真实的黄包车API凭据，整个系统就会完美运行。

## 🆘 如果仍有问题

如果获得真实凭据后仍有问题，请：

1. 运行调试脚本查看详细错误
2. 检查Cherry Studio的MCP日志
3. 确认网络连接正常
4. 验证API端点是否有变化

项目代码质量很高，只需要正确的API凭据即可完美运行！
