# Cherry Studio SSE 模式配置指南

## 🍒 概述

本指南说明如何在 Cherry Studio 中配置基于 SSE (Server-Sent Events) 模式的 Hitrip MCP 服务器。

## 🔧 配置步骤

### 1. 启动 SSE 服务器

首先确保 Hitrip MCP SSE 服务器正在运行：

```bash
# 启动 SSE 服务器
./scripts/start-sse-server.sh

# 验证服务器状态
curl http://localhost:8081/health
```

### 2. Cherry Studio 配置方法

#### 方法一：通过配置文件

创建或编辑 Cherry Studio 的 MCP 配置文件（通常位于用户配置目录）：

```json
{
  "mcpServers": {
    "hitrip": {
      "name": "Hitrip MCP Server (SSE)",
      "description": "黄包车接机送机包车服务 - SSE 模式",
      "type": "sse",
      "config": {
        "sseUrl": "http://localhost:8081/sse",
        "mcpUrl": "http://localhost:8081/mcp",
        "healthUrl": "http://localhost:8081/health",
        "timeout": 30000,
        "reconnect": true,
        "reconnectInterval": 5000
      },
      "env": {
        "HITRIP_CID": "**********",
        "HITRIP_SECRET_KEY": "12345678",
        "HITRIP_BASE_URL": "https://api-gw.test.huangbaoche.com/"
      }
    }
  }
}
```

#### 方法二：通过 Cherry Studio 界面

1. 打开 Cherry Studio
2. 进入设置 → MCP 服务器
3. 点击"添加服务器"
4. 选择"SSE 模式"
5. 填入以下配置：

```
服务器名称: Hitrip MCP Server
服务器类型: SSE
SSE 端点: http://localhost:8081/sse
MCP 端点: http://localhost:8081/mcp
健康检查: http://localhost:8081/health
```

### 3. 高级配置选项

```json
{
  "mcpServers": {
    "hitrip": {
      "name": "Hitrip MCP Server (SSE)",
      "type": "sse",
      "config": {
        "sseUrl": "http://localhost:8081/sse",
        "mcpUrl": "http://localhost:8081/mcp",
        "healthUrl": "http://localhost:8081/health",
        
        // 连接配置
        "timeout": 30000,
        "reconnect": true,
        "reconnectInterval": 5000,
        "maxReconnectAttempts": 10,
        
        // SSE 配置
        "withCredentials": false,
        "headers": {
          "Accept": "text/event-stream",
          "Cache-Control": "no-cache"
        },
        
        // 心跳配置
        "heartbeat": {
          "enabled": true,
          "interval": 30000,
          "timeout": 10000
        },
        
        // 日志配置
        "logging": {
          "enabled": true,
          "level": "info"
        }
      },
      
      // 工具过滤（可选）
      "tools": {
        "include": [
          "hitrip_get_token",
          "hitrip_get_cities",
          "hitrip_pickup_quote",
          "hitrip_dropoff_quote",
          "hitrip_charter_quote"
        ],
        "exclude": []
      }
    }
  }
}
```

## 🧪 连接测试

### 1. 验证服务器状态

```bash
# 检查服务器健康状态
curl http://localhost:8081/health

# 检查工具列表
curl http://localhost:8081/tools

# 测试 SSE 连接
curl -N -H 'Accept: text/event-stream' http://localhost:8081/sse
```

### 2. Cherry Studio 连接测试

在 Cherry Studio 中：

1. 进入 MCP 服务器管理页面
2. 找到 Hitrip 服务器配置
3. 点击"测试连接"按钮
4. 查看连接状态和工具列表

### 3. 功能测试

在 Cherry Studio 对话中测试以下功能：

```
# 测试获取城市列表
请帮我查询东京的城市信息

# 测试接机报价
请帮我查询从东京成田机场到东京站的接机报价，服务时间是2025-12-25 10:00:00

# 测试送机报价
请帮我查询从东京站到东京成田机场的送机报价，服务时间是2025-12-25 15:00:00
```

## 🔧 故障排除

### 常见问题

1. **连接失败**
   - 检查 SSE 服务器是否正在运行
   - 验证端口 8081 是否可访问
   - 检查防火墙设置

2. **工具列表为空**
   - 检查服务器日志
   - 验证 API 配置是否正确
   - 测试健康检查端点

3. **请求超时**
   - 增加 timeout 配置值
   - 检查网络连接
   - 验证 API 服务器状态

### 调试方法

1. **查看服务器日志**
   ```bash
   # 查看 SSE 服务器日志
   tail -f /path/to/sse-server.log
   ```

2. **启用详细日志**
   ```json
   {
     "config": {
       "logging": {
         "enabled": true,
         "level": "debug"
       }
     }
   }
   ```

3. **手动测试 SSE 连接**
   ```bash
   # 使用 curl 测试 SSE 连接
   curl -N -H 'Accept: text/event-stream' http://localhost:8081/sse
   ```

## 📊 性能优化

### 1. 连接池配置

```json
{
  "config": {
    "connectionPool": {
      "maxConnections": 10,
      "keepAlive": true,
      "keepAliveTimeout": 60000
    }
  }
}
```

### 2. 缓存配置

```json
{
  "config": {
    "cache": {
      "enabled": true,
      "ttl": 300000,
      "maxSize": 100
    }
  }
}
```

## 🔒 安全配置

### 1. HTTPS 配置（生产环境）

```json
{
  "config": {
    "sseUrl": "https://your-domain.com:8081/sse",
    "mcpUrl": "https://your-domain.com:8081/mcp",
    "ssl": {
      "enabled": true,
      "rejectUnauthorized": true
    }
  }
}
```

### 2. 认证配置

```json
{
  "config": {
    "auth": {
      "type": "bearer",
      "token": "your-api-token"
    },
    "headers": {
      "Authorization": "Bearer your-api-token"
    }
  }
}
```

## 📝 配置模板

### 基础配置模板

```json
{
  "mcpServers": {
    "hitrip": {
      "name": "Hitrip MCP Server",
      "type": "sse",
      "config": {
        "sseUrl": "http://localhost:8081/sse",
        "mcpUrl": "http://localhost:8081/mcp",
        "timeout": 30000,
        "reconnect": true
      }
    }
  }
}
```

### 完整配置模板

```json
{
  "mcpServers": {
    "hitrip": {
      "name": "Hitrip MCP Server (Production)",
      "description": "黄包车接机送机包车服务",
      "type": "sse",
      "config": {
        "sseUrl": "https://your-domain.com:8081/sse",
        "mcpUrl": "https://your-domain.com:8081/mcp",
        "healthUrl": "https://your-domain.com:8081/health",
        "timeout": 30000,
        "reconnect": true,
        "reconnectInterval": 5000,
        "maxReconnectAttempts": 10,
        "ssl": {
          "enabled": true,
          "rejectUnauthorized": true
        },
        "auth": {
          "type": "bearer",
          "token": "your-api-token"
        },
        "logging": {
          "enabled": true,
          "level": "info"
        }
      },
      "tools": {
        "include": [
          "hitrip_get_cities",
          "hitrip_pickup_quote",
          "hitrip_dropoff_quote",
          "hitrip_charter_quote"
        ]
      }
    }
  }
}
```

## 📞 支持

如果遇到配置问题：

1. 检查 SSE 服务器日志
2. 验证网络连接
3. 测试 API 端点
4. 查看 Cherry Studio 错误日志
