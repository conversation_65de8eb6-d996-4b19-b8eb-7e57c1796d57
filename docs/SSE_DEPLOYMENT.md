# Hitrip MCP SSE 服务器部署指南

## 🚀 SSE 模式概述

Hitrip MCP 服务器的 SSE (Server-Sent Events) 模式专为 Agent 端调用设计，提供实时的单向通信能力。Agent 可以通过 SSE 连接接收服务器推送的消息，同时通过 HTTP POST 发送 MCP 请求。

## 🏗️ 架构设计

```
Agent 端                    SSE 服务器
   |                           |
   |-- SSE 连接 (接收) -------->|
   |                           |
   |<-- 工具列表推送 ------------|
   |<-- 响应消息推送 ------------|
   |<-- 心跳消息推送 ------------|
   |                           |
   |-- HTTP POST (发送) ------->|
   |   (MCP 请求)               |
```

## 📋 环境要求

- Node.js 18+
- npm 或 yarn
- 网络访问权限（访问黄包车 API）

## 🔧 安装和配置

### 1. 安装依赖

```bash
npm install
npm install eventsource node-fetch  # SSE 测试依赖
```

### 2. 环境变量配置

```bash
export HITRIP_CID="your_channel_id"
export HITRIP_SECRET_KEY="your_secret_key"
export HITRIP_BASE_URL="https://api-gw.test.huangbaoche.com/"
export NODE_ENV="production"
export LOG_LEVEL="info"
export PORT="8081"
```

### 3. 构建项目

```bash
npm run build
```

## 🚀 启动 SSE 服务

### 方式一：使用启动脚本（推荐）

```bash
./scripts/start-sse-server.sh
```

### 方式二：手动启动

```bash
# 设置环境变量
export HITRIP_CID="**********"
export HITRIP_SECRET_KEY="12345678"
export HITRIP_BASE_URL="https://api-gw.test.huangbaoche.com/"
export PORT="8081"

# 启动服务
npm run start:sse
```

### 方式三：开发模式

```bash
npm run dev:sse
```

## 📡 SSE API 端点

### 基础端点

| 端点 | 方法 | 描述 |
|------|------|------|
| `/` | GET | 服务信息和活跃连接数 |
| `/health` | GET | 健康检查和连接状态 |
| `/tools` | GET | 获取工具列表 |

### SSE 端点

| 端点 | 方法 | 描述 |
|------|------|------|
| `/sse` | GET | 建立 SSE 连接 |

### MCP 协议端点

| 端点 | 方法 | 描述 |
|------|------|------|
| `/mcp` | POST | 处理 MCP 请求，支持 SSE 响应 |

### 工具调用端点

| 端点 | 方法 | 描述 |
|------|------|------|
| `/tools/{toolName}` | POST | 直接调用指定工具 |

### 管理端点

| 端点 | 方法 | 描述 |
|------|------|------|
| `/broadcast` | POST | 向所有连接广播消息 |

## 🔌 SSE 连接流程

### 1. 建立 SSE 连接

```javascript
const eventSource = new EventSource('http://localhost:8081/sse');

eventSource.onopen = () => {
  console.log('SSE 连接已建立');
};
```

### 2. 监听 SSE 事件

```javascript
// 连接确认
eventSource.addEventListener('connected', (event) => {
  const data = JSON.parse(event.data);
  console.log('连接ID:', data.connectionId);
});

// 工具列表
eventSource.addEventListener('tools', (event) => {
  const data = JSON.parse(event.data);
  console.log('工具列表:', data.tools);
});

// MCP 响应
eventSource.addEventListener('response', (event) => {
  const response = JSON.parse(event.data);
  console.log('MCP 响应:', response);
});

// 心跳
eventSource.addEventListener('ping', (event) => {
  const data = JSON.parse(event.data);
  console.log('心跳:', data.timestamp);
});
```

### 3. 发送 MCP 请求

```javascript
const connectionId = 'conn_xxx'; // 从 connected 事件获取

const mcpRequest = {
  jsonrpc: '2.0',
  id: 1,
  method: 'tools/call',
  params: {
    name: 'hitrip_get_token',
    arguments: {}
  }
};

fetch('http://localhost:8081/mcp', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-Connection-Id': connectionId  // 指定连接ID
  },
  body: JSON.stringify(mcpRequest)
});
```

## 📝 SSE 事件类型

### `connected` 事件
连接建立确认
```json
{
  "connectionId": "conn_1757296056353_b89bbyi8o",
  "timestamp": "2025-09-08T01:47:36.354Z",
  "message": "SSE connection established"
}
```

### `tools` 事件
工具列表推送
```json
{
  "tools": [
    {
      "name": "hitrip_get_token",
      "description": "获取黄包车API访问令牌",
      "inputSchema": { ... }
    }
  ]
}
```

### `response` 事件
MCP 响应推送
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "result": {
    "content": [
      {
        "type": "text",
        "text": "响应内容"
      }
    ]
  }
}
```

### `ping` 事件
心跳保持连接
```json
{
  "timestamp": "2025-09-08T01:47:36.354Z"
}
```

### `disconnect` 事件
服务器关闭通知
```json
{
  "message": "Server shutting down"
}
```

## 🧪 测试

### 基础功能测试
```bash
./scripts/test-sse-server.sh
```

### SSE 客户端测试
```bash
node scripts/test-sse-client.js
```

### 手动 SSE 连接测试
```bash
curl -N -H 'Accept: text/event-stream' http://localhost:8081/sse
```

## 🔧 Agent 端集成示例

### Python 示例

```python
import requests
import sseclient
import json

# 建立 SSE 连接
response = requests.get('http://localhost:8081/sse', stream=True)
client = sseclient.SSEClient(response)

connection_id = None

for event in client.events():
    if event.event == 'connected':
        data = json.loads(event.data)
        connection_id = data['connectionId']
        print(f"连接建立: {connection_id}")
    
    elif event.event == 'tools':
        data = json.loads(event.data)
        print(f"收到工具列表: {len(data['tools'])} 个工具")
    
    elif event.event == 'response':
        response = json.loads(event.data)
        print(f"收到 MCP 响应: {response}")

# 发送 MCP 请求
def send_mcp_request(request):
    return requests.post('http://localhost:8081/mcp', 
                        json=request,
                        headers={'X-Connection-Id': connection_id})
```

### JavaScript/Node.js 示例

```javascript
import { EventSource } from 'eventsource';
import fetch from 'node-fetch';

const eventSource = new EventSource('http://localhost:8081/sse');
let connectionId = null;

eventSource.addEventListener('connected', (event) => {
  const data = JSON.parse(event.data);
  connectionId = data.connectionId;
});

eventSource.addEventListener('response', (event) => {
  const response = JSON.parse(event.data);
  console.log('收到响应:', response);
});

// 发送 MCP 请求
async function sendMcpRequest(request) {
  const response = await fetch('http://localhost:8081/mcp', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Connection-Id': connectionId
    },
    body: JSON.stringify(request)
  });
  
  return response.json();
}
```

## 🔒 安全注意事项

1. **连接管理** - 服务器自动管理连接生命周期
2. **心跳机制** - 每30秒发送心跳保持连接活跃
3. **错误处理** - 完善的错误处理和连接恢复机制
4. **资源清理** - 连接断开时自动清理资源

## 📊 监控和管理

### 查看活跃连接
```bash
curl http://localhost:8081/health
```

### 广播消息到所有连接
```bash
curl -X POST http://localhost:8081/broadcast \
  -H "Content-Type: application/json" \
  -d '{"type": "notification", "data": {"message": "系统维护通知"}}'
```

## 🔧 故障排除

### 常见问题

1. **连接断开** - 检查网络连接和服务器状态
2. **消息丢失** - 确保正确处理 SSE 事件
3. **内存泄漏** - 及时关闭不需要的连接

### 调试技巧

1. **查看服务器日志** - 所有连接和请求都有详细日志
2. **监控连接数** - 通过健康检查端点监控活跃连接
3. **测试工具** - 使用提供的测试脚本验证功能

## 📞 支持

SSE 模式特别适合：
- Agent 端实时接收服务器推送
- 长连接场景下的高效通信
- 需要服务器主动推送的应用场景

如有问题，请查看服务器日志和测试脚本输出。
