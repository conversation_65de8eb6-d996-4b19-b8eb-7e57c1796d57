# Hitrip MCP HTTP 服务器部署指南

## 🚀 部署概述

Hitrip MCP 服务器现在支持两种运行模式：

1. **STDIO 模式** - 本地开发和 MCP 客户端集成
2. **HTTP 模式** - 在线服务，支持 REST API 和 WebSocket

## 📋 环境要求

- Node.js 18+ 
- npm 或 yarn
- 网络访问权限（访问黄包车 API）

## 🔧 安装和配置

### 1. 克隆和安装依赖

```bash
git clone <repository-url>
cd hitrip_new_mcp
npm install
```

### 2. 环境变量配置

创建 `.env` 文件或设置环境变量：

```bash
export HITRIP_CID="your_channel_id"
export HITRIP_SECRET_KEY="your_secret_key"
export HITRIP_BASE_URL="https://api-gw.test.huangbaoche.com/"
export NODE_ENV="production"
export LOG_LEVEL="info"
export PORT="8080"
```

### 3. 构建项目

```bash
npm run build
```

## 🚀 启动服务

### 方式一：使用启动脚本（推荐）

```bash
./scripts/start-http-server.sh
```

### 方式二：手动启动

```bash
# 设置环境变量
export HITRIP_CID="**********"
export HITRIP_SECRET_KEY="12345678"
export HITRIP_BASE_URL="https://api-gw.test.huangbaoche.com/"
export PORT="8080"

# 启动服务
npm run start:http
```

### 方式三：开发模式

```bash
npm run dev:http
```

## 📡 API 端点

### 基础端点

| 端点 | 方法 | 描述 |
|------|------|------|
| `/` | GET | 服务信息 |
| `/health` | GET | 健康检查 |
| `/tools` | GET | 获取工具列表 |

### MCP 协议端点

| 端点 | 方法 | 描述 |
|------|------|------|
| `/mcp` | POST | MCP JSON-RPC 协议端点 |
| `/ws` | WebSocket | MCP WebSocket 连接 |

### 工具调用端点

| 端点 | 方法 | 描述 |
|------|------|------|
| `/tools/{toolName}` | POST | 直接调用指定工具 |

## 🔧 可用工具

### 基础信息工具
- `hitrip_get_token` - 获取 API 访问令牌
- `hitrip_get_cities` - 查询城市列表
- `hitrip_get_airports` - 查询机场信息
- `hitrip_get_vehicle_types` - 查询车型信息

### 报价工具
- `hitrip_pickup_quote` - 接机服务报价
- `hitrip_dropoff_quote` - 送机服务报价
- `hitrip_charter_quote` - 包车服务报价

### 订单管理工具
- `hitrip_create_pickup_order` - 创建接机订单
- `hitrip_create_dropoff_order` - 创建送机订单
- `hitrip_create_charter_order` - 创建包车订单
- `hitrip_get_order_list` - 获取订单列表
- `hitrip_get_order_detail` - 获取订单详情
- `hitrip_cancel_order` - 取消订单

## 📝 使用示例

### 1. 健康检查

```bash
curl http://localhost:8080/health
```

### 2. 获取工具列表

```bash
curl http://localhost:8080/tools
```

### 3. 获取访问令牌

```bash
curl -X POST http://localhost:8080/tools/hitrip_get_token \
  -H "Content-Type: application/json" \
  -d '{}'
```

### 4. 查询城市列表

```bash
curl -X POST http://localhost:8080/tools/hitrip_get_cities \
  -H "Content-Type: application/json" \
  -d '{"keyword": "东京"}'
```

### 5. 接机报价

```bash
curl -X POST http://localhost:8080/tools/hitrip_pickup_quote \
  -H "Content-Type: application/json" \
  -d '{
    "airportCode": "NRT",
    "airportName": "东京成田机场",
    "serviceTime": "2025-12-25 10:00:00",
    "serviceCityId": 217,
    "serviceCityName": "东京",
    "startAddress": "东京成田机场",
    "startDetailAddress": "东京成田机场T1航站楼",
    "startLocation": "35.7647,140.3864",
    "destination": {
      "address": "东京站",
      "addressDetail": "东京站八重洲口",
      "location": "35.6812,139.7671"
    }
  }'
```

### 6. MCP 协议调用

```bash
curl -X POST http://localhost:8080/mcp \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "tools/list",
    "params": {}
  }'
```

## 🧪 测试

运行完整的服务器测试：

```bash
./scripts/test-http-server.sh
```

## 🔒 安全注意事项

1. **环境变量保护** - 不要在代码中硬编码敏感信息
2. **HTTPS** - 生产环境建议使用 HTTPS
3. **防火墙** - 适当配置防火墙规则
4. **访问控制** - 考虑添加 API 密钥或其他认证机制

## 🐳 Docker 部署（可选）

创建 `Dockerfile`：

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY dist ./dist
EXPOSE 8080
CMD ["node", "dist/http-launcher.js"]
```

构建和运行：

```bash
docker build -t hitrip-mcp-server .
docker run -p 8080:8080 \
  -e HITRIP_CID="your_cid" \
  -e HITRIP_SECRET_KEY="your_secret" \
  -e HITRIP_BASE_URL="https://api-gw.test.huangbaoche.com/" \
  hitrip-mcp-server
```

## 📊 监控和日志

服务器提供详细的日志输出，包括：
- 请求日志
- API 调用日志
- 错误日志
- 性能指标

## 🔧 故障排除

### 常见问题

1. **端口被占用** - 修改 `PORT` 环境变量
2. **API 调用失败** - 检查网络连接和 API 密钥
3. **内存不足** - 增加服务器内存或优化请求频率

### 日志查看

服务器日志会显示详细的请求和响应信息，有助于调试问题。

## 📞 支持

如有问题，请查看：
1. 服务器日志输出
2. API 响应错误信息
3. 网络连接状态
