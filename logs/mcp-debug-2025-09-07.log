{"timestamp":"2025-09-07T16:26:40.188Z","level":"INFO","message":"Starting Hitrip MCP Server (Debug Mode)","pid":70794}
{"timestamp":"2025-09-07T16:26:40.190Z","level":"INFO","message":"Node.js version","data":{"version":"v23.11.0"},"pid":70794}
{"timestamp":"2025-09-07T16:26:40.190Z","level":"INFO","message":"Working directory","data":{"cwd":"/Users/<USER>/WorkSpace/hitrip_new_mcp"},"pid":70794}
{"timestamp":"2025-09-07T16:26:40.190Z","level":"INFO","message":"Log file","data":{"path":"/Users/<USER>/WorkSpace/hitrip_new_mcp/logs/mcp-debug-2025-09-07.log"},"pid":70794}
{"timestamp":"2025-09-07T16:26:40.190Z","level":"INFO","message":"Validating configuration","pid":70794}
{"timestamp":"2025-09-07T16:26:40.191Z","level":"DEBUG","message":"Environment variables","data":{"HITRIP_BASE_URL":"https://api-gw.test.huangbaoche.com/","HITRIP_CID":"***","HITRIP_SECRET_KEY":"***"},"pid":70794}
{"timestamp":"2025-09-07T16:26:40.191Z","level":"INFO","message":"Configuration validated successfully","pid":70794}
{"timestamp":"2025-09-07T16:26:40.191Z","level":"INFO","message":"Creating MCP server instance","pid":70794}
{"timestamp":"2025-09-07T16:26:40.192Z","level":"INFO","message":"Starting MCP server in STDIO mode","pid":70794}
{"timestamp":"2025-09-07T16:26:40.195Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":71,"content":"2025-09-08 00:26:40 [\u001b[32minfo\u001b[39m]: MCP Server started in STDIO mode\n"},"pid":70794}
{"timestamp":"2025-09-07T16:26:40.196Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":80,"content":"info: MCP Server started in STDIO mode {\"timestamp\":\"2025-09-07T16:26:40.193Z\"}\n"},"pid":70794}
{"timestamp":"2025-09-07T16:26:40.197Z","level":"INFO","message":"Hitrip MCP Server started successfully","pid":70794}
{"timestamp":"2025-09-07T16:28:45.287Z","level":"INFO","message":"Starting Hitrip MCP Server (Debug Mode)","pid":77311}
{"timestamp":"2025-09-07T16:28:45.288Z","level":"INFO","message":"Node.js version","data":{"version":"v23.11.0"},"pid":77311}
{"timestamp":"2025-09-07T16:28:45.288Z","level":"INFO","message":"Working directory","data":{"cwd":"/Users/<USER>/WorkSpace/hitrip_new_mcp"},"pid":77311}
{"timestamp":"2025-09-07T16:28:45.288Z","level":"INFO","message":"Log file","data":{"path":"/Users/<USER>/WorkSpace/hitrip_new_mcp/logs/mcp-debug-2025-09-07.log"},"pid":77311}
{"timestamp":"2025-09-07T16:28:45.289Z","level":"INFO","message":"Validating configuration","pid":77311}
{"timestamp":"2025-09-07T16:28:45.289Z","level":"DEBUG","message":"Environment variables","data":{"NODE_ENV":"development","LOG_LEVEL":"debug","HITRIP_BASE_URL":"https://api-gw.test.huangbaoche.com/","HITRIP_CID":"***","HITRIP_SECRET_KEY":"***"},"pid":77311}
{"timestamp":"2025-09-07T16:28:45.289Z","level":"INFO","message":"Configuration validated successfully","pid":77311}
{"timestamp":"2025-09-07T16:28:45.289Z","level":"INFO","message":"Creating MCP server instance","pid":77311}
{"timestamp":"2025-09-07T16:28:45.292Z","level":"INFO","message":"Starting MCP server in STDIO mode","pid":77311}
{"timestamp":"2025-09-07T16:28:45.295Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":71,"content":"2025-09-08 00:28:45 [\u001b[32minfo\u001b[39m]: MCP Server started in STDIO mode\n"},"pid":77311}
{"timestamp":"2025-09-07T16:28:45.296Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":80,"content":"info: MCP Server started in STDIO mode {\"timestamp\":\"2025-09-07T16:28:45.293Z\"}\n"},"pid":77311}
{"timestamp":"2025-09-07T16:28:45.297Z","level":"INFO","message":"Hitrip MCP Server started successfully","pid":77311}
{"timestamp":"2025-09-07T16:28:46.986Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":1,"length":170,"message":"{\"jsonrpc\":\"2.0\",\"id\":1,\"method\":\"initialize\",\"params\":{\"protocolVersion\":\"2024-11-05\",\"capabilities\":{\"tools\":{}},\"clientInfo\":{\"name\":\"test-client\",\"version\":\"1.0.0\"}}}"},"pid":77311}
{"timestamp":"2025-09-07T16:28:46.986Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":1,"method":"initialize","id":1,"hasParams":true},"pid":77311}
{"timestamp":"2025-09-07T16:28:46.989Z","level":"DEBUG","message":"Sending MCP response","data":{"id":1,"hasResult":true,"hasError":false},"pid":77311}
{"timestamp":"2025-09-07T16:28:47.986Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":2,"length":54,"message":"{\"jsonrpc\":\"2.0\",\"method\":\"notifications/initialized\"}"},"pid":77311}
{"timestamp":"2025-09-07T16:28:47.986Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":2,"method":"notifications/initialized","hasParams":false},"pid":77311}
{"timestamp":"2025-09-07T16:28:48.986Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":3,"length":46,"message":"{\"jsonrpc\":\"2.0\",\"id\":2,\"method\":\"tools/list\"}"},"pid":77311}
{"timestamp":"2025-09-07T16:28:48.986Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":3,"method":"tools/list","id":2,"hasParams":false},"pid":77311}
{"timestamp":"2025-09-07T16:28:48.987Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":76,"content":"2025-09-08 00:28:48 [\u001b[34mdebug\u001b[39m]: Listing available tools {\"count\":13}\n"},"pid":77311}
{"timestamp":"2025-09-07T16:28:48.988Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":83,"content":"debug: Listing available tools {\"count\":13,\"timestamp\":\"2025-09-07T16:28:48.986Z\"}\n"},"pid":77311}
{"timestamp":"2025-09-07T16:28:48.988Z","level":"DEBUG","message":"Sending MCP response","data":{"id":2,"hasResult":true,"hasError":false},"pid":77311}
{"timestamp":"2025-09-07T16:28:49.986Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":4,"length":98,"message":"{\"jsonrpc\":\"2.0\",\"id\":3,\"method\":\"tools/call\",\"params\":{\"name\":\"hitrip_get_token\",\"arguments\":{}}}"},"pid":77311}
{"timestamp":"2025-09-07T16:28:49.986Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":4,"method":"tools/call","id":3,"hasParams":true},"pid":77311}
{"timestamp":"2025-09-07T16:28:49.989Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":95,"content":"2025-09-08 00:28:49 [\u001b[32minfo\u001b[39m]: Tool call received {\"tool\":\"hitrip_get_token\",\"args\":[]}\n"},"pid":77311}
{"timestamp":"2025-09-07T16:28:49.989Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":102,"content":"info: Tool call received {\"args\":[],\"timestamp\":\"2025-09-07T16:28:49.989Z\",\"tool\":\"hitrip_get_token\"}\n"},"pid":77311}
{"timestamp":"2025-09-07T16:28:49.990Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":57,"content":"2025-09-08 00:28:49 [\u001b[32minfo\u001b[39m]: Fetching new token\n"},"pid":77311}
{"timestamp":"2025-09-07T16:28:49.990Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":66,"content":"info: Fetching new token {\"timestamp\":\"2025-09-07T16:28:49.989Z\"}\n"},"pid":77311}
{"timestamp":"2025-09-07T16:28:49.992Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":87,"content":"2025-09-08 00:28:49 [\u001b[34mdebug\u001b[39m]: API Request {\"method\":\"POST\",\"url\":\"/getToken\"}\n"},"pid":77311}
{"timestamp":"2025-09-07T16:28:49.993Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":94,"content":"debug: API Request {\"method\":\"POST\",\"timestamp\":\"2025-09-07T16:28:49.991Z\",\"url\":\"/getToken\"}\n"},"pid":77311}
{"timestamp":"2025-09-07T16:28:50.061Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":118,"content":"2025-09-08 00:28:50 [\u001b[31merror\u001b[39m]: API Error Request failed with status code 404 {\"status\":404,\"url\":\"/getToken\"}\n"},"pid":77311}
{"timestamp":"2025-09-07T16:28:50.062Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":125,"content":"error: API Error Request failed with status code 404 {\"status\":404,\"timestamp\":\"2025-09-07T16:28:50.061Z\",\"url\":\"/getToken\"}\n"},"pid":77311}
{"timestamp":"2025-09-07T16:28:50.063Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":1961,"content":"2025-09-08 00:28:50 [\u001b[31merror\u001b[39m]: Failed to get token {\"error\":{\"message\":\"Request failed with status code 404\",\"name\":\"AxiosError\",\"stack\":\"AxiosError: Request failed with status code 404\\n    a"},"pid":77311}
{"timestamp":"2025-09-07T16:28:50.064Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":1968,"content":"error: Failed to get token {\"error\":{\"code\":\"ERR_BAD_REQUEST\",\"config\":{\"adapter\":[\"xhr\",\"http\",\"fetch\"],\"allowAbsoluteUrls\":true,\"baseURL\":\"https://api-gw.test.huangbaoche.com/\",\"data\":\"{\\\"cid\\\":\\\"10"},"pid":77311}
{"timestamp":"2025-09-07T16:28:50.064Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":130,"content":"2025-09-08 00:28:50 [\u001b[31merror\u001b[39m]: Tool call failed {\"tool\":\"hitrip_get_token\",\"error\":\"Request failed with status code 404\"}\n"},"pid":77311}
{"timestamp":"2025-09-07T16:28:50.064Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":137,"content":"error: Tool call failed {\"error\":\"Request failed with status code 404\",\"timestamp\":\"2025-09-07T16:28:50.064Z\",\"tool\":\"hitrip_get_token\"}\n"},"pid":77311}
{"timestamp":"2025-09-07T16:28:50.064Z","level":"DEBUG","message":"Sending MCP response","data":{"id":3,"hasResult":true,"hasError":false},"pid":77311}
{"timestamp":"2025-09-07T16:28:50.985Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":5,"length":112,"message":"{\"jsonrpc\":\"2.0\",\"id\":4,\"method\":\"tools/call\",\"params\":{\"name\":\"hitrip_get_cities\",\"arguments\":{\"countryId\":1}}}"},"pid":77311}
{"timestamp":"2025-09-07T16:28:50.986Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":5,"method":"tools/call","id":4,"hasParams":true},"pid":77311}
{"timestamp":"2025-09-07T16:28:50.987Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":107,"content":"2025-09-08 00:28:50 [\u001b[32minfo\u001b[39m]: Tool call received {\"tool\":\"hitrip_get_cities\",\"args\":[\"countryId\"]}\n"},"pid":77311}
{"timestamp":"2025-09-07T16:28:50.988Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":114,"content":"info: Tool call received {\"args\":[\"countryId\"],\"timestamp\":\"2025-09-07T16:28:50.987Z\",\"tool\":\"hitrip_get_cities\"}\n"},"pid":77311}
{"timestamp":"2025-09-07T16:28:50.989Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":90,"content":"2025-09-08 00:28:50 [\u001b[34mdebug\u001b[39m]: API Request {\"method\":\"POST\",\"url\":\"/getCityList\"}\n"},"pid":77311}
{"timestamp":"2025-09-07T16:28:50.989Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":97,"content":"debug: API Request {\"method\":\"POST\",\"timestamp\":\"2025-09-07T16:28:50.988Z\",\"url\":\"/getCityList\"}\n"},"pid":77311}
{"timestamp":"2025-09-07T16:28:51.040Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":121,"content":"2025-09-08 00:28:51 [\u001b[31merror\u001b[39m]: API Error Request failed with status code 404 {\"status\":404,\"url\":\"/getCityList\"}\n"},"pid":77311}
{"timestamp":"2025-09-07T16:28:51.041Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":128,"content":"error: API Error Request failed with status code 404 {\"status\":404,\"timestamp\":\"2025-09-07T16:28:51.040Z\",\"url\":\"/getCityList\"}\n"},"pid":77311}
{"timestamp":"2025-09-07T16:28:51.041Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":131,"content":"2025-09-08 00:28:51 [\u001b[31merror\u001b[39m]: Tool call failed {\"tool\":\"hitrip_get_cities\",\"error\":\"Request failed with status code 404\"}\n"},"pid":77311}
{"timestamp":"2025-09-07T16:28:51.041Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":138,"content":"error: Tool call failed {\"error\":\"Request failed with status code 404\",\"timestamp\":\"2025-09-07T16:28:51.041Z\",\"tool\":\"hitrip_get_cities\"}\n"},"pid":77311}
{"timestamp":"2025-09-07T16:28:51.041Z","level":"DEBUG","message":"Sending MCP response","data":{"id":4,"hasResult":true,"hasError":false},"pid":77311}
{"timestamp":"2025-09-07T16:28:52.987Z","level":"INFO","message":"Received SIGTERM, shutting down","pid":77311}
{"timestamp":"2025-09-07T16:38:02.610Z","level":"INFO","message":"Starting Hitrip MCP Server (Debug Mode)","pid":6578}
{"timestamp":"2025-09-07T16:38:02.611Z","level":"INFO","message":"Node.js version","data":{"version":"v23.11.0"},"pid":6578}
{"timestamp":"2025-09-07T16:38:02.611Z","level":"INFO","message":"Working directory","data":{"cwd":"/Users/<USER>/WorkSpace/hitrip_new_mcp"},"pid":6578}
{"timestamp":"2025-09-07T16:38:02.611Z","level":"INFO","message":"Log file","data":{"path":"/Users/<USER>/WorkSpace/hitrip_new_mcp/logs/mcp-debug-2025-09-07.log"},"pid":6578}
{"timestamp":"2025-09-07T16:38:02.612Z","level":"INFO","message":"Validating configuration","pid":6578}
{"timestamp":"2025-09-07T16:38:02.612Z","level":"DEBUG","message":"Environment variables","data":{"NODE_ENV":"development","LOG_LEVEL":"debug","HITRIP_BASE_URL":"https://api-gw.test.huangbaoche.com/","HITRIP_CID":"***","HITRIP_SECRET_KEY":"***"},"pid":6578}
{"timestamp":"2025-09-07T16:38:02.612Z","level":"INFO","message":"Configuration validated successfully","pid":6578}
{"timestamp":"2025-09-07T16:38:02.612Z","level":"INFO","message":"Creating MCP server instance","pid":6578}
{"timestamp":"2025-09-07T16:38:02.614Z","level":"INFO","message":"Starting MCP server in STDIO mode","pid":6578}
{"timestamp":"2025-09-07T16:38:02.617Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":71,"content":"2025-09-08 00:38:02 [\u001b[32minfo\u001b[39m]: MCP Server started in STDIO mode\n"},"pid":6578}
{"timestamp":"2025-09-07T16:38:02.619Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":80,"content":"info: MCP Server started in STDIO mode {\"timestamp\":\"2025-09-07T16:38:02.615Z\"}\n"},"pid":6578}
{"timestamp":"2025-09-07T16:38:02.619Z","level":"INFO","message":"Hitrip MCP Server started successfully","pid":6578}
{"timestamp":"2025-09-07T16:38:04.321Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":1,"length":170,"message":"{\"jsonrpc\":\"2.0\",\"id\":1,\"method\":\"initialize\",\"params\":{\"protocolVersion\":\"2024-11-05\",\"capabilities\":{\"tools\":{}},\"clientInfo\":{\"name\":\"test-client\",\"version\":\"1.0.0\"}}}"},"pid":6578}
{"timestamp":"2025-09-07T16:38:04.322Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":1,"method":"initialize","id":1,"hasParams":true},"pid":6578}
{"timestamp":"2025-09-07T16:38:04.325Z","level":"DEBUG","message":"Sending MCP response","data":{"id":1,"hasResult":true,"hasError":false},"pid":6578}
{"timestamp":"2025-09-07T16:38:05.321Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":2,"length":54,"message":"{\"jsonrpc\":\"2.0\",\"method\":\"notifications/initialized\"}"},"pid":6578}
{"timestamp":"2025-09-07T16:38:05.322Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":2,"method":"notifications/initialized","hasParams":false},"pid":6578}
{"timestamp":"2025-09-07T16:38:06.321Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":3,"length":46,"message":"{\"jsonrpc\":\"2.0\",\"id\":2,\"method\":\"tools/list\"}"},"pid":6578}
{"timestamp":"2025-09-07T16:38:06.321Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":3,"method":"tools/list","id":2,"hasParams":false},"pid":6578}
{"timestamp":"2025-09-07T16:38:06.322Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":76,"content":"2025-09-08 00:38:06 [\u001b[34mdebug\u001b[39m]: Listing available tools {\"count\":13}\n"},"pid":6578}
{"timestamp":"2025-09-07T16:38:06.323Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":83,"content":"debug: Listing available tools {\"count\":13,\"timestamp\":\"2025-09-07T16:38:06.322Z\"}\n"},"pid":6578}
{"timestamp":"2025-09-07T16:38:06.323Z","level":"DEBUG","message":"Sending MCP response","data":{"id":2,"hasResult":true,"hasError":false},"pid":6578}
{"timestamp":"2025-09-07T16:38:07.321Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":4,"length":98,"message":"{\"jsonrpc\":\"2.0\",\"id\":3,\"method\":\"tools/call\",\"params\":{\"name\":\"hitrip_get_token\",\"arguments\":{}}}"},"pid":6578}
{"timestamp":"2025-09-07T16:38:07.321Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":4,"method":"tools/call","id":3,"hasParams":true},"pid":6578}
{"timestamp":"2025-09-07T16:38:07.323Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":95,"content":"2025-09-08 00:38:07 [\u001b[32minfo\u001b[39m]: Tool call received {\"tool\":\"hitrip_get_token\",\"args\":[]}\n"},"pid":6578}
{"timestamp":"2025-09-07T16:38:07.323Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":102,"content":"info: Tool call received {\"args\":[],\"timestamp\":\"2025-09-07T16:38:07.322Z\",\"tool\":\"hitrip_get_token\"}\n"},"pid":6578}
{"timestamp":"2025-09-07T16:38:07.323Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":57,"content":"2025-09-08 00:38:07 [\u001b[32minfo\u001b[39m]: Fetching new token\n"},"pid":6578}
{"timestamp":"2025-09-07T16:38:07.323Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":66,"content":"info: Fetching new token {\"timestamp\":\"2025-09-07T16:38:07.323Z\"}\n"},"pid":6578}
{"timestamp":"2025-09-07T16:38:07.325Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":146,"content":"2025-09-08 00:38:07 [\u001b[34mdebug\u001b[39m]: API Request {\"method\":\"GET\",\"url\":\"/otaorder-api/access/getAccessToken?cid=1086910840&secretKey=12345678\"}\n"},"pid":6578}
{"timestamp":"2025-09-07T16:38:07.325Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":153,"content":"debug: API Request {\"method\":\"GET\",\"timestamp\":\"2025-09-07T16:38:07.324Z\",\"url\":\"/otaorder-api/access/getAccessToken?cid=1086910840&secretKey=12345678\"}\n"},"pid":6578}
{"timestamp":"2025-09-07T16:38:07.469Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":160,"content":"2025-09-08 00:38:07 [\u001b[34mdebug\u001b[39m]: API Response {\"status\":200,\"url\":\"/otaorder-api/access/getAccessToken?cid=1086910840&secretKey=12345678\",\"success\":true}\n"},"pid":6578}
{"timestamp":"2025-09-07T16:38:07.469Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":167,"content":"debug: API Response {\"status\":200,\"success\":true,\"timestamp\":\"2025-09-07T16:38:07.468Z\",\"url\":\"/otaorder-api/access/getAccessToken?cid=1086910840&secretKey=12345678\"}\n"},"pid":6578}
{"timestamp":"2025-09-07T16:38:07.469Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":66,"content":"2025-09-08 00:38:07 [\u001b[32minfo\u001b[39m]: Token obtained successfully\n"},"pid":6578}
{"timestamp":"2025-09-07T16:38:07.469Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":75,"content":"info: Token obtained successfully {\"timestamp\":\"2025-09-07T16:38:07.469Z\"}\n"},"pid":6578}
{"timestamp":"2025-09-07T16:38:07.470Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":101,"content":"2025-09-08 00:38:07 [\u001b[32minfo\u001b[39m]: Tool call completed {\"tool\":\"hitrip_get_token\",\"success\":true}\n"},"pid":6578}
{"timestamp":"2025-09-07T16:38:07.470Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":108,"content":"info: Tool call completed {\"success\":true,\"timestamp\":\"2025-09-07T16:38:07.470Z\",\"tool\":\"hitrip_get_token\"}\n"},"pid":6578}
{"timestamp":"2025-09-07T16:38:07.470Z","level":"DEBUG","message":"Sending MCP response","data":{"id":3,"hasResult":true,"hasError":false},"pid":6578}
{"timestamp":"2025-09-07T16:38:08.322Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":5,"length":112,"message":"{\"jsonrpc\":\"2.0\",\"id\":4,\"method\":\"tools/call\",\"params\":{\"name\":\"hitrip_get_cities\",\"arguments\":{\"countryId\":1}}}"},"pid":6578}
{"timestamp":"2025-09-07T16:38:08.322Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":5,"method":"tools/call","id":4,"hasParams":true},"pid":6578}
{"timestamp":"2025-09-07T16:38:08.324Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":107,"content":"2025-09-08 00:38:08 [\u001b[32minfo\u001b[39m]: Tool call received {\"tool\":\"hitrip_get_cities\",\"args\":[\"countryId\"]}\n"},"pid":6578}
{"timestamp":"2025-09-07T16:38:08.324Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":114,"content":"info: Tool call received {\"args\":[\"countryId\"],\"timestamp\":\"2025-09-07T16:38:08.323Z\",\"tool\":\"hitrip_get_cities\"}\n"},"pid":6578}
{"timestamp":"2025-09-07T16:38:08.325Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":58,"content":"2025-09-08 00:38:08 [\u001b[34mdebug\u001b[39m]: Using cached token\n"},"pid":6578}
{"timestamp":"2025-09-07T16:38:08.325Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":67,"content":"debug: Using cached token {\"timestamp\":\"2025-09-07T16:38:08.324Z\"}\n"},"pid":6578}
{"timestamp":"2025-09-07T16:38:08.327Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":504,"content":"2025-09-08 00:38:08 [\u001b[34mdebug\u001b[39m]: API Request {\"method\":\"POST\",\"url\":\"/otaorder-api/common/v1.0/cities/listByNameAndSpell?token=26A2DED6136A8F6BDCD27DF8DE7B6056747FA1A0F6AE1FBE10301F28E25AB099D88"},"pid":6578}
{"timestamp":"2025-09-07T16:38:08.327Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":511,"content":"debug: API Request {\"method\":\"POST\",\"timestamp\":\"2025-09-07T16:38:08.326Z\",\"url\":\"/otaorder-api/common/v1.0/cities/listByNameAndSpell?token=26A2DED6136A8F6BDCD27DF8DE7B6056747FA1A0F6AE1FBE10301F28E25A"},"pid":6578}
{"timestamp":"2025-09-07T16:38:09.643Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":517,"content":"2025-09-08 00:38:09 [\u001b[34mdebug\u001b[39m]: API Response {\"status\":200,\"url\":\"/otaorder-api/common/v1.0/cities/listByNameAndSpell?token=26A2DED6136A8F6BDCD27DF8DE7B6056747FA1A0F6AE1FBE10301F28E25AB099D88EF"},"pid":6578}
{"timestamp":"2025-09-07T16:38:09.643Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":524,"content":"debug: API Response {\"status\":200,\"success\":true,\"timestamp\":\"2025-09-07T16:38:09.642Z\",\"url\":\"/otaorder-api/common/v1.0/cities/listByNameAndSpell?token=26A2DED6136A8F6BDCD27DF8DE7B6056747FA1A0F6AE1FB"},"pid":6578}
{"timestamp":"2025-09-07T16:38:09.643Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":102,"content":"2025-09-08 00:38:09 [\u001b[32minfo\u001b[39m]: Tool call completed {\"tool\":\"hitrip_get_cities\",\"success\":true}\n"},"pid":6578}
{"timestamp":"2025-09-07T16:38:09.643Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":109,"content":"info: Tool call completed {\"success\":true,\"timestamp\":\"2025-09-07T16:38:09.643Z\",\"tool\":\"hitrip_get_cities\"}\n"},"pid":6578}
{"timestamp":"2025-09-07T16:38:09.662Z","level":"DEBUG","message":"Sending MCP response","data":{"id":4,"hasResult":true,"hasError":false},"pid":6578}
{"timestamp":"2025-09-07T16:38:10.322Z","level":"INFO","message":"Received SIGTERM, shutting down","pid":6578}
{"timestamp":"2025-09-07T16:45:00.206Z","level":"INFO","message":"Starting Hitrip MCP Server (Debug Mode)","pid":29143}
{"timestamp":"2025-09-07T16:45:00.207Z","level":"INFO","message":"Node.js version","data":{"version":"v23.11.0"},"pid":29143}
{"timestamp":"2025-09-07T16:45:00.208Z","level":"INFO","message":"Working directory","data":{"cwd":"/Users/<USER>/WorkSpace/hitrip_new_mcp"},"pid":29143}
{"timestamp":"2025-09-07T16:45:00.208Z","level":"INFO","message":"Log file","data":{"path":"/Users/<USER>/WorkSpace/hitrip_new_mcp/logs/mcp-debug-2025-09-07.log"},"pid":29143}
{"timestamp":"2025-09-07T16:45:00.208Z","level":"INFO","message":"Validating configuration","pid":29143}
{"timestamp":"2025-09-07T16:45:00.210Z","level":"DEBUG","message":"Environment variables","data":{"NODE_ENV":"development","LOG_LEVEL":"debug","HITRIP_BASE_URL":"https://api-gw.huangbaoche.com/","HITRIP_CID":"***","HITRIP_SECRET_KEY":"***"},"pid":29143}
{"timestamp":"2025-09-07T16:45:00.210Z","level":"INFO","message":"Configuration validated successfully","pid":29143}
{"timestamp":"2025-09-07T16:45:00.210Z","level":"INFO","message":"Creating MCP server instance","pid":29143}
{"timestamp":"2025-09-07T16:45:00.213Z","level":"INFO","message":"Starting MCP server in STDIO mode","pid":29143}
{"timestamp":"2025-09-07T16:45:00.219Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":71,"content":"2025-09-08 00:45:00 [\u001b[32minfo\u001b[39m]: MCP Server started in STDIO mode\n"},"pid":29143}
{"timestamp":"2025-09-07T16:45:00.220Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":80,"content":"info: MCP Server started in STDIO mode {\"timestamp\":\"2025-09-07T16:45:00.217Z\"}\n"},"pid":29143}
{"timestamp":"2025-09-07T16:45:00.220Z","level":"INFO","message":"Hitrip MCP Server started successfully","pid":29143}
{"timestamp":"2025-09-07T16:45:00.934Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":1,"length":172,"message":"{\"jsonrpc\":\"2.0\",\"id\":1,\"method\":\"initialize\",\"params\":{\"protocolVersion\":\"2024-11-05\",\"capabilities\":{\"tools\":{}},\"clientInfo\":{\"name\":\"cherry-studio\",\"version\":\"1.0.0\"}}}"},"pid":29143}
{"timestamp":"2025-09-07T16:45:00.935Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":1,"method":"initialize","id":1,"hasParams":true},"pid":29143}
{"timestamp":"2025-09-07T16:45:00.937Z","level":"DEBUG","message":"Sending MCP response","data":{"id":1,"hasResult":true,"hasError":false},"pid":29143}
{"timestamp":"2025-09-07T16:45:01.934Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":2,"length":54,"message":"{\"jsonrpc\":\"2.0\",\"method\":\"notifications/initialized\"}"},"pid":29143}
{"timestamp":"2025-09-07T16:45:01.934Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":2,"method":"notifications/initialized","hasParams":false},"pid":29143}
{"timestamp":"2025-09-07T16:45:02.934Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":3,"length":46,"message":"{\"jsonrpc\":\"2.0\",\"id\":2,\"method\":\"tools/list\"}"},"pid":29143}
{"timestamp":"2025-09-07T16:45:02.935Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":3,"method":"tools/list","id":2,"hasParams":false},"pid":29143}
{"timestamp":"2025-09-07T16:45:02.936Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":76,"content":"2025-09-08 00:45:02 [\u001b[34mdebug\u001b[39m]: Listing available tools {\"count\":13}\n"},"pid":29143}
{"timestamp":"2025-09-07T16:45:02.937Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":83,"content":"debug: Listing available tools {\"count\":13,\"timestamp\":\"2025-09-07T16:45:02.935Z\"}\n"},"pid":29143}
{"timestamp":"2025-09-07T16:45:02.937Z","level":"DEBUG","message":"Sending MCP response","data":{"id":2,"hasResult":true,"hasError":false},"pid":29143}
{"timestamp":"2025-09-07T16:45:03.934Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":4,"length":98,"message":"{\"jsonrpc\":\"2.0\",\"id\":3,\"method\":\"tools/call\",\"params\":{\"name\":\"hitrip_get_token\",\"arguments\":{}}}"},"pid":29143}
{"timestamp":"2025-09-07T16:45:03.934Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":4,"method":"tools/call","id":3,"hasParams":true},"pid":29143}
{"timestamp":"2025-09-07T16:45:03.937Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":95,"content":"2025-09-08 00:45:03 [\u001b[32minfo\u001b[39m]: Tool call received {\"tool\":\"hitrip_get_token\",\"args\":[]}\n"},"pid":29143}
{"timestamp":"2025-09-07T16:45:03.938Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":102,"content":"info: Tool call received {\"args\":[],\"timestamp\":\"2025-09-07T16:45:03.937Z\",\"tool\":\"hitrip_get_token\"}\n"},"pid":29143}
{"timestamp":"2025-09-07T16:45:03.938Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":57,"content":"2025-09-08 00:45:03 [\u001b[32minfo\u001b[39m]: Fetching new token\n"},"pid":29143}
{"timestamp":"2025-09-07T16:45:03.938Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":66,"content":"info: Fetching new token {\"timestamp\":\"2025-09-07T16:45:03.938Z\"}\n"},"pid":29143}
{"timestamp":"2025-09-07T16:45:03.942Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":146,"content":"2025-09-08 00:45:03 [\u001b[34mdebug\u001b[39m]: API Request {\"method\":\"GET\",\"url\":\"/otaorder-api/access/getAccessToken?cid=1086910840&secretKey=12345678\"}\n"},"pid":29143}
{"timestamp":"2025-09-07T16:45:03.943Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":153,"content":"debug: API Request {\"method\":\"GET\",\"timestamp\":\"2025-09-07T16:45:03.941Z\",\"url\":\"/otaorder-api/access/getAccessToken?cid=1086910840&secretKey=12345678\"}\n"},"pid":29143}
{"timestamp":"2025-09-07T16:45:04.200Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":161,"content":"2025-09-08 00:45:04 [\u001b[34mdebug\u001b[39m]: API Response {\"status\":200,\"url\":\"/otaorder-api/access/getAccessToken?cid=1086910840&secretKey=12345678\",\"success\":false}\n"},"pid":29143}
{"timestamp":"2025-09-07T16:45:04.200Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":168,"content":"debug: API Response {\"status\":200,\"success\":false,\"timestamp\":\"2025-09-07T16:45:04.200Z\",\"url\":\"/otaorder-api/access/getAccessToken?cid=1086910840&secretKey=12345678\"}\n"},"pid":29143}
{"timestamp":"2025-09-07T16:45:04.201Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":72,"content":"2025-09-08 00:45:04 [\u001b[31merror\u001b[39m]: Failed to get token {\"error\":{}}\n"},"pid":29143}
{"timestamp":"2025-09-07T16:45:04.201Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":79,"content":"error: Failed to get token {\"error\":{},\"timestamp\":\"2025-09-07T16:45:04.201Z\"}\n"},"pid":29143}
{"timestamp":"2025-09-07T16:45:04.201Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":124,"content":"2025-09-08 00:45:04 [\u001b[31merror\u001b[39m]: Tool call failed {\"tool\":\"hitrip_get_token\",\"error\":\"Token request failed: 9330005\"}\n"},"pid":29143}
{"timestamp":"2025-09-07T16:45:04.201Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":131,"content":"error: Tool call failed {\"error\":\"Token request failed: 9330005\",\"timestamp\":\"2025-09-07T16:45:04.201Z\",\"tool\":\"hitrip_get_token\"}\n"},"pid":29143}
{"timestamp":"2025-09-07T16:45:04.201Z","level":"DEBUG","message":"Sending MCP response","data":{"id":3,"hasResult":true,"hasError":false},"pid":29143}
{"timestamp":"2025-09-07T16:45:04.933Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":5,"length":120,"message":"{\"jsonrpc\":\"2.0\",\"id\":4,\"method\":\"tools/call\",\"params\":{\"name\":\"hitrip_get_airports\",\"arguments\":{\"keyword\":\"Beijing\"}}}"},"pid":29143}
{"timestamp":"2025-09-07T16:45:04.933Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":5,"method":"tools/call","id":4,"hasParams":true},"pid":29143}
{"timestamp":"2025-09-07T16:45:04.934Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":107,"content":"2025-09-08 00:45:04 [\u001b[32minfo\u001b[39m]: Tool call received {\"tool\":\"hitrip_get_airports\",\"args\":[\"keyword\"]}\n"},"pid":29143}
{"timestamp":"2025-09-07T16:45:04.934Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":114,"content":"info: Tool call received {\"args\":[\"keyword\"],\"timestamp\":\"2025-09-07T16:45:04.934Z\",\"tool\":\"hitrip_get_airports\"}\n"},"pid":29143}
{"timestamp":"2025-09-07T16:45:04.936Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":57,"content":"2025-09-08 00:45:04 [\u001b[32minfo\u001b[39m]: Fetching new token\n"},"pid":29143}
{"timestamp":"2025-09-07T16:45:04.936Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":66,"content":"info: Fetching new token {\"timestamp\":\"2025-09-07T16:45:04.935Z\"}\n"},"pid":29143}
{"timestamp":"2025-09-07T16:45:04.936Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":146,"content":"2025-09-08 00:45:04 [\u001b[34mdebug\u001b[39m]: API Request {\"method\":\"GET\",\"url\":\"/otaorder-api/access/getAccessToken?cid=1086910840&secretKey=12345678\"}\n"},"pid":29143}
{"timestamp":"2025-09-07T16:45:04.936Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":153,"content":"debug: API Request {\"method\":\"GET\",\"timestamp\":\"2025-09-07T16:45:04.936Z\",\"url\":\"/otaorder-api/access/getAccessToken?cid=1086910840&secretKey=12345678\"}\n"},"pid":29143}
{"timestamp":"2025-09-07T16:45:05.143Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":161,"content":"2025-09-08 00:45:05 [\u001b[34mdebug\u001b[39m]: API Response {\"status\":200,\"url\":\"/otaorder-api/access/getAccessToken?cid=1086910840&secretKey=12345678\",\"success\":false}\n"},"pid":29143}
{"timestamp":"2025-09-07T16:45:05.143Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":168,"content":"debug: API Response {\"status\":200,\"success\":false,\"timestamp\":\"2025-09-07T16:45:05.143Z\",\"url\":\"/otaorder-api/access/getAccessToken?cid=1086910840&secretKey=12345678\"}\n"},"pid":29143}
{"timestamp":"2025-09-07T16:45:05.144Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":72,"content":"2025-09-08 00:45:05 [\u001b[31merror\u001b[39m]: Failed to get token {\"error\":{}}\n"},"pid":29143}
{"timestamp":"2025-09-07T16:45:05.144Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":79,"content":"error: Failed to get token {\"error\":{},\"timestamp\":\"2025-09-07T16:45:05.144Z\"}\n"},"pid":29143}
{"timestamp":"2025-09-07T16:45:05.144Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":127,"content":"2025-09-08 00:45:05 [\u001b[31merror\u001b[39m]: Tool call failed {\"tool\":\"hitrip_get_airports\",\"error\":\"Token request failed: 9330005\"}\n"},"pid":29143}
{"timestamp":"2025-09-07T16:45:05.144Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":134,"content":"error: Tool call failed {\"error\":\"Token request failed: 9330005\",\"timestamp\":\"2025-09-07T16:45:05.144Z\",\"tool\":\"hitrip_get_airports\"}\n"},"pid":29143}
{"timestamp":"2025-09-07T16:45:05.144Z","level":"DEBUG","message":"Sending MCP response","data":{"id":4,"hasResult":true,"hasError":false},"pid":29143}
{"timestamp":"2025-09-07T16:45:06.934Z","level":"INFO","message":"Received SIGTERM, shutting down","pid":29143}
{"timestamp":"2025-09-07T16:48:16.216Z","level":"INFO","message":"Starting Hitrip MCP Server (Debug Mode)","pid":39318}
{"timestamp":"2025-09-07T16:48:16.218Z","level":"INFO","message":"Node.js version","data":{"version":"v23.11.0"},"pid":39318}
{"timestamp":"2025-09-07T16:48:16.218Z","level":"INFO","message":"Working directory","data":{"cwd":"/Users/<USER>/WorkSpace/hitrip_new_mcp"},"pid":39318}
{"timestamp":"2025-09-07T16:48:16.218Z","level":"INFO","message":"Log file","data":{"path":"/Users/<USER>/WorkSpace/hitrip_new_mcp/logs/mcp-debug-2025-09-07.log"},"pid":39318}
{"timestamp":"2025-09-07T16:48:16.219Z","level":"INFO","message":"Validating configuration","pid":39318}
{"timestamp":"2025-09-07T16:48:16.219Z","level":"DEBUG","message":"Environment variables","data":{"NODE_ENV":"development","LOG_LEVEL":"debug","HITRIP_BASE_URL":"https://api-gw.huangbaoche.com/","HITRIP_CID":"***","HITRIP_SECRET_KEY":"***"},"pid":39318}
{"timestamp":"2025-09-07T16:48:16.219Z","level":"INFO","message":"Configuration validated successfully","pid":39318}
{"timestamp":"2025-09-07T16:48:16.219Z","level":"INFO","message":"Creating MCP server instance","pid":39318}
{"timestamp":"2025-09-07T16:48:16.221Z","level":"INFO","message":"Starting MCP server in STDIO mode","pid":39318}
{"timestamp":"2025-09-07T16:48:16.223Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":71,"content":"2025-09-08 00:48:16 [\u001b[32minfo\u001b[39m]: MCP Server started in STDIO mode\n"},"pid":39318}
{"timestamp":"2025-09-07T16:48:16.226Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":80,"content":"info: MCP Server started in STDIO mode {\"timestamp\":\"2025-09-07T16:48:16.222Z\"}\n"},"pid":39318}
{"timestamp":"2025-09-07T16:48:16.227Z","level":"INFO","message":"Hitrip MCP Server started successfully","pid":39318}
{"timestamp":"2025-09-07T16:48:16.945Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":1,"length":172,"message":"{\"jsonrpc\":\"2.0\",\"id\":1,\"method\":\"initialize\",\"params\":{\"protocolVersion\":\"2024-11-05\",\"capabilities\":{\"tools\":{}},\"clientInfo\":{\"name\":\"cherry-studio\",\"version\":\"1.0.0\"}}}"},"pid":39318}
{"timestamp":"2025-09-07T16:48:16.946Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":1,"method":"initialize","id":1,"hasParams":true},"pid":39318}
{"timestamp":"2025-09-07T16:48:16.950Z","level":"DEBUG","message":"Sending MCP response","data":{"id":1,"hasResult":true,"hasError":false},"pid":39318}
{"timestamp":"2025-09-07T16:48:17.945Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":2,"length":54,"message":"{\"jsonrpc\":\"2.0\",\"method\":\"notifications/initialized\"}"},"pid":39318}
{"timestamp":"2025-09-07T16:48:17.945Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":2,"method":"notifications/initialized","hasParams":false},"pid":39318}
{"timestamp":"2025-09-07T16:48:18.945Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":3,"length":46,"message":"{\"jsonrpc\":\"2.0\",\"id\":2,\"method\":\"tools/list\"}"},"pid":39318}
{"timestamp":"2025-09-07T16:48:18.945Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":3,"method":"tools/list","id":2,"hasParams":false},"pid":39318}
{"timestamp":"2025-09-07T16:48:18.948Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":76,"content":"2025-09-08 00:48:18 [\u001b[34mdebug\u001b[39m]: Listing available tools {\"count\":13}\n"},"pid":39318}
{"timestamp":"2025-09-07T16:48:18.949Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":83,"content":"debug: Listing available tools {\"count\":13,\"timestamp\":\"2025-09-07T16:48:18.947Z\"}\n"},"pid":39318}
{"timestamp":"2025-09-07T16:48:18.950Z","level":"DEBUG","message":"Sending MCP response","data":{"id":2,"hasResult":true,"hasError":false},"pid":39318}
{"timestamp":"2025-09-07T16:48:19.968Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":4,"length":98,"message":"{\"jsonrpc\":\"2.0\",\"id\":3,\"method\":\"tools/call\",\"params\":{\"name\":\"hitrip_get_token\",\"arguments\":{}}}"},"pid":39318}
{"timestamp":"2025-09-07T16:48:19.972Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":4,"method":"tools/call","id":3,"hasParams":true},"pid":39318}
{"timestamp":"2025-09-07T16:48:20.006Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":95,"content":"2025-09-08 00:48:20 [\u001b[32minfo\u001b[39m]: Tool call received {\"tool\":\"hitrip_get_token\",\"args\":[]}\n"},"pid":39318}
{"timestamp":"2025-09-07T16:48:20.013Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":102,"content":"info: Tool call received {\"args\":[],\"timestamp\":\"2025-09-07T16:48:20.003Z\",\"tool\":\"hitrip_get_token\"}\n"},"pid":39318}
{"timestamp":"2025-09-07T16:48:20.023Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":57,"content":"2025-09-08 00:48:20 [\u001b[32minfo\u001b[39m]: Fetching new token\n"},"pid":39318}
{"timestamp":"2025-09-07T16:48:20.023Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":66,"content":"info: Fetching new token {\"timestamp\":\"2025-09-07T16:48:20.021Z\"}\n"},"pid":39318}
{"timestamp":"2025-09-07T16:48:20.048Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":146,"content":"2025-09-08 00:48:20 [\u001b[34mdebug\u001b[39m]: API Request {\"method\":\"GET\",\"url\":\"/otaorder-api/access/getAccessToken?cid=1086910840&secretKey=12345678\"}\n"},"pid":39318}
{"timestamp":"2025-09-07T16:48:20.049Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":153,"content":"debug: API Request {\"method\":\"GET\",\"timestamp\":\"2025-09-07T16:48:20.045Z\",\"url\":\"/otaorder-api/access/getAccessToken?cid=1086910840&secretKey=12345678\"}\n"},"pid":39318}
{"timestamp":"2025-09-07T16:48:20.368Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":161,"content":"2025-09-08 00:48:20 [\u001b[34mdebug\u001b[39m]: API Response {\"status\":200,\"url\":\"/otaorder-api/access/getAccessToken?cid=1086910840&secretKey=12345678\",\"success\":false}\n"},"pid":39318}
{"timestamp":"2025-09-07T16:48:20.368Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":168,"content":"debug: API Response {\"status\":200,\"success\":false,\"timestamp\":\"2025-09-07T16:48:20.367Z\",\"url\":\"/otaorder-api/access/getAccessToken?cid=1086910840&secretKey=12345678\"}\n"},"pid":39318}
{"timestamp":"2025-09-07T16:48:20.368Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":72,"content":"2025-09-08 00:48:20 [\u001b[31merror\u001b[39m]: Failed to get token {\"error\":{}}\n"},"pid":39318}
{"timestamp":"2025-09-07T16:48:20.368Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":79,"content":"error: Failed to get token {\"error\":{},\"timestamp\":\"2025-09-07T16:48:20.368Z\"}\n"},"pid":39318}
{"timestamp":"2025-09-07T16:48:20.369Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":124,"content":"2025-09-08 00:48:20 [\u001b[31merror\u001b[39m]: Tool call failed {\"tool\":\"hitrip_get_token\",\"error\":\"Token request failed: 9330005\"}\n"},"pid":39318}
{"timestamp":"2025-09-07T16:48:20.369Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":131,"content":"error: Tool call failed {\"error\":\"Token request failed: 9330005\",\"timestamp\":\"2025-09-07T16:48:20.368Z\",\"tool\":\"hitrip_get_token\"}\n"},"pid":39318}
{"timestamp":"2025-09-07T16:48:20.369Z","level":"DEBUG","message":"Sending MCP response","data":{"id":3,"hasResult":true,"hasError":false},"pid":39318}
{"timestamp":"2025-09-07T16:48:20.946Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":5,"length":120,"message":"{\"jsonrpc\":\"2.0\",\"id\":4,\"method\":\"tools/call\",\"params\":{\"name\":\"hitrip_get_airports\",\"arguments\":{\"keyword\":\"Beijing\"}}}"},"pid":39318}
{"timestamp":"2025-09-07T16:48:20.946Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":5,"method":"tools/call","id":4,"hasParams":true},"pid":39318}
{"timestamp":"2025-09-07T16:48:20.948Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":107,"content":"2025-09-08 00:48:20 [\u001b[32minfo\u001b[39m]: Tool call received {\"tool\":\"hitrip_get_airports\",\"args\":[\"keyword\"]}\n"},"pid":39318}
{"timestamp":"2025-09-07T16:48:20.949Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":114,"content":"info: Tool call received {\"args\":[\"keyword\"],\"timestamp\":\"2025-09-07T16:48:20.947Z\",\"tool\":\"hitrip_get_airports\"}\n"},"pid":39318}
{"timestamp":"2025-09-07T16:48:20.950Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":57,"content":"2025-09-08 00:48:20 [\u001b[32minfo\u001b[39m]: Fetching new token\n"},"pid":39318}
{"timestamp":"2025-09-07T16:48:20.950Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":66,"content":"info: Fetching new token {\"timestamp\":\"2025-09-07T16:48:20.949Z\"}\n"},"pid":39318}
{"timestamp":"2025-09-07T16:48:20.951Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":146,"content":"2025-09-08 00:48:20 [\u001b[34mdebug\u001b[39m]: API Request {\"method\":\"GET\",\"url\":\"/otaorder-api/access/getAccessToken?cid=1086910840&secretKey=12345678\"}\n"},"pid":39318}
{"timestamp":"2025-09-07T16:48:20.952Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":153,"content":"debug: API Request {\"method\":\"GET\",\"timestamp\":\"2025-09-07T16:48:20.951Z\",\"url\":\"/otaorder-api/access/getAccessToken?cid=1086910840&secretKey=12345678\"}\n"},"pid":39318}
{"timestamp":"2025-09-07T16:48:21.177Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":161,"content":"2025-09-08 00:48:21 [\u001b[34mdebug\u001b[39m]: API Response {\"status\":200,\"url\":\"/otaorder-api/access/getAccessToken?cid=1086910840&secretKey=12345678\",\"success\":false}\n"},"pid":39318}
{"timestamp":"2025-09-07T16:48:21.178Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":168,"content":"debug: API Response {\"status\":200,\"success\":false,\"timestamp\":\"2025-09-07T16:48:21.177Z\",\"url\":\"/otaorder-api/access/getAccessToken?cid=1086910840&secretKey=12345678\"}\n"},"pid":39318}
{"timestamp":"2025-09-07T16:48:21.178Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":72,"content":"2025-09-08 00:48:21 [\u001b[31merror\u001b[39m]: Failed to get token {\"error\":{}}\n"},"pid":39318}
{"timestamp":"2025-09-07T16:48:21.178Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":79,"content":"error: Failed to get token {\"error\":{},\"timestamp\":\"2025-09-07T16:48:21.178Z\"}\n"},"pid":39318}
{"timestamp":"2025-09-07T16:48:21.180Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":127,"content":"2025-09-08 00:48:21 [\u001b[31merror\u001b[39m]: Tool call failed {\"tool\":\"hitrip_get_airports\",\"error\":\"Token request failed: 9330005\"}\n"},"pid":39318}
{"timestamp":"2025-09-07T16:48:21.181Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":134,"content":"error: Tool call failed {\"error\":\"Token request failed: 9330005\",\"timestamp\":\"2025-09-07T16:48:21.179Z\",\"tool\":\"hitrip_get_airports\"}\n"},"pid":39318}
{"timestamp":"2025-09-07T16:48:21.181Z","level":"DEBUG","message":"Sending MCP response","data":{"id":4,"hasResult":true,"hasError":false},"pid":39318}
{"timestamp":"2025-09-07T16:48:22.946Z","level":"INFO","message":"Received SIGTERM, shutting down","pid":39318}
{"timestamp":"2025-09-07T16:48:54.522Z","level":"INFO","message":"Starting Hitrip MCP Server (Debug Mode)","pid":41325}
{"timestamp":"2025-09-07T16:48:54.523Z","level":"INFO","message":"Node.js version","data":{"version":"v23.11.0"},"pid":41325}
{"timestamp":"2025-09-07T16:48:54.523Z","level":"INFO","message":"Working directory","data":{"cwd":"/Users/<USER>/WorkSpace/hitrip_new_mcp"},"pid":41325}
{"timestamp":"2025-09-07T16:48:54.523Z","level":"INFO","message":"Log file","data":{"path":"/Users/<USER>/WorkSpace/hitrip_new_mcp/logs/mcp-debug-2025-09-07.log"},"pid":41325}
{"timestamp":"2025-09-07T16:48:54.524Z","level":"INFO","message":"Validating configuration","pid":41325}
{"timestamp":"2025-09-07T16:48:54.524Z","level":"DEBUG","message":"Environment variables","data":{"NODE_ENV":"development","LOG_LEVEL":"debug","HITRIP_BASE_URL":"https://api-gw.test.huangbaoche.com/","HITRIP_CID":"***","HITRIP_SECRET_KEY":"***"},"pid":41325}
{"timestamp":"2025-09-07T16:48:54.524Z","level":"INFO","message":"Configuration validated successfully","pid":41325}
{"timestamp":"2025-09-07T16:48:54.524Z","level":"INFO","message":"Creating MCP server instance","pid":41325}
{"timestamp":"2025-09-07T16:48:54.526Z","level":"INFO","message":"Starting MCP server in STDIO mode","pid":41325}
{"timestamp":"2025-09-07T16:48:54.530Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":71,"content":"2025-09-08 00:48:54 [\u001b[32minfo\u001b[39m]: MCP Server started in STDIO mode\n"},"pid":41325}
{"timestamp":"2025-09-07T16:48:54.532Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":80,"content":"info: MCP Server started in STDIO mode {\"timestamp\":\"2025-09-07T16:48:54.528Z\"}\n"},"pid":41325}
{"timestamp":"2025-09-07T16:48:54.532Z","level":"INFO","message":"Hitrip MCP Server started successfully","pid":41325}
{"timestamp":"2025-09-07T16:48:55.229Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":1,"length":172,"message":"{\"jsonrpc\":\"2.0\",\"id\":1,\"method\":\"initialize\",\"params\":{\"protocolVersion\":\"2024-11-05\",\"capabilities\":{\"tools\":{}},\"clientInfo\":{\"name\":\"cherry-studio\",\"version\":\"1.0.0\"}}}"},"pid":41325}
{"timestamp":"2025-09-07T16:48:55.229Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":1,"method":"initialize","id":1,"hasParams":true},"pid":41325}
{"timestamp":"2025-09-07T16:48:55.232Z","level":"DEBUG","message":"Sending MCP response","data":{"id":1,"hasResult":true,"hasError":false},"pid":41325}
{"timestamp":"2025-09-07T16:48:56.228Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":2,"length":54,"message":"{\"jsonrpc\":\"2.0\",\"method\":\"notifications/initialized\"}"},"pid":41325}
{"timestamp":"2025-09-07T16:48:56.229Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":2,"method":"notifications/initialized","hasParams":false},"pid":41325}
{"timestamp":"2025-09-07T16:48:57.228Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":3,"length":46,"message":"{\"jsonrpc\":\"2.0\",\"id\":2,\"method\":\"tools/list\"}"},"pid":41325}
{"timestamp":"2025-09-07T16:48:57.228Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":3,"method":"tools/list","id":2,"hasParams":false},"pid":41325}
{"timestamp":"2025-09-07T16:48:57.231Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":76,"content":"2025-09-08 00:48:57 [\u001b[34mdebug\u001b[39m]: Listing available tools {\"count\":13}\n"},"pid":41325}
{"timestamp":"2025-09-07T16:48:57.233Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":83,"content":"debug: Listing available tools {\"count\":13,\"timestamp\":\"2025-09-07T16:48:57.230Z\"}\n"},"pid":41325}
{"timestamp":"2025-09-07T16:48:57.234Z","level":"DEBUG","message":"Sending MCP response","data":{"id":2,"hasResult":true,"hasError":false},"pid":41325}
{"timestamp":"2025-09-07T16:48:58.227Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":4,"length":98,"message":"{\"jsonrpc\":\"2.0\",\"id\":3,\"method\":\"tools/call\",\"params\":{\"name\":\"hitrip_get_token\",\"arguments\":{}}}"},"pid":41325}
{"timestamp":"2025-09-07T16:48:58.227Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":4,"method":"tools/call","id":3,"hasParams":true},"pid":41325}
{"timestamp":"2025-09-07T16:48:58.231Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":95,"content":"2025-09-08 00:48:58 [\u001b[32minfo\u001b[39m]: Tool call received {\"tool\":\"hitrip_get_token\",\"args\":[]}\n"},"pid":41325}
{"timestamp":"2025-09-07T16:48:58.232Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":102,"content":"info: Tool call received {\"args\":[],\"timestamp\":\"2025-09-07T16:48:58.231Z\",\"tool\":\"hitrip_get_token\"}\n"},"pid":41325}
{"timestamp":"2025-09-07T16:48:58.233Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":57,"content":"2025-09-08 00:48:58 [\u001b[32minfo\u001b[39m]: Fetching new token\n"},"pid":41325}
{"timestamp":"2025-09-07T16:48:58.233Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":66,"content":"info: Fetching new token {\"timestamp\":\"2025-09-07T16:48:58.232Z\"}\n"},"pid":41325}
{"timestamp":"2025-09-07T16:48:58.236Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":146,"content":"2025-09-08 00:48:58 [\u001b[34mdebug\u001b[39m]: API Request {\"method\":\"GET\",\"url\":\"/otaorder-api/access/getAccessToken?cid=1086910840&secretKey=12345678\"}\n"},"pid":41325}
{"timestamp":"2025-09-07T16:48:58.237Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":153,"content":"debug: API Request {\"method\":\"GET\",\"timestamp\":\"2025-09-07T16:48:58.235Z\",\"url\":\"/otaorder-api/access/getAccessToken?cid=1086910840&secretKey=12345678\"}\n"},"pid":41325}
{"timestamp":"2025-09-07T16:48:58.347Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":160,"content":"2025-09-08 00:48:58 [\u001b[34mdebug\u001b[39m]: API Response {\"status\":200,\"url\":\"/otaorder-api/access/getAccessToken?cid=1086910840&secretKey=12345678\",\"success\":true}\n"},"pid":41325}
{"timestamp":"2025-09-07T16:48:58.348Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":167,"content":"debug: API Response {\"status\":200,\"success\":true,\"timestamp\":\"2025-09-07T16:48:58.347Z\",\"url\":\"/otaorder-api/access/getAccessToken?cid=1086910840&secretKey=12345678\"}\n"},"pid":41325}
{"timestamp":"2025-09-07T16:48:58.348Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":66,"content":"2025-09-08 00:48:58 [\u001b[32minfo\u001b[39m]: Token obtained successfully\n"},"pid":41325}
{"timestamp":"2025-09-07T16:48:58.348Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":75,"content":"info: Token obtained successfully {\"timestamp\":\"2025-09-07T16:48:58.348Z\"}\n"},"pid":41325}
{"timestamp":"2025-09-07T16:48:58.348Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":101,"content":"2025-09-08 00:48:58 [\u001b[32minfo\u001b[39m]: Tool call completed {\"tool\":\"hitrip_get_token\",\"success\":true}\n"},"pid":41325}
{"timestamp":"2025-09-07T16:48:58.348Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":108,"content":"info: Tool call completed {\"success\":true,\"timestamp\":\"2025-09-07T16:48:58.348Z\",\"tool\":\"hitrip_get_token\"}\n"},"pid":41325}
{"timestamp":"2025-09-07T16:48:58.348Z","level":"DEBUG","message":"Sending MCP response","data":{"id":3,"hasResult":true,"hasError":false},"pid":41325}
{"timestamp":"2025-09-07T16:48:59.230Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":5,"length":120,"message":"{\"jsonrpc\":\"2.0\",\"id\":4,\"method\":\"tools/call\",\"params\":{\"name\":\"hitrip_get_airports\",\"arguments\":{\"keyword\":\"Beijing\"}}}"},"pid":41325}
{"timestamp":"2025-09-07T16:48:59.230Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":5,"method":"tools/call","id":4,"hasParams":true},"pid":41325}
{"timestamp":"2025-09-07T16:48:59.232Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":107,"content":"2025-09-08 00:48:59 [\u001b[32minfo\u001b[39m]: Tool call received {\"tool\":\"hitrip_get_airports\",\"args\":[\"keyword\"]}\n"},"pid":41325}
{"timestamp":"2025-09-07T16:48:59.233Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":114,"content":"info: Tool call received {\"args\":[\"keyword\"],\"timestamp\":\"2025-09-07T16:48:59.232Z\",\"tool\":\"hitrip_get_airports\"}\n"},"pid":41325}
{"timestamp":"2025-09-07T16:48:59.234Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":58,"content":"2025-09-08 00:48:59 [\u001b[34mdebug\u001b[39m]: Using cached token\n"},"pid":41325}
{"timestamp":"2025-09-07T16:48:59.234Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":67,"content":"debug: Using cached token {\"timestamp\":\"2025-09-07T16:48:59.234Z\"}\n"},"pid":41325}
{"timestamp":"2025-09-07T16:48:59.236Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":505,"content":"2025-09-08 00:48:59 [\u001b[34mdebug\u001b[39m]: API Request {\"method\":\"POST\",\"url\":\"/otaorder-api/common/v1.0/airport/listByNameAndSpell?token=26A2DED6136A8F6BDCD27DF8DE7B6056747FA1A0F6AE1FBE10301F28E25AB099D8"},"pid":41325}
{"timestamp":"2025-09-07T16:48:59.236Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":512,"content":"debug: API Request {\"method\":\"POST\",\"timestamp\":\"2025-09-07T16:48:59.236Z\",\"url\":\"/otaorder-api/common/v1.0/airport/listByNameAndSpell?token=26A2DED6136A8F6BDCD27DF8DE7B6056747FA1A0F6AE1FBE10301F28E25"},"pid":41325}
{"timestamp":"2025-09-07T16:48:59.507Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":518,"content":"2025-09-08 00:48:59 [\u001b[34mdebug\u001b[39m]: API Response {\"status\":200,\"url\":\"/otaorder-api/common/v1.0/airport/listByNameAndSpell?token=26A2DED6136A8F6BDCD27DF8DE7B6056747FA1A0F6AE1FBE10301F28E25AB099D88E"},"pid":41325}
{"timestamp":"2025-09-07T16:48:59.507Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":525,"content":"debug: API Response {\"status\":200,\"success\":true,\"timestamp\":\"2025-09-07T16:48:59.507Z\",\"url\":\"/otaorder-api/common/v1.0/airport/listByNameAndSpell?token=26A2DED6136A8F6BDCD27DF8DE7B6056747FA1A0F6AE1F"},"pid":41325}
{"timestamp":"2025-09-07T16:48:59.507Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":104,"content":"2025-09-08 00:48:59 [\u001b[32minfo\u001b[39m]: Tool call completed {\"tool\":\"hitrip_get_airports\",\"success\":true}\n"},"pid":41325}
{"timestamp":"2025-09-07T16:48:59.507Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":111,"content":"info: Tool call completed {\"success\":true,\"timestamp\":\"2025-09-07T16:48:59.507Z\",\"tool\":\"hitrip_get_airports\"}\n"},"pid":41325}
{"timestamp":"2025-09-07T16:48:59.507Z","level":"DEBUG","message":"Sending MCP response","data":{"id":4,"hasResult":true,"hasError":false},"pid":41325}
{"timestamp":"2025-09-07T16:49:01.229Z","level":"INFO","message":"Received SIGTERM, shutting down","pid":41325}
{"timestamp":"2025-09-07T16:58:51.264Z","level":"INFO","message":"Starting Hitrip MCP Server (Debug Mode)","pid":72404}
{"timestamp":"2025-09-07T16:58:51.265Z","level":"INFO","message":"Node.js version","data":{"version":"v23.11.0"},"pid":72404}
{"timestamp":"2025-09-07T16:58:51.266Z","level":"INFO","message":"Working directory","data":{"cwd":"/Users/<USER>/WorkSpace/hitrip_new_mcp"},"pid":72404}
{"timestamp":"2025-09-07T16:58:51.266Z","level":"INFO","message":"Log file","data":{"path":"/Users/<USER>/WorkSpace/hitrip_new_mcp/logs/mcp-debug-2025-09-07.log"},"pid":72404}
{"timestamp":"2025-09-07T16:58:51.266Z","level":"INFO","message":"Validating configuration","pid":72404}
{"timestamp":"2025-09-07T16:58:51.266Z","level":"DEBUG","message":"Environment variables","data":{"NODE_ENV":"development","LOG_LEVEL":"debug","HITRIP_BASE_URL":"https://api-gw.test.huangbaoche.com/","HITRIP_CID":"***","HITRIP_SECRET_KEY":"***"},"pid":72404}
{"timestamp":"2025-09-07T16:58:51.267Z","level":"INFO","message":"Configuration validated successfully","pid":72404}
{"timestamp":"2025-09-07T16:58:51.267Z","level":"INFO","message":"Creating MCP server instance","pid":72404}
{"timestamp":"2025-09-07T16:58:51.268Z","level":"INFO","message":"Starting MCP server in STDIO mode","pid":72404}
{"timestamp":"2025-09-07T16:58:51.270Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":71,"content":"2025-09-08 00:58:51 [\u001b[32minfo\u001b[39m]: MCP Server started in STDIO mode\n"},"pid":72404}
{"timestamp":"2025-09-07T16:58:51.271Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":80,"content":"info: MCP Server started in STDIO mode {\"timestamp\":\"2025-09-07T16:58:51.269Z\"}\n"},"pid":72404}
{"timestamp":"2025-09-07T16:58:51.271Z","level":"INFO","message":"Hitrip MCP Server started successfully","pid":72404}
{"timestamp":"2025-09-07T16:58:51.998Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":1,"length":172,"message":"{\"jsonrpc\":\"2.0\",\"id\":1,\"method\":\"initialize\",\"params\":{\"protocolVersion\":\"2024-11-05\",\"capabilities\":{\"tools\":{}},\"clientInfo\":{\"name\":\"cherry-studio\",\"version\":\"1.0.0\"}}}"},"pid":72404}
{"timestamp":"2025-09-07T16:58:51.999Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":1,"method":"initialize","id":1,"hasParams":true},"pid":72404}
{"timestamp":"2025-09-07T16:58:52.006Z","level":"DEBUG","message":"Sending MCP response","data":{"id":1,"hasResult":true,"hasError":false},"pid":72404}
{"timestamp":"2025-09-07T16:58:52.995Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":2,"length":54,"message":"{\"jsonrpc\":\"2.0\",\"method\":\"notifications/initialized\"}"},"pid":72404}
{"timestamp":"2025-09-07T16:58:52.996Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":2,"method":"notifications/initialized","hasParams":false},"pid":72404}
{"timestamp":"2025-09-07T16:58:53.996Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":3,"length":46,"message":"{\"jsonrpc\":\"2.0\",\"id\":2,\"method\":\"tools/list\"}"},"pid":72404}
{"timestamp":"2025-09-07T16:58:53.996Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":3,"method":"tools/list","id":2,"hasParams":false},"pid":72404}
{"timestamp":"2025-09-07T16:58:53.998Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":76,"content":"2025-09-08 00:58:53 [\u001b[34mdebug\u001b[39m]: Listing available tools {\"count\":13}\n"},"pid":72404}
{"timestamp":"2025-09-07T16:58:53.998Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":83,"content":"debug: Listing available tools {\"count\":13,\"timestamp\":\"2025-09-07T16:58:53.997Z\"}\n"},"pid":72404}
{"timestamp":"2025-09-07T16:58:53.999Z","level":"DEBUG","message":"Sending MCP response","data":{"id":2,"hasResult":true,"hasError":false},"pid":72404}
{"timestamp":"2025-09-07T16:58:54.996Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":4,"length":98,"message":"{\"jsonrpc\":\"2.0\",\"id\":3,\"method\":\"tools/call\",\"params\":{\"name\":\"hitrip_get_token\",\"arguments\":{}}}"},"pid":72404}
{"timestamp":"2025-09-07T16:58:54.996Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":4,"method":"tools/call","id":3,"hasParams":true},"pid":72404}
{"timestamp":"2025-09-07T16:58:54.999Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":95,"content":"2025-09-08 00:58:54 [\u001b[32minfo\u001b[39m]: Tool call received {\"tool\":\"hitrip_get_token\",\"args\":[]}\n"},"pid":72404}
{"timestamp":"2025-09-07T16:58:54.999Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":102,"content":"info: Tool call received {\"args\":[],\"timestamp\":\"2025-09-07T16:58:54.998Z\",\"tool\":\"hitrip_get_token\"}\n"},"pid":72404}
{"timestamp":"2025-09-07T16:58:55.000Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":57,"content":"2025-09-08 00:58:55 [\u001b[32minfo\u001b[39m]: Fetching new token\n"},"pid":72404}
{"timestamp":"2025-09-07T16:58:55.000Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":66,"content":"info: Fetching new token {\"timestamp\":\"2025-09-07T16:58:54.999Z\"}\n"},"pid":72404}
{"timestamp":"2025-09-07T16:58:55.002Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":146,"content":"2025-09-08 00:58:55 [\u001b[34mdebug\u001b[39m]: API Request {\"method\":\"GET\",\"url\":\"/otaorder-api/access/getAccessToken?cid=1086910840&secretKey=12345678\"}\n"},"pid":72404}
{"timestamp":"2025-09-07T16:58:55.002Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":153,"content":"debug: API Request {\"method\":\"GET\",\"timestamp\":\"2025-09-07T16:58:55.001Z\",\"url\":\"/otaorder-api/access/getAccessToken?cid=1086910840&secretKey=12345678\"}\n"},"pid":72404}
{"timestamp":"2025-09-07T16:58:55.393Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":160,"content":"2025-09-08 00:58:55 [\u001b[34mdebug\u001b[39m]: API Response {\"status\":200,\"url\":\"/otaorder-api/access/getAccessToken?cid=1086910840&secretKey=12345678\",\"success\":true}\n"},"pid":72404}
{"timestamp":"2025-09-07T16:58:55.393Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":167,"content":"debug: API Response {\"status\":200,\"success\":true,\"timestamp\":\"2025-09-07T16:58:55.393Z\",\"url\":\"/otaorder-api/access/getAccessToken?cid=1086910840&secretKey=12345678\"}\n"},"pid":72404}
{"timestamp":"2025-09-07T16:58:55.393Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":66,"content":"2025-09-08 00:58:55 [\u001b[32minfo\u001b[39m]: Token obtained successfully\n"},"pid":72404}
{"timestamp":"2025-09-07T16:58:55.394Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":75,"content":"info: Token obtained successfully {\"timestamp\":\"2025-09-07T16:58:55.393Z\"}\n"},"pid":72404}
{"timestamp":"2025-09-07T16:58:55.395Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":101,"content":"2025-09-08 00:58:55 [\u001b[32minfo\u001b[39m]: Tool call completed {\"tool\":\"hitrip_get_token\",\"success\":true}\n"},"pid":72404}
{"timestamp":"2025-09-07T16:58:55.395Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":108,"content":"info: Tool call completed {\"success\":true,\"timestamp\":\"2025-09-07T16:58:55.394Z\",\"tool\":\"hitrip_get_token\"}\n"},"pid":72404}
{"timestamp":"2025-09-07T16:58:55.395Z","level":"DEBUG","message":"Sending MCP response","data":{"id":3,"hasResult":true,"hasError":false},"pid":72404}
{"timestamp":"2025-09-07T16:58:55.997Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":5,"length":120,"message":"{\"jsonrpc\":\"2.0\",\"id\":4,\"method\":\"tools/call\",\"params\":{\"name\":\"hitrip_get_airports\",\"arguments\":{\"keyword\":\"Beijing\"}}}"},"pid":72404}
{"timestamp":"2025-09-07T16:58:55.998Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":5,"method":"tools/call","id":4,"hasParams":true},"pid":72404}
{"timestamp":"2025-09-07T16:58:56.005Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":107,"content":"2025-09-08 00:58:56 [\u001b[32minfo\u001b[39m]: Tool call received {\"tool\":\"hitrip_get_airports\",\"args\":[\"keyword\"]}\n"},"pid":72404}
{"timestamp":"2025-09-07T16:58:56.006Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":114,"content":"info: Tool call received {\"args\":[\"keyword\"],\"timestamp\":\"2025-09-07T16:58:56.002Z\",\"tool\":\"hitrip_get_airports\"}\n"},"pid":72404}
{"timestamp":"2025-09-07T16:58:56.013Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":58,"content":"2025-09-08 00:58:56 [\u001b[34mdebug\u001b[39m]: Using cached token\n"},"pid":72404}
{"timestamp":"2025-09-07T16:58:56.045Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":67,"content":"debug: Using cached token {\"timestamp\":\"2025-09-07T16:58:56.007Z\"}\n"},"pid":72404}
{"timestamp":"2025-09-07T16:58:56.084Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":505,"content":"2025-09-08 00:58:56 [\u001b[34mdebug\u001b[39m]: API Request {\"method\":\"POST\",\"url\":\"/otaorder-api/common/v1.0/airport/listByNameAndSpell?token=26A2DED6136A8F6BDCD27DF8DE7B6056747FA1A0F6AE1FBE10301F28E25AB099D8"},"pid":72404}
{"timestamp":"2025-09-07T16:58:56.114Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":512,"content":"debug: API Request {\"method\":\"POST\",\"timestamp\":\"2025-09-07T16:58:56.084Z\",\"url\":\"/otaorder-api/common/v1.0/airport/listByNameAndSpell?token=26A2DED6136A8F6BDCD27DF8DE7B6056747FA1A0F6AE1FBE10301F28E25"},"pid":72404}
{"timestamp":"2025-09-07T16:58:56.276Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":518,"content":"2025-09-08 00:58:56 [\u001b[34mdebug\u001b[39m]: API Response {\"status\":200,\"url\":\"/otaorder-api/common/v1.0/airport/listByNameAndSpell?token=26A2DED6136A8F6BDCD27DF8DE7B6056747FA1A0F6AE1FBE10301F28E25AB099D88E"},"pid":72404}
{"timestamp":"2025-09-07T16:58:56.277Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":525,"content":"debug: API Response {\"status\":200,\"success\":true,\"timestamp\":\"2025-09-07T16:58:56.275Z\",\"url\":\"/otaorder-api/common/v1.0/airport/listByNameAndSpell?token=26A2DED6136A8F6BDCD27DF8DE7B6056747FA1A0F6AE1F"},"pid":72404}
{"timestamp":"2025-09-07T16:58:56.278Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":104,"content":"2025-09-08 00:58:56 [\u001b[32minfo\u001b[39m]: Tool call completed {\"tool\":\"hitrip_get_airports\",\"success\":true}\n"},"pid":72404}
{"timestamp":"2025-09-07T16:58:56.280Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":111,"content":"info: Tool call completed {\"success\":true,\"timestamp\":\"2025-09-07T16:58:56.277Z\",\"tool\":\"hitrip_get_airports\"}\n"},"pid":72404}
{"timestamp":"2025-09-07T16:58:56.281Z","level":"DEBUG","message":"Sending MCP response","data":{"id":4,"hasResult":true,"hasError":false},"pid":72404}
{"timestamp":"2025-09-07T16:58:57.997Z","level":"INFO","message":"Received SIGTERM, shutting down","pid":72404}
{"timestamp":"2025-09-07T16:59:40.704Z","level":"INFO","message":"Starting Hitrip MCP Server (Debug Mode)","pid":74989}
{"timestamp":"2025-09-07T16:59:40.708Z","level":"INFO","message":"Node.js version","data":{"version":"v23.11.0"},"pid":74989}
{"timestamp":"2025-09-07T16:59:40.708Z","level":"INFO","message":"Working directory","data":{"cwd":"/Users/<USER>/WorkSpace/hitrip_new_mcp"},"pid":74989}
{"timestamp":"2025-09-07T16:59:40.708Z","level":"INFO","message":"Log file","data":{"path":"/Users/<USER>/WorkSpace/hitrip_new_mcp/logs/mcp-debug-2025-09-07.log"},"pid":74989}
{"timestamp":"2025-09-07T16:59:40.709Z","level":"INFO","message":"Validating configuration","pid":74989}
{"timestamp":"2025-09-07T16:59:40.710Z","level":"DEBUG","message":"Environment variables","data":{"NODE_ENV":"development","LOG_LEVEL":"debug","HITRIP_BASE_URL":"https://api-gw.test.huangbaoche.com/","HITRIP_CID":"***","HITRIP_SECRET_KEY":"***"},"pid":74989}
{"timestamp":"2025-09-07T16:59:40.710Z","level":"INFO","message":"Configuration validated successfully","pid":74989}
{"timestamp":"2025-09-07T16:59:40.710Z","level":"INFO","message":"Creating MCP server instance","pid":74989}
{"timestamp":"2025-09-07T16:59:40.712Z","level":"INFO","message":"Starting MCP server in STDIO mode","pid":74989}
{"timestamp":"2025-09-07T16:59:40.717Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":71,"content":"2025-09-08 00:59:40 [\u001b[32minfo\u001b[39m]: MCP Server started in STDIO mode\n"},"pid":74989}
{"timestamp":"2025-09-07T16:59:40.719Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":80,"content":"info: MCP Server started in STDIO mode {\"timestamp\":\"2025-09-07T16:59:40.714Z\"}\n"},"pid":74989}
{"timestamp":"2025-09-07T16:59:40.720Z","level":"INFO","message":"Hitrip MCP Server started successfully","pid":74989}
{"timestamp":"2025-09-07T16:59:41.379Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":1,"length":170,"message":"{\"jsonrpc\":\"2.0\",\"id\":1,\"method\":\"initialize\",\"params\":{\"protocolVersion\":\"2024-11-05\",\"capabilities\":{\"tools\":{}},\"clientInfo\":{\"name\":\"cities-test\",\"version\":\"1.0.0\"}}}"},"pid":74989}
{"timestamp":"2025-09-07T16:59:41.380Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":1,"method":"initialize","id":1,"hasParams":true},"pid":74989}
{"timestamp":"2025-09-07T16:59:41.385Z","level":"DEBUG","message":"Sending MCP response","data":{"id":1,"hasResult":true,"hasError":false},"pid":74989}
{"timestamp":"2025-09-07T16:59:42.379Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":2,"length":54,"message":"{\"jsonrpc\":\"2.0\",\"method\":\"notifications/initialized\"}"},"pid":74989}
{"timestamp":"2025-09-07T16:59:42.379Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":2,"method":"notifications/initialized","hasParams":false},"pid":74989}
{"timestamp":"2025-09-07T16:59:43.379Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":3,"length":99,"message":"{\"jsonrpc\":\"2.0\",\"id\":2,\"method\":\"tools/call\",\"params\":{\"name\":\"hitrip_get_cities\",\"arguments\":{}}}"},"pid":74989}
{"timestamp":"2025-09-07T16:59:43.379Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":3,"method":"tools/call","id":2,"hasParams":true},"pid":74989}
{"timestamp":"2025-09-07T16:59:43.381Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":96,"content":"2025-09-08 00:59:43 [\u001b[32minfo\u001b[39m]: Tool call received {\"tool\":\"hitrip_get_cities\",\"args\":[]}\n"},"pid":74989}
{"timestamp":"2025-09-07T16:59:43.382Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":103,"content":"info: Tool call received {\"args\":[],\"timestamp\":\"2025-09-07T16:59:43.380Z\",\"tool\":\"hitrip_get_cities\"}\n"},"pid":74989}
{"timestamp":"2025-09-07T16:59:43.383Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":57,"content":"2025-09-08 00:59:43 [\u001b[32minfo\u001b[39m]: Fetching new token\n"},"pid":74989}
{"timestamp":"2025-09-07T16:59:43.383Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":66,"content":"info: Fetching new token {\"timestamp\":\"2025-09-07T16:59:43.382Z\"}\n"},"pid":74989}
{"timestamp":"2025-09-07T16:59:43.388Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":146,"content":"2025-09-08 00:59:43 [\u001b[34mdebug\u001b[39m]: API Request {\"method\":\"GET\",\"url\":\"/otaorder-api/access/getAccessToken?cid=1086910840&secretKey=12345678\"}\n"},"pid":74989}
{"timestamp":"2025-09-07T16:59:43.388Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":153,"content":"debug: API Request {\"method\":\"GET\",\"timestamp\":\"2025-09-07T16:59:43.386Z\",\"url\":\"/otaorder-api/access/getAccessToken?cid=1086910840&secretKey=12345678\"}\n"},"pid":74989}
{"timestamp":"2025-09-07T16:59:43.490Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":160,"content":"2025-09-08 00:59:43 [\u001b[34mdebug\u001b[39m]: API Response {\"status\":200,\"url\":\"/otaorder-api/access/getAccessToken?cid=1086910840&secretKey=12345678\",\"success\":true}\n"},"pid":74989}
{"timestamp":"2025-09-07T16:59:43.491Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":167,"content":"debug: API Response {\"status\":200,\"success\":true,\"timestamp\":\"2025-09-07T16:59:43.490Z\",\"url\":\"/otaorder-api/access/getAccessToken?cid=1086910840&secretKey=12345678\"}\n"},"pid":74989}
{"timestamp":"2025-09-07T16:59:43.492Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":66,"content":"2025-09-08 00:59:43 [\u001b[32minfo\u001b[39m]: Token obtained successfully\n"},"pid":74989}
{"timestamp":"2025-09-07T16:59:43.492Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":75,"content":"info: Token obtained successfully {\"timestamp\":\"2025-09-07T16:59:43.491Z\"}\n"},"pid":74989}
{"timestamp":"2025-09-07T16:59:43.493Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":504,"content":"2025-09-08 00:59:43 [\u001b[34mdebug\u001b[39m]: API Request {\"method\":\"POST\",\"url\":\"/otaorder-api/common/v1.0/cities/listByNameAndSpell?token=26A2DED6136A8F6BDCD27DF8DE7B6056747FA1A0F6AE1FBE10301F28E25AB099D88"},"pid":74989}
{"timestamp":"2025-09-07T16:59:43.493Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":511,"content":"debug: API Request {\"method\":\"POST\",\"timestamp\":\"2025-09-07T16:59:43.493Z\",\"url\":\"/otaorder-api/common/v1.0/cities/listByNameAndSpell?token=26A2DED6136A8F6BDCD27DF8DE7B6056747FA1A0F6AE1FBE10301F28E25A"},"pid":74989}
{"timestamp":"2025-09-07T16:59:44.380Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":4,"length":118,"message":"{\"jsonrpc\":\"2.0\",\"id\":3,\"method\":\"tools/call\",\"params\":{\"name\":\"hitrip_get_cities\",\"arguments\":{\"keyword\":\"Beijing\"}}}"},"pid":74989}
{"timestamp":"2025-09-07T16:59:44.380Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":4,"method":"tools/call","id":3,"hasParams":true},"pid":74989}
{"timestamp":"2025-09-07T16:59:44.381Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":105,"content":"2025-09-08 00:59:44 [\u001b[32minfo\u001b[39m]: Tool call received {\"tool\":\"hitrip_get_cities\",\"args\":[\"keyword\"]}\n"},"pid":74989}
{"timestamp":"2025-09-07T16:59:44.381Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":112,"content":"info: Tool call received {\"args\":[\"keyword\"],\"timestamp\":\"2025-09-07T16:59:44.380Z\",\"tool\":\"hitrip_get_cities\"}\n"},"pid":74989}
{"timestamp":"2025-09-07T16:59:44.382Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":58,"content":"2025-09-08 00:59:44 [\u001b[34mdebug\u001b[39m]: Using cached token\n"},"pid":74989}
{"timestamp":"2025-09-07T16:59:44.382Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":67,"content":"debug: Using cached token {\"timestamp\":\"2025-09-07T16:59:44.381Z\"}\n"},"pid":74989}
{"timestamp":"2025-09-07T16:59:44.383Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":504,"content":"2025-09-08 00:59:44 [\u001b[34mdebug\u001b[39m]: API Request {\"method\":\"POST\",\"url\":\"/otaorder-api/common/v1.0/cities/listByNameAndSpell?token=26A2DED6136A8F6BDCD27DF8DE7B6056747FA1A0F6AE1FBE10301F28E25AB099D88"},"pid":74989}
{"timestamp":"2025-09-07T16:59:44.383Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":511,"content":"debug: API Request {\"method\":\"POST\",\"timestamp\":\"2025-09-07T16:59:44.383Z\",\"url\":\"/otaorder-api/common/v1.0/cities/listByNameAndSpell?token=26A2DED6136A8F6BDCD27DF8DE7B6056747FA1A0F6AE1FBE10301F28E25A"},"pid":74989}
{"timestamp":"2025-09-07T16:59:44.512Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":517,"content":"2025-09-08 00:59:44 [\u001b[34mdebug\u001b[39m]: API Response {\"status\":200,\"url\":\"/otaorder-api/common/v1.0/cities/listByNameAndSpell?token=26A2DED6136A8F6BDCD27DF8DE7B6056747FA1A0F6AE1FBE10301F28E25AB099D88EF"},"pid":74989}
{"timestamp":"2025-09-07T16:59:44.512Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":524,"content":"debug: API Response {\"status\":200,\"success\":true,\"timestamp\":\"2025-09-07T16:59:44.512Z\",\"url\":\"/otaorder-api/common/v1.0/cities/listByNameAndSpell?token=26A2DED6136A8F6BDCD27DF8DE7B6056747FA1A0F6AE1FB"},"pid":74989}
{"timestamp":"2025-09-07T16:59:44.513Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":102,"content":"2025-09-08 00:59:44 [\u001b[32minfo\u001b[39m]: Tool call completed {\"tool\":\"hitrip_get_cities\",\"success\":true}\n"},"pid":74989}
{"timestamp":"2025-09-07T16:59:44.513Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":109,"content":"info: Tool call completed {\"success\":true,\"timestamp\":\"2025-09-07T16:59:44.513Z\",\"tool\":\"hitrip_get_cities\"}\n"},"pid":74989}
{"timestamp":"2025-09-07T16:59:44.533Z","level":"DEBUG","message":"Sending MCP response","data":{"id":2,"hasResult":true,"hasError":false},"pid":74989}
{"timestamp":"2025-09-07T16:59:44.539Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":517,"content":"2025-09-08 00:59:44 [\u001b[34mdebug\u001b[39m]: API Response {\"status\":200,\"url\":\"/otaorder-api/common/v1.0/cities/listByNameAndSpell?token=26A2DED6136A8F6BDCD27DF8DE7B6056747FA1A0F6AE1FBE10301F28E25AB099D88EF"},"pid":74989}
{"timestamp":"2025-09-07T16:59:44.539Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":524,"content":"debug: API Response {\"status\":200,\"success\":true,\"timestamp\":\"2025-09-07T16:59:44.539Z\",\"url\":\"/otaorder-api/common/v1.0/cities/listByNameAndSpell?token=26A2DED6136A8F6BDCD27DF8DE7B6056747FA1A0F6AE1FB"},"pid":74989}
{"timestamp":"2025-09-07T16:59:44.539Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":102,"content":"2025-09-08 00:59:44 [\u001b[32minfo\u001b[39m]: Tool call completed {\"tool\":\"hitrip_get_cities\",\"success\":true}\n"},"pid":74989}
{"timestamp":"2025-09-07T16:59:44.539Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":109,"content":"info: Tool call completed {\"success\":true,\"timestamp\":\"2025-09-07T16:59:44.539Z\",\"tool\":\"hitrip_get_cities\"}\n"},"pid":74989}
{"timestamp":"2025-09-07T16:59:44.540Z","level":"DEBUG","message":"Sending MCP response","data":{"id":3,"hasResult":true,"hasError":false},"pid":74989}
{"timestamp":"2025-09-07T16:59:45.380Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":5,"length":113,"message":"{\"jsonrpc\":\"2.0\",\"id\":4,\"method\":\"tools/call\",\"params\":{\"name\":\"hitrip_get_cities\",\"arguments\":{\"keyword\":\"北京\"}}}"},"pid":74989}
{"timestamp":"2025-09-07T16:59:45.380Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":5,"method":"tools/call","id":4,"hasParams":true},"pid":74989}
{"timestamp":"2025-09-07T16:59:45.381Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":105,"content":"2025-09-08 00:59:45 [\u001b[32minfo\u001b[39m]: Tool call received {\"tool\":\"hitrip_get_cities\",\"args\":[\"keyword\"]}\n"},"pid":74989}
{"timestamp":"2025-09-07T16:59:45.381Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":112,"content":"info: Tool call received {\"args\":[\"keyword\"],\"timestamp\":\"2025-09-07T16:59:45.381Z\",\"tool\":\"hitrip_get_cities\"}\n"},"pid":74989}
{"timestamp":"2025-09-07T16:59:45.381Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":58,"content":"2025-09-08 00:59:45 [\u001b[34mdebug\u001b[39m]: Using cached token\n"},"pid":74989}
{"timestamp":"2025-09-07T16:59:45.381Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":67,"content":"debug: Using cached token {\"timestamp\":\"2025-09-07T16:59:45.381Z\"}\n"},"pid":74989}
{"timestamp":"2025-09-07T16:59:45.382Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":504,"content":"2025-09-08 00:59:45 [\u001b[34mdebug\u001b[39m]: API Request {\"method\":\"POST\",\"url\":\"/otaorder-api/common/v1.0/cities/listByNameAndSpell?token=26A2DED6136A8F6BDCD27DF8DE7B6056747FA1A0F6AE1FBE10301F28E25AB099D88"},"pid":74989}
{"timestamp":"2025-09-07T16:59:45.382Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":511,"content":"debug: API Request {\"method\":\"POST\",\"timestamp\":\"2025-09-07T16:59:45.382Z\",\"url\":\"/otaorder-api/common/v1.0/cities/listByNameAndSpell?token=26A2DED6136A8F6BDCD27DF8DE7B6056747FA1A0F6AE1FBE10301F28E25A"},"pid":74989}
{"timestamp":"2025-09-07T16:59:45.531Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":517,"content":"2025-09-08 00:59:45 [\u001b[34mdebug\u001b[39m]: API Response {\"status\":200,\"url\":\"/otaorder-api/common/v1.0/cities/listByNameAndSpell?token=26A2DED6136A8F6BDCD27DF8DE7B6056747FA1A0F6AE1FBE10301F28E25AB099D88EF"},"pid":74989}
{"timestamp":"2025-09-07T16:59:45.532Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":524,"content":"debug: API Response {\"status\":200,\"success\":true,\"timestamp\":\"2025-09-07T16:59:45.531Z\",\"url\":\"/otaorder-api/common/v1.0/cities/listByNameAndSpell?token=26A2DED6136A8F6BDCD27DF8DE7B6056747FA1A0F6AE1FB"},"pid":74989}
{"timestamp":"2025-09-07T16:59:45.532Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":102,"content":"2025-09-08 00:59:45 [\u001b[32minfo\u001b[39m]: Tool call completed {\"tool\":\"hitrip_get_cities\",\"success\":true}\n"},"pid":74989}
{"timestamp":"2025-09-07T16:59:45.532Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":109,"content":"info: Tool call completed {\"success\":true,\"timestamp\":\"2025-09-07T16:59:45.532Z\",\"tool\":\"hitrip_get_cities\"}\n"},"pid":74989}
{"timestamp":"2025-09-07T16:59:45.532Z","level":"DEBUG","message":"Sending MCP response","data":{"id":4,"hasResult":true,"hasError":false},"pid":74989}
{"timestamp":"2025-09-07T16:59:47.380Z","level":"INFO","message":"Received SIGTERM, shutting down","pid":74989}
{"timestamp":"2025-09-07T17:09:19.341Z","level":"INFO","message":"Starting Hitrip MCP Server (Debug Mode)","pid":6189}
{"timestamp":"2025-09-07T17:09:19.342Z","level":"INFO","message":"Node.js version","data":{"version":"v23.11.0"},"pid":6189}
{"timestamp":"2025-09-07T17:09:19.342Z","level":"INFO","message":"Working directory","data":{"cwd":"/Users/<USER>/WorkSpace/hitrip_new_mcp"},"pid":6189}
{"timestamp":"2025-09-07T17:09:19.342Z","level":"INFO","message":"Log file","data":{"path":"/Users/<USER>/WorkSpace/hitrip_new_mcp/logs/mcp-debug-2025-09-07.log"},"pid":6189}
{"timestamp":"2025-09-07T17:09:19.343Z","level":"INFO","message":"Validating configuration","pid":6189}
{"timestamp":"2025-09-07T17:09:19.343Z","level":"DEBUG","message":"Environment variables","data":{"NODE_ENV":"development","LOG_LEVEL":"debug","HITRIP_BASE_URL":"https://api-gw.test.huangbaoche.com/","HITRIP_CID":"***","HITRIP_SECRET_KEY":"***"},"pid":6189}
{"timestamp":"2025-09-07T17:09:19.343Z","level":"INFO","message":"Configuration validated successfully","pid":6189}
{"timestamp":"2025-09-07T17:09:19.343Z","level":"INFO","message":"Creating MCP server instance","pid":6189}
{"timestamp":"2025-09-07T17:09:19.345Z","level":"INFO","message":"Starting MCP server in STDIO mode","pid":6189}
{"timestamp":"2025-09-07T17:09:19.348Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":71,"content":"2025-09-08 01:09:19 [\u001b[32minfo\u001b[39m]: MCP Server started in STDIO mode\n"},"pid":6189}
{"timestamp":"2025-09-07T17:09:19.350Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":80,"content":"info: MCP Server started in STDIO mode {\"timestamp\":\"2025-09-07T17:09:19.346Z\"}\n"},"pid":6189}
{"timestamp":"2025-09-07T17:09:19.350Z","level":"INFO","message":"Hitrip MCP Server started successfully","pid":6189}
{"timestamp":"2025-09-07T17:09:20.062Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":1,"length":172,"message":"{\"jsonrpc\":\"2.0\",\"id\":1,\"method\":\"initialize\",\"params\":{\"protocolVersion\":\"2024-11-05\",\"capabilities\":{\"tools\":{}},\"clientInfo\":{\"name\":\"airports-test\",\"version\":\"1.0.0\"}}}"},"pid":6189}
{"timestamp":"2025-09-07T17:09:20.064Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":1,"method":"initialize","id":1,"hasParams":true},"pid":6189}
{"timestamp":"2025-09-07T17:09:20.072Z","level":"DEBUG","message":"Sending MCP response","data":{"id":1,"hasResult":true,"hasError":false},"pid":6189}
{"timestamp":"2025-09-07T17:09:21.060Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":2,"length":54,"message":"{\"jsonrpc\":\"2.0\",\"method\":\"notifications/initialized\"}"},"pid":6189}
{"timestamp":"2025-09-07T17:09:21.060Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":2,"method":"notifications/initialized","hasParams":false},"pid":6189}
{"timestamp":"2025-09-07T17:09:22.060Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":3,"length":101,"message":"{\"jsonrpc\":\"2.0\",\"id\":2,\"method\":\"tools/call\",\"params\":{\"name\":\"hitrip_get_airports\",\"arguments\":{}}}"},"pid":6189}
{"timestamp":"2025-09-07T17:09:22.060Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":3,"method":"tools/call","id":2,"hasParams":true},"pid":6189}
{"timestamp":"2025-09-07T17:09:22.066Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":98,"content":"2025-09-08 01:09:22 [\u001b[32minfo\u001b[39m]: Tool call received {\"tool\":\"hitrip_get_airports\",\"args\":[]}\n"},"pid":6189}
{"timestamp":"2025-09-07T17:09:22.069Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":105,"content":"info: Tool call received {\"args\":[],\"timestamp\":\"2025-09-07T17:09:22.064Z\",\"tool\":\"hitrip_get_airports\"}\n"},"pid":6189}
{"timestamp":"2025-09-07T17:09:22.077Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":57,"content":"2025-09-08 01:09:22 [\u001b[32minfo\u001b[39m]: Fetching new token\n"},"pid":6189}
{"timestamp":"2025-09-07T17:09:22.078Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":66,"content":"info: Fetching new token {\"timestamp\":\"2025-09-07T17:09:22.077Z\"}\n"},"pid":6189}
{"timestamp":"2025-09-07T17:09:22.084Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":146,"content":"2025-09-08 01:09:22 [\u001b[34mdebug\u001b[39m]: API Request {\"method\":\"GET\",\"url\":\"/otaorder-api/access/getAccessToken?cid=1086910840&secretKey=12345678\"}\n"},"pid":6189}
{"timestamp":"2025-09-07T17:09:22.085Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":153,"content":"debug: API Request {\"method\":\"GET\",\"timestamp\":\"2025-09-07T17:09:22.083Z\",\"url\":\"/otaorder-api/access/getAccessToken?cid=1086910840&secretKey=12345678\"}\n"},"pid":6189}
{"timestamp":"2025-09-07T17:09:22.235Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":160,"content":"2025-09-08 01:09:22 [\u001b[34mdebug\u001b[39m]: API Response {\"status\":200,\"url\":\"/otaorder-api/access/getAccessToken?cid=1086910840&secretKey=12345678\",\"success\":true}\n"},"pid":6189}
{"timestamp":"2025-09-07T17:09:22.236Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":167,"content":"debug: API Response {\"status\":200,\"success\":true,\"timestamp\":\"2025-09-07T17:09:22.235Z\",\"url\":\"/otaorder-api/access/getAccessToken?cid=1086910840&secretKey=12345678\"}\n"},"pid":6189}
{"timestamp":"2025-09-07T17:09:22.237Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":66,"content":"2025-09-08 01:09:22 [\u001b[32minfo\u001b[39m]: Token obtained successfully\n"},"pid":6189}
{"timestamp":"2025-09-07T17:09:22.237Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":75,"content":"info: Token obtained successfully {\"timestamp\":\"2025-09-07T17:09:22.236Z\"}\n"},"pid":6189}
{"timestamp":"2025-09-07T17:09:22.237Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":505,"content":"2025-09-08 01:09:22 [\u001b[34mdebug\u001b[39m]: API Request {\"method\":\"POST\",\"url\":\"/otaorder-api/common/v1.0/airport/listByNameAndSpell?token=26A2DED6136A8F6BDCD27DF8DE7B6056747FA1A0F6AE1FBE10301F28E25AB099D8"},"pid":6189}
{"timestamp":"2025-09-07T17:09:22.237Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":512,"content":"debug: API Request {\"method\":\"POST\",\"timestamp\":\"2025-09-07T17:09:22.237Z\",\"url\":\"/otaorder-api/common/v1.0/airport/listByNameAndSpell?token=26A2DED6136A8F6BDCD27DF8DE7B6056747FA1A0F6AE1FBE10301F28E25"},"pid":6189}
{"timestamp":"2025-09-07T17:09:22.400Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":518,"content":"2025-09-08 01:09:22 [\u001b[34mdebug\u001b[39m]: API Response {\"status\":200,\"url\":\"/otaorder-api/common/v1.0/airport/listByNameAndSpell?token=26A2DED6136A8F6BDCD27DF8DE7B6056747FA1A0F6AE1FBE10301F28E25AB099D88E"},"pid":6189}
{"timestamp":"2025-09-07T17:09:22.400Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":525,"content":"debug: API Response {\"status\":200,\"success\":true,\"timestamp\":\"2025-09-07T17:09:22.400Z\",\"url\":\"/otaorder-api/common/v1.0/airport/listByNameAndSpell?token=26A2DED6136A8F6BDCD27DF8DE7B6056747FA1A0F6AE1F"},"pid":6189}
{"timestamp":"2025-09-07T17:09:22.401Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":104,"content":"2025-09-08 01:09:22 [\u001b[32minfo\u001b[39m]: Tool call completed {\"tool\":\"hitrip_get_airports\",\"success\":true}\n"},"pid":6189}
{"timestamp":"2025-09-07T17:09:22.401Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":111,"content":"info: Tool call completed {\"success\":true,\"timestamp\":\"2025-09-07T17:09:22.400Z\",\"tool\":\"hitrip_get_airports\"}\n"},"pid":6189}
{"timestamp":"2025-09-07T17:09:22.401Z","level":"DEBUG","message":"Sending MCP response","data":{"id":2,"hasResult":true,"hasError":false},"pid":6189}
{"timestamp":"2025-09-07T17:09:23.060Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":4,"length":115,"message":"{\"jsonrpc\":\"2.0\",\"id\":3,\"method\":\"tools/call\",\"params\":{\"name\":\"hitrip_get_airports\",\"arguments\":{\"keyword\":\"北京\"}}}"},"pid":6189}
{"timestamp":"2025-09-07T17:09:23.060Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":4,"method":"tools/call","id":3,"hasParams":true},"pid":6189}
{"timestamp":"2025-09-07T17:09:23.064Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":107,"content":"2025-09-08 01:09:23 [\u001b[32minfo\u001b[39m]: Tool call received {\"tool\":\"hitrip_get_airports\",\"args\":[\"keyword\"]}\n"},"pid":6189}
{"timestamp":"2025-09-07T17:09:23.064Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":114,"content":"info: Tool call received {\"args\":[\"keyword\"],\"timestamp\":\"2025-09-07T17:09:23.062Z\",\"tool\":\"hitrip_get_airports\"}\n"},"pid":6189}
{"timestamp":"2025-09-07T17:09:23.066Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":58,"content":"2025-09-08 01:09:23 [\u001b[34mdebug\u001b[39m]: Using cached token\n"},"pid":6189}
{"timestamp":"2025-09-07T17:09:23.066Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":67,"content":"debug: Using cached token {\"timestamp\":\"2025-09-07T17:09:23.065Z\"}\n"},"pid":6189}
{"timestamp":"2025-09-07T17:09:23.067Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":505,"content":"2025-09-08 01:09:23 [\u001b[34mdebug\u001b[39m]: API Request {\"method\":\"POST\",\"url\":\"/otaorder-api/common/v1.0/airport/listByNameAndSpell?token=26A2DED6136A8F6BDCD27DF8DE7B6056747FA1A0F6AE1FBE10301F28E25AB099D8"},"pid":6189}
{"timestamp":"2025-09-07T17:09:23.068Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":512,"content":"debug: API Request {\"method\":\"POST\",\"timestamp\":\"2025-09-07T17:09:23.067Z\",\"url\":\"/otaorder-api/common/v1.0/airport/listByNameAndSpell?token=26A2DED6136A8F6BDCD27DF8DE7B6056747FA1A0F6AE1FBE10301F28E25"},"pid":6189}
{"timestamp":"2025-09-07T17:09:23.155Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":518,"content":"2025-09-08 01:09:23 [\u001b[34mdebug\u001b[39m]: API Response {\"status\":200,\"url\":\"/otaorder-api/common/v1.0/airport/listByNameAndSpell?token=26A2DED6136A8F6BDCD27DF8DE7B6056747FA1A0F6AE1FBE10301F28E25AB099D88E"},"pid":6189}
{"timestamp":"2025-09-07T17:09:23.156Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":525,"content":"debug: API Response {\"status\":200,\"success\":true,\"timestamp\":\"2025-09-07T17:09:23.155Z\",\"url\":\"/otaorder-api/common/v1.0/airport/listByNameAndSpell?token=26A2DED6136A8F6BDCD27DF8DE7B6056747FA1A0F6AE1F"},"pid":6189}
{"timestamp":"2025-09-07T17:09:23.156Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":104,"content":"2025-09-08 01:09:23 [\u001b[32minfo\u001b[39m]: Tool call completed {\"tool\":\"hitrip_get_airports\",\"success\":true}\n"},"pid":6189}
{"timestamp":"2025-09-07T17:09:23.156Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":111,"content":"info: Tool call completed {\"success\":true,\"timestamp\":\"2025-09-07T17:09:23.156Z\",\"tool\":\"hitrip_get_airports\"}\n"},"pid":6189}
{"timestamp":"2025-09-07T17:09:23.156Z","level":"DEBUG","message":"Sending MCP response","data":{"id":3,"hasResult":true,"hasError":false},"pid":6189}
{"timestamp":"2025-09-07T17:09:24.060Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":5,"length":120,"message":"{\"jsonrpc\":\"2.0\",\"id\":4,\"method\":\"tools/call\",\"params\":{\"name\":\"hitrip_get_airports\",\"arguments\":{\"keyword\":\"Beijing\"}}}"},"pid":6189}
{"timestamp":"2025-09-07T17:09:24.061Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":5,"method":"tools/call","id":4,"hasParams":true},"pid":6189}
{"timestamp":"2025-09-07T17:09:24.062Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":107,"content":"2025-09-08 01:09:24 [\u001b[32minfo\u001b[39m]: Tool call received {\"tool\":\"hitrip_get_airports\",\"args\":[\"keyword\"]}\n"},"pid":6189}
{"timestamp":"2025-09-07T17:09:24.063Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":114,"content":"info: Tool call received {\"args\":[\"keyword\"],\"timestamp\":\"2025-09-07T17:09:24.062Z\",\"tool\":\"hitrip_get_airports\"}\n"},"pid":6189}
{"timestamp":"2025-09-07T17:09:24.063Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":58,"content":"2025-09-08 01:09:24 [\u001b[34mdebug\u001b[39m]: Using cached token\n"},"pid":6189}
{"timestamp":"2025-09-07T17:09:24.063Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":67,"content":"debug: Using cached token {\"timestamp\":\"2025-09-07T17:09:24.063Z\"}\n"},"pid":6189}
{"timestamp":"2025-09-07T17:09:24.063Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":505,"content":"2025-09-08 01:09:24 [\u001b[34mdebug\u001b[39m]: API Request {\"method\":\"POST\",\"url\":\"/otaorder-api/common/v1.0/airport/listByNameAndSpell?token=26A2DED6136A8F6BDCD27DF8DE7B6056747FA1A0F6AE1FBE10301F28E25AB099D8"},"pid":6189}
{"timestamp":"2025-09-07T17:09:24.063Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":512,"content":"debug: API Request {\"method\":\"POST\",\"timestamp\":\"2025-09-07T17:09:24.063Z\",\"url\":\"/otaorder-api/common/v1.0/airport/listByNameAndSpell?token=26A2DED6136A8F6BDCD27DF8DE7B6056747FA1A0F6AE1FBE10301F28E25"},"pid":6189}
{"timestamp":"2025-09-07T17:09:24.139Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":518,"content":"2025-09-08 01:09:24 [\u001b[34mdebug\u001b[39m]: API Response {\"status\":200,\"url\":\"/otaorder-api/common/v1.0/airport/listByNameAndSpell?token=26A2DED6136A8F6BDCD27DF8DE7B6056747FA1A0F6AE1FBE10301F28E25AB099D88E"},"pid":6189}
{"timestamp":"2025-09-07T17:09:24.139Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":525,"content":"debug: API Response {\"status\":200,\"success\":true,\"timestamp\":\"2025-09-07T17:09:24.139Z\",\"url\":\"/otaorder-api/common/v1.0/airport/listByNameAndSpell?token=26A2DED6136A8F6BDCD27DF8DE7B6056747FA1A0F6AE1F"},"pid":6189}
{"timestamp":"2025-09-07T17:09:24.139Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":104,"content":"2025-09-08 01:09:24 [\u001b[32minfo\u001b[39m]: Tool call completed {\"tool\":\"hitrip_get_airports\",\"success\":true}\n"},"pid":6189}
{"timestamp":"2025-09-07T17:09:24.139Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":111,"content":"info: Tool call completed {\"success\":true,\"timestamp\":\"2025-09-07T17:09:24.139Z\",\"tool\":\"hitrip_get_airports\"}\n"},"pid":6189}
{"timestamp":"2025-09-07T17:09:24.139Z","level":"DEBUG","message":"Sending MCP response","data":{"id":4,"hasResult":true,"hasError":false},"pid":6189}
{"timestamp":"2025-09-07T17:09:25.060Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":6,"length":115,"message":"{\"jsonrpc\":\"2.0\",\"id\":5,\"method\":\"tools/call\",\"params\":{\"name\":\"hitrip_get_airports\",\"arguments\":{\"keyword\":\"首都\"}}}"},"pid":6189}
{"timestamp":"2025-09-07T17:09:25.060Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":6,"method":"tools/call","id":5,"hasParams":true},"pid":6189}
{"timestamp":"2025-09-07T17:09:25.060Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":107,"content":"2025-09-08 01:09:25 [\u001b[32minfo\u001b[39m]: Tool call received {\"tool\":\"hitrip_get_airports\",\"args\":[\"keyword\"]}\n"},"pid":6189}
{"timestamp":"2025-09-07T17:09:25.060Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":114,"content":"info: Tool call received {\"args\":[\"keyword\"],\"timestamp\":\"2025-09-07T17:09:25.060Z\",\"tool\":\"hitrip_get_airports\"}\n"},"pid":6189}
{"timestamp":"2025-09-07T17:09:25.061Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":58,"content":"2025-09-08 01:09:25 [\u001b[34mdebug\u001b[39m]: Using cached token\n"},"pid":6189}
{"timestamp":"2025-09-07T17:09:25.061Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":67,"content":"debug: Using cached token {\"timestamp\":\"2025-09-07T17:09:25.060Z\"}\n"},"pid":6189}
{"timestamp":"2025-09-07T17:09:25.061Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":505,"content":"2025-09-08 01:09:25 [\u001b[34mdebug\u001b[39m]: API Request {\"method\":\"POST\",\"url\":\"/otaorder-api/common/v1.0/airport/listByNameAndSpell?token=26A2DED6136A8F6BDCD27DF8DE7B6056747FA1A0F6AE1FBE10301F28E25AB099D8"},"pid":6189}
{"timestamp":"2025-09-07T17:09:25.061Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":512,"content":"debug: API Request {\"method\":\"POST\",\"timestamp\":\"2025-09-07T17:09:25.061Z\",\"url\":\"/otaorder-api/common/v1.0/airport/listByNameAndSpell?token=26A2DED6136A8F6BDCD27DF8DE7B6056747FA1A0F6AE1FBE10301F28E25"},"pid":6189}
{"timestamp":"2025-09-07T17:09:25.157Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":518,"content":"2025-09-08 01:09:25 [\u001b[34mdebug\u001b[39m]: API Response {\"status\":200,\"url\":\"/otaorder-api/common/v1.0/airport/listByNameAndSpell?token=26A2DED6136A8F6BDCD27DF8DE7B6056747FA1A0F6AE1FBE10301F28E25AB099D88E"},"pid":6189}
{"timestamp":"2025-09-07T17:09:25.157Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":525,"content":"debug: API Response {\"status\":200,\"success\":true,\"timestamp\":\"2025-09-07T17:09:25.157Z\",\"url\":\"/otaorder-api/common/v1.0/airport/listByNameAndSpell?token=26A2DED6136A8F6BDCD27DF8DE7B6056747FA1A0F6AE1F"},"pid":6189}
{"timestamp":"2025-09-07T17:09:25.157Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":104,"content":"2025-09-08 01:09:25 [\u001b[32minfo\u001b[39m]: Tool call completed {\"tool\":\"hitrip_get_airports\",\"success\":true}\n"},"pid":6189}
{"timestamp":"2025-09-07T17:09:25.157Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":111,"content":"info: Tool call completed {\"success\":true,\"timestamp\":\"2025-09-07T17:09:25.157Z\",\"tool\":\"hitrip_get_airports\"}\n"},"pid":6189}
{"timestamp":"2025-09-07T17:09:25.158Z","level":"DEBUG","message":"Sending MCP response","data":{"id":5,"hasResult":true,"hasError":false},"pid":6189}
{"timestamp":"2025-09-07T17:09:27.060Z","level":"INFO","message":"Received SIGTERM, shutting down","pid":6189}
