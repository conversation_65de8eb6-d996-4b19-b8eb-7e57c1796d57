{"timestamp":"2025-09-07T16:26:40.188Z","level":"INFO","message":"Starting Hitrip MCP Server (Debug Mode)","pid":70794}
{"timestamp":"2025-09-07T16:26:40.190Z","level":"INFO","message":"Node.js version","data":{"version":"v23.11.0"},"pid":70794}
{"timestamp":"2025-09-07T16:26:40.190Z","level":"INFO","message":"Working directory","data":{"cwd":"/Users/<USER>/WorkSpace/hitrip_new_mcp"},"pid":70794}
{"timestamp":"2025-09-07T16:26:40.190Z","level":"INFO","message":"Log file","data":{"path":"/Users/<USER>/WorkSpace/hitrip_new_mcp/logs/mcp-debug-2025-09-07.log"},"pid":70794}
{"timestamp":"2025-09-07T16:26:40.190Z","level":"INFO","message":"Validating configuration","pid":70794}
{"timestamp":"2025-09-07T16:26:40.191Z","level":"DEBUG","message":"Environment variables","data":{"HITRIP_BASE_URL":"https://api-gw.test.huangbaoche.com/","HITRIP_CID":"***","HITRIP_SECRET_KEY":"***"},"pid":70794}
{"timestamp":"2025-09-07T16:26:40.191Z","level":"INFO","message":"Configuration validated successfully","pid":70794}
{"timestamp":"2025-09-07T16:26:40.191Z","level":"INFO","message":"Creating MCP server instance","pid":70794}
{"timestamp":"2025-09-07T16:26:40.192Z","level":"INFO","message":"Starting MCP server in STDIO mode","pid":70794}
{"timestamp":"2025-09-07T16:26:40.195Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":71,"content":"2025-09-08 00:26:40 [\u001b[32minfo\u001b[39m]: MCP Server started in STDIO mode\n"},"pid":70794}
{"timestamp":"2025-09-07T16:26:40.196Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":80,"content":"info: MCP Server started in STDIO mode {\"timestamp\":\"2025-09-07T16:26:40.193Z\"}\n"},"pid":70794}
{"timestamp":"2025-09-07T16:26:40.197Z","level":"INFO","message":"Hitrip MCP Server started successfully","pid":70794}
{"timestamp":"2025-09-07T16:28:45.287Z","level":"INFO","message":"Starting Hitrip MCP Server (Debug Mode)","pid":77311}
{"timestamp":"2025-09-07T16:28:45.288Z","level":"INFO","message":"Node.js version","data":{"version":"v23.11.0"},"pid":77311}
{"timestamp":"2025-09-07T16:28:45.288Z","level":"INFO","message":"Working directory","data":{"cwd":"/Users/<USER>/WorkSpace/hitrip_new_mcp"},"pid":77311}
{"timestamp":"2025-09-07T16:28:45.288Z","level":"INFO","message":"Log file","data":{"path":"/Users/<USER>/WorkSpace/hitrip_new_mcp/logs/mcp-debug-2025-09-07.log"},"pid":77311}
{"timestamp":"2025-09-07T16:28:45.289Z","level":"INFO","message":"Validating configuration","pid":77311}
{"timestamp":"2025-09-07T16:28:45.289Z","level":"DEBUG","message":"Environment variables","data":{"NODE_ENV":"development","LOG_LEVEL":"debug","HITRIP_BASE_URL":"https://api-gw.test.huangbaoche.com/","HITRIP_CID":"***","HITRIP_SECRET_KEY":"***"},"pid":77311}
{"timestamp":"2025-09-07T16:28:45.289Z","level":"INFO","message":"Configuration validated successfully","pid":77311}
{"timestamp":"2025-09-07T16:28:45.289Z","level":"INFO","message":"Creating MCP server instance","pid":77311}
{"timestamp":"2025-09-07T16:28:45.292Z","level":"INFO","message":"Starting MCP server in STDIO mode","pid":77311}
{"timestamp":"2025-09-07T16:28:45.295Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":71,"content":"2025-09-08 00:28:45 [\u001b[32minfo\u001b[39m]: MCP Server started in STDIO mode\n"},"pid":77311}
{"timestamp":"2025-09-07T16:28:45.296Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":80,"content":"info: MCP Server started in STDIO mode {\"timestamp\":\"2025-09-07T16:28:45.293Z\"}\n"},"pid":77311}
{"timestamp":"2025-09-07T16:28:45.297Z","level":"INFO","message":"Hitrip MCP Server started successfully","pid":77311}
{"timestamp":"2025-09-07T16:28:46.986Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":1,"length":170,"message":"{\"jsonrpc\":\"2.0\",\"id\":1,\"method\":\"initialize\",\"params\":{\"protocolVersion\":\"2024-11-05\",\"capabilities\":{\"tools\":{}},\"clientInfo\":{\"name\":\"test-client\",\"version\":\"1.0.0\"}}}"},"pid":77311}
{"timestamp":"2025-09-07T16:28:46.986Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":1,"method":"initialize","id":1,"hasParams":true},"pid":77311}
{"timestamp":"2025-09-07T16:28:46.989Z","level":"DEBUG","message":"Sending MCP response","data":{"id":1,"hasResult":true,"hasError":false},"pid":77311}
{"timestamp":"2025-09-07T16:28:47.986Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":2,"length":54,"message":"{\"jsonrpc\":\"2.0\",\"method\":\"notifications/initialized\"}"},"pid":77311}
{"timestamp":"2025-09-07T16:28:47.986Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":2,"method":"notifications/initialized","hasParams":false},"pid":77311}
{"timestamp":"2025-09-07T16:28:48.986Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":3,"length":46,"message":"{\"jsonrpc\":\"2.0\",\"id\":2,\"method\":\"tools/list\"}"},"pid":77311}
{"timestamp":"2025-09-07T16:28:48.986Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":3,"method":"tools/list","id":2,"hasParams":false},"pid":77311}
{"timestamp":"2025-09-07T16:28:48.987Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":76,"content":"2025-09-08 00:28:48 [\u001b[34mdebug\u001b[39m]: Listing available tools {\"count\":13}\n"},"pid":77311}
{"timestamp":"2025-09-07T16:28:48.988Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":83,"content":"debug: Listing available tools {\"count\":13,\"timestamp\":\"2025-09-07T16:28:48.986Z\"}\n"},"pid":77311}
{"timestamp":"2025-09-07T16:28:48.988Z","level":"DEBUG","message":"Sending MCP response","data":{"id":2,"hasResult":true,"hasError":false},"pid":77311}
{"timestamp":"2025-09-07T16:28:49.986Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":4,"length":98,"message":"{\"jsonrpc\":\"2.0\",\"id\":3,\"method\":\"tools/call\",\"params\":{\"name\":\"hitrip_get_token\",\"arguments\":{}}}"},"pid":77311}
{"timestamp":"2025-09-07T16:28:49.986Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":4,"method":"tools/call","id":3,"hasParams":true},"pid":77311}
{"timestamp":"2025-09-07T16:28:49.989Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":95,"content":"2025-09-08 00:28:49 [\u001b[32minfo\u001b[39m]: Tool call received {\"tool\":\"hitrip_get_token\",\"args\":[]}\n"},"pid":77311}
{"timestamp":"2025-09-07T16:28:49.989Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":102,"content":"info: Tool call received {\"args\":[],\"timestamp\":\"2025-09-07T16:28:49.989Z\",\"tool\":\"hitrip_get_token\"}\n"},"pid":77311}
{"timestamp":"2025-09-07T16:28:49.990Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":57,"content":"2025-09-08 00:28:49 [\u001b[32minfo\u001b[39m]: Fetching new token\n"},"pid":77311}
{"timestamp":"2025-09-07T16:28:49.990Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":66,"content":"info: Fetching new token {\"timestamp\":\"2025-09-07T16:28:49.989Z\"}\n"},"pid":77311}
{"timestamp":"2025-09-07T16:28:49.992Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":87,"content":"2025-09-08 00:28:49 [\u001b[34mdebug\u001b[39m]: API Request {\"method\":\"POST\",\"url\":\"/getToken\"}\n"},"pid":77311}
{"timestamp":"2025-09-07T16:28:49.993Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":94,"content":"debug: API Request {\"method\":\"POST\",\"timestamp\":\"2025-09-07T16:28:49.991Z\",\"url\":\"/getToken\"}\n"},"pid":77311}
{"timestamp":"2025-09-07T16:28:50.061Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":118,"content":"2025-09-08 00:28:50 [\u001b[31merror\u001b[39m]: API Error Request failed with status code 404 {\"status\":404,\"url\":\"/getToken\"}\n"},"pid":77311}
{"timestamp":"2025-09-07T16:28:50.062Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":125,"content":"error: API Error Request failed with status code 404 {\"status\":404,\"timestamp\":\"2025-09-07T16:28:50.061Z\",\"url\":\"/getToken\"}\n"},"pid":77311}
{"timestamp":"2025-09-07T16:28:50.063Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":1961,"content":"2025-09-08 00:28:50 [\u001b[31merror\u001b[39m]: Failed to get token {\"error\":{\"message\":\"Request failed with status code 404\",\"name\":\"AxiosError\",\"stack\":\"AxiosError: Request failed with status code 404\\n    a"},"pid":77311}
{"timestamp":"2025-09-07T16:28:50.064Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":1968,"content":"error: Failed to get token {\"error\":{\"code\":\"ERR_BAD_REQUEST\",\"config\":{\"adapter\":[\"xhr\",\"http\",\"fetch\"],\"allowAbsoluteUrls\":true,\"baseURL\":\"https://api-gw.test.huangbaoche.com/\",\"data\":\"{\\\"cid\\\":\\\"10"},"pid":77311}
{"timestamp":"2025-09-07T16:28:50.064Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":130,"content":"2025-09-08 00:28:50 [\u001b[31merror\u001b[39m]: Tool call failed {\"tool\":\"hitrip_get_token\",\"error\":\"Request failed with status code 404\"}\n"},"pid":77311}
{"timestamp":"2025-09-07T16:28:50.064Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":137,"content":"error: Tool call failed {\"error\":\"Request failed with status code 404\",\"timestamp\":\"2025-09-07T16:28:50.064Z\",\"tool\":\"hitrip_get_token\"}\n"},"pid":77311}
{"timestamp":"2025-09-07T16:28:50.064Z","level":"DEBUG","message":"Sending MCP response","data":{"id":3,"hasResult":true,"hasError":false},"pid":77311}
{"timestamp":"2025-09-07T16:28:50.985Z","level":"DEBUG","message":"Received MCP message","data":{"messageId":5,"length":112,"message":"{\"jsonrpc\":\"2.0\",\"id\":4,\"method\":\"tools/call\",\"params\":{\"name\":\"hitrip_get_cities\",\"arguments\":{\"countryId\":1}}}"},"pid":77311}
{"timestamp":"2025-09-07T16:28:50.986Z","level":"DEBUG","message":"Parsed MCP message","data":{"messageId":5,"method":"tools/call","id":4,"hasParams":true},"pid":77311}
{"timestamp":"2025-09-07T16:28:50.987Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":107,"content":"2025-09-08 00:28:50 [\u001b[32minfo\u001b[39m]: Tool call received {\"tool\":\"hitrip_get_cities\",\"args\":[\"countryId\"]}\n"},"pid":77311}
{"timestamp":"2025-09-07T16:28:50.988Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":114,"content":"info: Tool call received {\"args\":[\"countryId\"],\"timestamp\":\"2025-09-07T16:28:50.987Z\",\"tool\":\"hitrip_get_cities\"}\n"},"pid":77311}
{"timestamp":"2025-09-07T16:28:50.989Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":90,"content":"2025-09-08 00:28:50 [\u001b[34mdebug\u001b[39m]: API Request {\"method\":\"POST\",\"url\":\"/getCityList\"}\n"},"pid":77311}
{"timestamp":"2025-09-07T16:28:50.989Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":97,"content":"debug: API Request {\"method\":\"POST\",\"timestamp\":\"2025-09-07T16:28:50.988Z\",\"url\":\"/getCityList\"}\n"},"pid":77311}
{"timestamp":"2025-09-07T16:28:51.040Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":121,"content":"2025-09-08 00:28:51 [\u001b[31merror\u001b[39m]: API Error Request failed with status code 404 {\"status\":404,\"url\":\"/getCityList\"}\n"},"pid":77311}
{"timestamp":"2025-09-07T16:28:51.041Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":128,"content":"error: API Error Request failed with status code 404 {\"status\":404,\"timestamp\":\"2025-09-07T16:28:51.040Z\",\"url\":\"/getCityList\"}\n"},"pid":77311}
{"timestamp":"2025-09-07T16:28:51.041Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":131,"content":"2025-09-08 00:28:51 [\u001b[31merror\u001b[39m]: Tool call failed {\"tool\":\"hitrip_get_cities\",\"error\":\"Request failed with status code 404\"}\n"},"pid":77311}
{"timestamp":"2025-09-07T16:28:51.041Z","level":"DEBUG","message":"Sending non-JSON response","data":{"length":138,"content":"error: Tool call failed {\"error\":\"Request failed with status code 404\",\"timestamp\":\"2025-09-07T16:28:51.041Z\",\"tool\":\"hitrip_get_cities\"}\n"},"pid":77311}
{"timestamp":"2025-09-07T16:28:51.041Z","level":"DEBUG","message":"Sending MCP response","data":{"id":4,"hasResult":true,"hasError":false},"pid":77311}
{"timestamp":"2025-09-07T16:28:52.987Z","level":"INFO","message":"Received SIGTERM, shutting down","pid":77311}
