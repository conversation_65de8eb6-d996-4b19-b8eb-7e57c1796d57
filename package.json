{"name": "hitrip-mcp-server", "version": "1.0.0", "description": "MCP Server for Hitrip (黄包车) Open API integration", "type": "module", "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "start:stdio": "node dist/stdio-launcher.js", "start:sse": "node dist/sse-launcher.js", "test": "vitest", "test:watch": "vitest --watch", "type-check": "tsc --noEmit", "lint": "eslint src/**/*.ts", "clean": "rm -rf dist"}, "keywords": ["mcp", "hitrip", "travel", "api", "server"], "author": "Your Name", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^0.5.0", "@types/crypto-js": "^4.2.2", "axios": "^1.6.0", "crypto-js": "^4.2.0", "node-cache": "^5.1.2", "winston": "^3.11.0", "zod": "^3.22.0"}, "devDependencies": {"@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "tsx": "^4.0.0", "typescript": "^5.3.0", "vitest": "^1.0.0"}}