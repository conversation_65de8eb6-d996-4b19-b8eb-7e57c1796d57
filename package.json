{"name": "hitrip-mcp-server", "version": "1.0.0", "description": "MCP Server for Hitrip (黄包车) Open API integration", "type": "module", "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "start:stdio": "node dist/stdio-launcher.js", "start:sse": "node dist/sse-server-launcher.js", "start:http": "node dist/http-launcher.js", "dev:http": "tsc && node dist/http-launcher.js", "dev:sse": "tsc && node dist/sse-server-launcher.js", "test": "vitest", "test:watch": "vitest --watch", "type-check": "tsc --noEmit", "lint": "eslint src/**/*.ts", "clean": "rm -rf dist"}, "keywords": ["mcp", "hitrip", "travel", "api", "server"], "author": "rockenlee", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^0.5.0", "@types/cors": "^2.8.19", "@types/crypto-js": "^4.2.2", "@types/express": "^5.0.3", "@types/ws": "^8.18.1", "axios": "^1.6.0", "cors": "^2.8.5", "crypto-js": "^4.2.0", "eventsource": "^4.0.0", "express": "^5.1.0", "node-cache": "^5.1.2", "node-fetch": "^3.3.2", "winston": "^3.11.0", "ws": "^8.18.3", "zod": "^3.22.0"}, "devDependencies": {"@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "tsx": "^4.0.0", "typescript": "^5.3.0", "vitest": "^1.0.0"}}