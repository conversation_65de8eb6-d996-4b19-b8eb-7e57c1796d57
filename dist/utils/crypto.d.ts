export declare class CryptoUtils {
    private secretKey;
    constructor(secretKey: string);
    /**
     * 生成签名 (基于黄包车API文档1.5节)
     * 1. 对所有参数按ASCII码排序
     * 2. 拼装参数名和参数值，并加上secretKey
     * 3. UTF-8编码后进行MD5摘要
     * 4. 十六进制表示(小写)
     */
    generateSignature(params: Record<string, any>): string;
    /**
     * DES加密 (基于黄包车API文档1.6节)
     * 模式：ECB
     * 填充：PKCS5Padding
     * 编码：base64
     */
    desEncrypt(data: string): string;
    /**
     * DES解密
     */
    desDecrypt(encryptedData: string): string;
}
export declare function validateCrypto(): void;
//# sourceMappingURL=crypto.d.ts.map