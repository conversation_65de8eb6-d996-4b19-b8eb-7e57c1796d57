import winston from 'winston';
import { config } from '../config/index.js';
// 创建日志格式
const logFormat = winston.format.combine(winston.format.timestamp(), winston.format.errors({ stack: true }), winston.format.json());
// 创建控制台格式
const consoleFormat = winston.format.combine(winston.format.colorize(), winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }), winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let log = `${timestamp} [${level}]: ${message}`;
    if (Object.keys(meta).length > 0) {
        log += ` ${JSON.stringify(meta)}`;
    }
    return log;
}));
// 配置传输方式
const transports = [
    new winston.transports.Console({
        format: consoleFormat,
        level: config.logging.level,
    }),
];
// 如果配置了日志文件，添加文件传输
if (config.logging.file) {
    transports.push(new winston.transports.File({
        filename: config.logging.file,
        format: logFormat,
        level: config.logging.level,
    }));
}
// 创建日志器
export const logger = winston.createLogger({
    level: config.logging.level,
    format: logFormat,
    transports,
    // 处理未捕获的异常
    exceptionHandlers: [
        new winston.transports.Console({ format: consoleFormat }),
    ],
    // 处理未处理的Promise拒绝
    rejectionHandlers: [
        new winston.transports.Console({ format: consoleFormat }),
    ],
});
// 开发环境下的额外配置
if (config.environment === 'development') {
    logger.add(new winston.transports.Console({
        format: winston.format.simple()
    }));
}
// 导出便捷方法
export const log = {
    error: (message, meta) => logger.error(message, meta),
    warn: (message, meta) => logger.warn(message, meta),
    info: (message, meta) => logger.info(message, meta),
    debug: (message, meta) => logger.debug(message, meta),
};
//# sourceMappingURL=logger.js.map