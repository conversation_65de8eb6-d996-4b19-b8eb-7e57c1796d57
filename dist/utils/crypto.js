import crypto from 'crypto';
import CryptoJS from 'crypto-js';
export class CryptoUtils {
    secretKey;
    constructor(secretKey) {
        this.secretKey = secretKey;
    }
    /**
     * 生成签名 (基于黄包车API文档1.5节)
     * 1. 对所有参数按ASCII码排序
     * 2. 拼装参数名和参数值，并加上secretKey
     * 3. UTF-8编码后进行MD5摘要
     * 4. 十六进制表示(小写)
     */
    generateSignature(params) {
        // 1. 排序参数 (排除sign参数)
        const sortedKeys = Object.keys(params)
            .filter(key => key !== 'sign')
            .sort();
        // 2. 拼装字符串
        let signString = '';
        for (const key of sortedKeys) {
            const value = params[key];
            if (value !== undefined && value !== null) {
                signString += `${key}${value}`;
            }
        }
        signString += this.secretKey;
        // 3. UTF-8编码后MD5摘要
        const hash = crypto.createHash('md5');
        hash.update(signString, 'utf8');
        // 4. 十六进制表示(小写)
        return hash.digest('hex');
    }
    /**
     * DES加密 (基于黄包车API文档1.6节)
     * 模式：ECB
     * 填充：PKCS5Padding
     * 编码：base64
     */
    desEncrypt(data) {
        try {
            // 使用 CryptoJS 进行 DES 加密
            const key = CryptoJS.enc.Utf8.parse(this.secretKey);
            const encrypted = CryptoJS.DES.encrypt(data, key, {
                mode: CryptoJS.mode.ECB,
                padding: CryptoJS.pad.Pkcs7
            });
            return encrypted.toString();
        }
        catch (error) {
            throw new Error(`DES encryption failed: ${error}`);
        }
    }
    /**
     * DES解密
     */
    desDecrypt(encryptedData) {
        try {
            // 使用 CryptoJS 进行 DES 解密
            const key = CryptoJS.enc.Utf8.parse(this.secretKey);
            const decrypted = CryptoJS.DES.decrypt(encryptedData, key, {
                mode: CryptoJS.mode.ECB,
                padding: CryptoJS.pad.Pkcs7
            });
            return decrypted.toString(CryptoJS.enc.Utf8);
        }
        catch (error) {
            throw new Error(`DES decryption failed: ${error}`);
        }
    }
}
// 示例用法验证 (基于文档示例)
export function validateCrypto() {
    const crypto = new CryptoUtils('12345678');
    // 验证签名算法
    const params = {
        cid: '1234567890',
        flightDate: '2020-04-16',
        flightNo: 'CA181',
        timestamp: '2020-01-08 10:57:00'
    };
    const signature = crypto.generateSignature(params);
    console.log('Generated signature:', signature);
    // 应该输出: 412a4d874f61262b23ac7c767be59b47 (根据文档示例)
    // 验证DES加密
    const testData = '{"a":"123","b":"456"}';
    const encrypted = crypto.desEncrypt(testData);
    console.log('Encrypted data:', encrypted);
    // 应该输出: bh9ldnZ6iTTszhIOYXy3OKzx8R9NpERd (根据文档示例)
    // 验证解密
    const decrypted = crypto.desDecrypt(encrypted);
    console.log('Decrypted data:', decrypted);
    console.log('Encryption/Decryption test passed:', testData === decrypted);
}
//# sourceMappingURL=crypto.js.map