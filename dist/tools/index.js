export function createHitripTools(apiClient) {
    return [
        // 认证工具
        {
            name: 'hitrip_get_token',
            description: '获取黄包车API访问令牌',
            inputSchema: {
                type: 'object',
                properties: {},
                required: [],
            },
        },
        // 基础数据工具
        {
            name: 'hitrip_get_cities',
            description: '查询城市列表',
            inputSchema: {
                type: 'object',
                properties: {
                    keyword: {
                        type: 'string',
                        description: '搜索关键词，可选参数用于按城市名称搜索',
                    },
                },
                required: [],
            },
        },
        {
            name: 'hitrip_get_airports',
            description: '查询机场信息',
            inputSchema: {
                type: 'object',
                properties: {
                    keyword: {
                        type: 'string',
                        description: '搜索关键词，可选参数用于按机场名称或城市名称搜索',
                    },
                },
                required: [],
            },
        },
        {
            name: 'hitrip_get_vehicle_types',
            description: '查询所有可用的车型信息',
            inputSchema: {
                type: 'object',
                properties: {
                    countryId: {
                        type: 'number',
                        description: '国家ID，可选参数用于筛选特定国家的车型',
                    },
                },
                required: [],
            },
        },
        // 报价工具
        {
            name: 'hitrip_pickup_quote',
            description: '获取接机服务报价',
            inputSchema: {
                type: 'object',
                properties: {
                    airportCode: {
                        type: 'string',
                        description: '机场三字码',
                    },
                    airportName: {
                        type: 'string',
                        description: '机场名称',
                    },
                    serviceTime: {
                        type: 'string',
                        description: '服务时间，格式：YYYY-MM-DD HH:mm:ss',
                    },
                    serviceCityId: {
                        type: 'number',
                        description: '服务城市ID',
                    },
                    serviceCityName: {
                        type: 'string',
                        description: '服务城市名称',
                    },
                    startAddress: {
                        type: 'string',
                        description: '起始地址（机场地址）',
                    },
                    startDetailAddress: {
                        type: 'string',
                        description: '起始详细地址',
                    },
                    startLocation: {
                        type: 'string',
                        description: '起始坐标点，格式：lat,lng',
                    },
                    destination: {
                        type: 'object',
                        description: '目的地信息',
                        properties: {
                            address: { type: 'string', description: '目的地地址' },
                            addressDetail: { type: 'string', description: '目的地详细地址' },
                            location: { type: 'string', description: '目的地坐标点，格式：lat,lng' },
                        },
                        required: ['address', 'addressDetail', 'location'],
                    },
                    carModelId: {
                        type: 'string',
                        description: '车型ID（可指定车型报价，多个以逗号隔开），可选参数',
                    },
                },
                required: ['airportCode', 'airportName', 'serviceTime', 'serviceCityId', 'serviceCityName', 'startAddress', 'startDetailAddress', 'startLocation', 'destination'],
            },
        },
        {
            name: 'hitrip_dropoff_quote',
            description: '获取送机服务报价',
            inputSchema: {
                type: 'object',
                properties: {
                    airportCode: {
                        type: 'string',
                        description: '机场三字码',
                    },
                    airportName: {
                        type: 'string',
                        description: '机场名称',
                    },
                    serviceTime: {
                        type: 'string',
                        description: '服务时间，格式：YYYY-MM-DD HH:mm:ss',
                    },
                    serviceCityId: {
                        type: 'number',
                        description: '服务城市ID',
                    },
                    serviceCityName: {
                        type: 'string',
                        description: '服务城市名称',
                    },
                    origin: {
                        type: 'object',
                        description: '出发地信息',
                        properties: {
                            address: { type: 'string', description: '出发地地址' },
                            addressDetail: { type: 'string', description: '出发地详细地址' },
                            location: { type: 'string', description: '出发地坐标点，格式：lat,lng' },
                        },
                        required: ['address', 'addressDetail', 'location'],
                    },
                    endAddress: {
                        type: 'string',
                        description: '目的地地址（机场地址）',
                    },
                    endDetailAddress: {
                        type: 'string',
                        description: '目的地详细地址',
                    },
                    endLocation: {
                        type: 'string',
                        description: '目的地坐标点，格式：lat,lng',
                    },
                    carModelId: {
                        type: 'string',
                        description: '车型ID（可指定车型报价，多个以逗号隔开），可选参数',
                    },
                },
                required: ['airportCode', 'airportName', 'serviceTime', 'serviceCityId', 'serviceCityName', 'origin', 'endAddress', 'endDetailAddress', 'endLocation'],
            },
        },
        {
            name: 'hitrip_charter_quote',
            description: '获取包车服务报价',
            inputSchema: {
                type: 'object',
                properties: {
                    serviceTime: {
                        type: 'string',
                        description: '服务时间，格式：YYYY-MM-DD HH:mm:ss',
                    },
                    origin: {
                        type: 'object',
                        description: '出发地信息',
                        properties: {
                            address: { type: 'string', description: '出发地地址' },
                            addressDetail: { type: 'string', description: '详细地址' },
                            location: { type: 'string', description: '经纬度坐标，格式：lat,lng' },
                            cityId: { type: 'number', description: '城市ID' },
                            cityName: { type: 'string', description: '城市名称' },
                        },
                        required: ['address', 'location'],
                    },
                    destination: {
                        type: 'object',
                        description: '目的地信息',
                        properties: {
                            address: { type: 'string', description: '目的地地址' },
                            addressDetail: { type: 'string', description: '详细地址' },
                            location: { type: 'string', description: '经纬度坐标，格式：lat,lng' },
                            cityId: { type: 'number', description: '城市ID' },
                            cityName: { type: 'string', description: '城市名称' },
                        },
                        required: ['address', 'location'],
                    },
                    serviceHours: {
                        type: 'number',
                        description: '服务小时数，用于计算是否半日游（halfDay）',
                    },
                    tourType: {
                        type: 'number',
                        description: '旅游类型，默认为1',
                        default: 1,
                    },
                    distance: {
                        type: 'number',
                        description: '预估距离（米），可选参数',
                    },
                    duration: {
                        type: 'number',
                        description: '预估时长（分钟），可选参数',
                    },
                },
                required: ['serviceTime', 'origin', 'destination'],
            },
        },
        // 预订工具
        {
            name: 'hitrip_create_pickup_order',
            description: '创建接机订单',
            inputSchema: {
                type: 'object',
                properties: {
                    // 航班信息
                    flightNo: { type: 'string', description: '航班编号' },
                    flightDestCode: { type: 'string', description: '降落机场三字码' },
                    flightDestAirportName: { type: 'string', description: '降落机场名称' },
                    flightDestBuilding: { type: 'string', description: '降落机场航站楼' },
                    flightArriveTime: { type: 'string', description: '航班计划到达时间，格式：YYYY-MM-DD HH:mm:ss' },
                    flightFlyTime: { type: 'string', description: '航班起飞时间，格式：YYYY-MM-DD HH:mm:ss' },
                    flightAirportCode: { type: 'string', description: '起飞机场三字码' },
                    flightAirportName: { type: 'string', description: '起飞机场名称' },
                    // 订单信息
                    orderThirdNo: { type: 'string', description: '第三方订单号' },
                    priceActual: { type: 'number', description: '实际支付价格，单位：分' },
                    priceChannel: { type: 'number', description: '渠道价格（不包含增项费用），单位：分' },
                    // 服务信息
                    serviceCarModel: { type: 'string', description: '车型ID' },
                    serviceCarModelName: { type: 'string', description: '车型名称' },
                    serviceCityId: { type: 'number', description: '服务城市ID' },
                    serviceCityName: { type: 'string', description: '服务城市名称' },
                    serviceTime: { type: 'string', description: '服务时间，格式：YYYY-MM-DD HH:mm:ss' },
                    // 地址信息
                    serviceStartAddress: { type: 'string', description: '出发地（机场地址）' },
                    serviceStartAddressDetail: { type: 'string', description: '出发地详细地址' },
                    serviceStartPoint: { type: 'string', description: '出发地经纬度，格式：lat,lng' },
                    serviceDestAddress: { type: 'string', description: '终止地（目的地地址）' },
                    serviceDestAddressDetail: { type: 'string', description: '终止地详细地址' },
                    serviceDestPoint: { type: 'string', description: '终止地经纬度，格式：lat,lng' },
                    // 乘客信息
                    servicePassagerName: { type: 'string', description: '乘车人名称' },
                    servicePassagerMobile: { type: 'string', description: '乘车人手机号码' },
                    servicePassagerAreacode: { type: 'string', description: '乘车人手机区号' },
                    // 商户信息
                    orderChannel: { type: 'string', description: '商户ID' },
                    orderChannelName: { type: 'string', description: '商户名称' },
                    // 报价参数（必需）
                    airportPriceQuestParam: {
                        type: 'object',
                        description: '报价参数',
                        properties: {
                            airportCode: { type: 'string', description: '机场三字码' },
                            airportName: { type: 'string', description: '机场名称' },
                            channelId: { type: 'number', description: '渠道ID' },
                            endAddress: { type: 'string', description: '目的地地址' },
                            endDetailAddress: { type: 'string', description: '目的地详细地址' },
                            endLocation: { type: 'string', description: '目的地坐标点' },
                            serviceCityId: { type: 'number', description: '服务城市ID' },
                            serviceCityName: { type: 'string', description: '服务城市名称' },
                            serviceTime: { type: 'string', description: '服务时间' },
                            startAddress: { type: 'string', description: '起始地址' },
                            startCityId: { type: 'number', description: '起始城市ID' },
                            startCityName: { type: 'string', description: '起始城市名称' },
                            startDetailAddress: { type: 'string', description: '起始详细地址' },
                            startLocation: { type: 'string', description: '起始坐标点' },
                        },
                        required: ['airportCode', 'airportName', 'channelId', 'endAddress', 'endDetailAddress', 'endLocation', 'serviceCityId', 'serviceCityName', 'serviceTime', 'startAddress', 'startCityId', 'startCityName', 'startDetailAddress', 'startLocation'],
                    },
                    // 可选信息
                    userRemark: { type: 'string', description: '用户留言' },
                    serviceStartPoi: { type: 'string', description: 'POI起点ID' },
                    serviceDestPoi: { type: 'string', description: 'POI终点ID' },
                    serviceEndCityId: { type: 'number', description: '终止地城市ID' },
                    serviceEndCityName: { type: 'string', description: '终止地城市名' },
                    serviceContinentId: { type: 'number', description: '服务大洲ID' },
                    serviceContinentName: { type: 'string', description: '服务大洲名称' },
                    serviceCountryId: { type: 'number', description: '服务国家ID' },
                    serviceCountryName: { type: 'string', description: '服务国家名称' },
                },
                required: [
                    'flightNo', 'flightDestCode', 'flightDestAirportName', 'orderThirdNo',
                    'priceActual', 'priceChannel', 'serviceCarModel', 'serviceCarModelName',
                    'serviceCityId', 'serviceCityName', 'serviceTime', 'serviceStartAddress',
                    'serviceStartAddressDetail', 'serviceStartPoint', 'serviceDestAddress',
                    'serviceDestAddressDetail', 'serviceDestPoint', 'servicePassagerName',
                    'servicePassagerMobile', 'servicePassagerAreacode', 'orderChannel', 'orderChannelName',
                    'airportPriceQuestParam'
                ],
            },
        },
        {
            name: 'hitrip_create_dropoff_order',
            description: '创建送机订单',
            inputSchema: {
                type: 'object',
                properties: {
                    // 航班信息
                    flightNo: { type: 'string', description: '航班编号' },
                    flightDestCode: { type: 'string', description: '降落机场三字码' },
                    flightDestAirportName: { type: 'string', description: '降落机场名称' },
                    flightDestBuilding: { type: 'string', description: '降落机场航站楼' },
                    flightArriveTime: { type: 'string', description: '航班计划到达时间，格式：YYYY-MM-DD HH:mm:ss' },
                    flightFlyTime: { type: 'string', description: '航班起飞时间，格式：YYYY-MM-DD HH:mm:ss' },
                    flightAirportCode: { type: 'string', description: '起飞机场三字码' },
                    flightAirportName: { type: 'string', description: '起飞机场名称' },
                    // 订单信息
                    orderNo: { type: 'string', description: '订单号' },
                    orderThirdNo: { type: 'string', description: '第三方订单号' },
                    priceActual: { type: 'number', description: '实际支付价格，单位：分' },
                    priceChannel: { type: 'number', description: '渠道价格（不包含增项费用），单位：分' },
                    // 服务信息
                    serviceCarModel: { type: 'string', description: '车型ID' },
                    serviceCarModelName: { type: 'string', description: '车型名称' },
                    serviceCityId: { type: 'number', description: '服务城市ID' },
                    serviceCityName: { type: 'string', description: '服务城市名称' },
                    serviceTime: { type: 'string', description: '服务时间，格式：YYYY-MM-DD HH:mm:ss' },
                    // 地址信息
                    serviceStartAddress: { type: 'string', description: '出发地地址' },
                    serviceStartAddressDetail: { type: 'string', description: '出发地详细地址' },
                    serviceStartPoint: { type: 'string', description: '出发地经纬度，格式：lat,lng' },
                    serviceDestAddress: { type: 'string', description: '终止地（机场地址）' },
                    serviceDestAddressDetail: { type: 'string', description: '终止地详细地址' },
                    serviceDestPoint: { type: 'string', description: '终止地经纬度，格式：lat,lng' },
                    // 乘客信息
                    servicePassagerName: { type: 'string', description: '乘车人名称' },
                    servicePassagerMobile: { type: 'string', description: '乘车人手机号码' },
                    servicePassagerAreacode: { type: 'string', description: '乘车人手机区号' },
                    // 商户信息
                    orderChannel: { type: 'string', description: '商户ID' },
                    orderChannelName: { type: 'string', description: '商户名称' },
                    // 报价参数（必需）
                    airportPriceQuestParam: {
                        type: 'object',
                        description: '报价参数',
                        properties: {
                            airportCode: { type: 'string', description: '机场三字码' },
                            airportName: { type: 'string', description: '机场名称' },
                            channelId: { type: 'number', description: '渠道ID' },
                            endAddress: { type: 'string', description: '目的地地址' },
                            endDetailAddress: { type: 'string', description: '目的地详细地址' },
                            endLocation: { type: 'string', description: '目的地坐标点' },
                            serviceCityId: { type: 'number', description: '服务城市ID' },
                            serviceCityName: { type: 'string', description: '服务城市名称' },
                            serviceTime: { type: 'string', description: '服务时间' },
                            startAddress: { type: 'string', description: '起始地址' },
                            startCityId: { type: 'number', description: '起始城市ID' },
                            startCityName: { type: 'string', description: '起始城市名称' },
                            startDetailAddress: { type: 'string', description: '起始详细地址' },
                            startLocation: { type: 'string', description: '起始坐标点' },
                        },
                        required: ['airportCode', 'airportName', 'channelId', 'endAddress', 'endDetailAddress', 'endLocation', 'serviceCityId', 'serviceCityName', 'serviceTime', 'startAddress', 'startCityId', 'startCityName', 'startDetailAddress', 'startLocation'],
                    },
                    // 可选信息
                    userRemark: { type: 'string', description: '用户留言' },
                    serviceStartPoi: { type: 'string', description: 'POI起点ID' },
                    serviceDestPoi: { type: 'string', description: 'POI终点ID' },
                    serviceEndCityId: { type: 'number', description: '终止地城市ID' },
                    serviceEndCityName: { type: 'string', description: '终止地城市名' },
                    serviceContinentId: { type: 'number', description: '服务大洲ID' },
                    serviceContinentName: { type: 'string', description: '服务大洲名称' },
                    serviceCountryId: { type: 'number', description: '服务国家ID' },
                    serviceCountryName: { type: 'string', description: '服务国家名称' },
                },
                required: [
                    'flightDestCode', 'flightDestAirportName', 'orderNo', 'orderThirdNo',
                    'priceActual', 'priceChannel', 'serviceCarModel', 'serviceCarModelName',
                    'serviceCityId', 'serviceCityName', 'serviceTime', 'serviceStartAddress',
                    'serviceStartAddressDetail', 'serviceStartPoint', 'serviceDestAddress',
                    'serviceDestAddressDetail', 'serviceDestPoint', 'servicePassagerName',
                    'servicePassagerMobile', 'servicePassagerAreacode', 'orderChannel', 'orderChannelName',
                    'airportPriceQuestParam'
                ],
            },
        },
        {
            name: 'hitrip_create_charter_order',
            description: '创建包车订单（简化版本，包含基本必需参数）',
            inputSchema: {
                type: 'object',
                properties: {
                    // 订单基础信息
                    orderNoThird: { type: 'string', description: '第三方订单号' },
                    travelStartTimeLocal: { type: 'string', description: '行程开始时间（当地），格式：YYYY-MM-DD HH:mm:ss' },
                    travelEndTimeLocal: { type: 'string', description: '行程结束时间（当地），格式：YYYY-MM-DD HH:mm:ss' },
                    travelStartTimeBeijing: { type: 'string', description: '行程开始时间（北京），格式：YYYY-MM-DD HH:mm:ss' },
                    travelEndTimeBeijing: { type: 'string', description: '行程结束时间（北京），格式：YYYY-MM-DD HH:mm:ss' },
                    totalDay: { type: 'number', description: '本次行程的总天数' },
                    // 城市信息
                    startCityId: { type: 'number', description: '出发城市ID' },
                    startCityName: { type: 'string', description: '出发城市名称' },
                    endCityId: { type: 'number', description: '结束城市ID' },
                    endCityName: { type: 'string', description: '结束城市名称' },
                    // 价格信息
                    amountChannelSys: { type: 'number', description: '渠道上的系统建议卖价，单位：分' },
                    amountShouldPay: { type: 'number', description: '总应付金额，单位：分' },
                    amountShouldReceive: { type: 'number', description: '总应收金额，单位：分' },
                    amountActualPay: { type: 'number', description: '总实付金额，单位：分' },
                    amountActualReceive: { type: 'number', description: '总实收金额，单位：分' },
                    amountCouponTotal: { type: 'number', description: '总渠道优惠金额，单位：分' },
                    // 联系人信息
                    customerName: { type: 'string', description: '主联系人姓名' },
                    customerAreaCode: { type: 'string', description: '主联系人手机区号' },
                    customerMobile: { type: 'string', description: '主联系人手机号' },
                    customerWechat: { type: 'string', description: '主联系人微信号' },
                    // 支付信息
                    payTime: { type: 'string', description: '支付时间，格式：YYYY-MM-DD HH:mm:ss' },
                    thirdPayNo: { type: 'string', description: '第三方支付单号' },
                    // 其他信息
                    priceMarkCity: { type: 'number', description: '报价城市ID' },
                    remark: { type: 'string', description: '订单备注，最多500个字符' },
                    // 每日行程（简化为单日）
                    travelDailyBaseReq: {
                        type: 'object',
                        description: '每日行程信息（简化为单日）',
                        properties: {
                            dayIndex: { type: 'number', description: '第几天，从1开始' },
                            goodsType: { type: 'number', description: '商品细分类型：40=10小时100公里,41=10小时300,42=10小时600公里' },
                            goodsTypeName: { type: 'string', description: '商品细分类型名称' },
                            serviceStartTimeLocal: { type: 'string', description: '服务开始时间（当地），格式：YYYY-MM-DD HH:mm:ss' },
                            serviceEndTimeLocal: { type: 'string', description: '服务结束时间（当地），格式：YYYY-MM-DD HH:mm:ss' },
                            serviceStartTimeBeijing: { type: 'string', description: '服务开始时间（北京），格式：YYYY-MM-DD HH:mm:ss' },
                            serviceEndTimeBeijing: { type: 'string', description: '服务结束时间（北京），格式：YYYY-MM-DD HH:mm:ss' },
                            carModelId: { type: 'number', description: '车型ID' },
                            carModelName: { type: 'string', description: '车型名称' },
                            startCityId: { type: 'number', description: '出发城市ID' },
                            startCityName: { type: 'string', description: '出发城市名称' },
                            endCityId: { type: 'number', description: '结束城市ID' },
                            endCityName: { type: 'string', description: '结束城市名称' },
                            serviceMaxTime: { type: 'number', description: '服务最大时间（小时）' },
                            serviceMaxDistance: { type: 'number', description: '服务最大距离' },
                        },
                        required: ['dayIndex', 'goodsType', 'goodsTypeName', 'serviceStartTimeLocal', 'serviceEndTimeLocal', 'serviceStartTimeBeijing', 'serviceEndTimeBeijing', 'carModelId', 'carModelName', 'startCityId', 'startCityName', 'endCityId', 'endCityName', 'serviceMaxTime', 'serviceMaxDistance'],
                    },
                },
                required: [
                    'orderNoThird', 'travelStartTimeLocal', 'travelEndTimeLocal', 'travelStartTimeBeijing',
                    'travelEndTimeBeijing', 'totalDay', 'startCityId', 'endCityId', 'amountChannelSys',
                    'amountShouldPay', 'amountShouldReceive', 'amountActualPay', 'amountActualReceive',
                    'amountCouponTotal', 'customerName', 'customerAreaCode', 'customerMobile', 'payTime',
                    'priceMarkCity', 'travelDailyBaseReq'
                ],
            },
        },
        // 订单管理工具
        {
            name: 'hitrip_get_order_list',
            description: '获取订单列表',
            inputSchema: {
                type: 'object',
                properties: {
                    limit: { type: 'number', description: '每页记录数' },
                    offset: { type: 'number', description: '偏移量' },
                    listType: {
                        type: 'number',
                        description: '列表类型：1-全部；2-待支付；3-服务中；4-已取消'
                    },
                    userId: { type: 'string', description: '用户ID' },
                },
                required: ['limit', 'offset', 'listType', 'userId'],
            },
        },
        {
            name: 'hitrip_get_order_detail',
            description: '获取订单详情',
            inputSchema: {
                type: 'object',
                properties: {
                    orderTravelNo: {
                        type: 'string',
                        description: '订单号（与orderNoThird二选一）',
                    },
                    orderNoThird: {
                        type: 'string',
                        description: '第三方订单号（与orderTravelNo二选一）',
                    },
                },
                required: [],
            },
        },
        {
            name: 'hitrip_cancel_order',
            description: '取消订单',
            inputSchema: {
                type: 'object',
                properties: {
                    orderTravelNo: {
                        type: 'string',
                        description: '订单号',
                    },
                    cancelReason: {
                        type: 'string',
                        description: '取消原因',
                    },
                    cancelType: {
                        type: 'number',
                        description: '取消类型（1:用户原因 2:工作室原因 3:平台原因 4:不可抗力）',
                    },
                    cancelRemark: {
                        type: 'string',
                        description: '备注（可选）',
                    },
                    actuallyPaidPrice: {
                        type: 'number',
                        description: '实付金额（可选）',
                    },
                },
                required: ['orderTravelNo', 'cancelReason', 'cancelType'],
            },
        },
    ];
}
//# sourceMappingURL=index.js.map