import { HitripConfig } from '../types/config.js';
import { CityInfo, AirportInfo, VehicleType } from '../types/hitrip-api/common.js';
import { QuoteListResponse } from '../types/hitrip-api/quote.js';
import { OrderResponse, OrderListResponse } from '../types/hitrip-api/order.js';
export declare class HitripApiClient {
    private config;
    private httpClient;
    private crypto;
    private cache;
    constructor(config: HitripConfig);
    private setupInterceptors;
    private generateTimestamp;
    private createSignedParams;
    getToken(): Promise<string>;
    getCities(params?: {
        keyword?: string;
    }): Promise<CityInfo[]>;
    getAirports(params?: {
        keyword?: string;
    }): Promise<AirportInfo[]>;
    getVehicleTypes(params?: {
        countryId?: number;
    }): Promise<VehicleType[]>;
    getPickupQuote(toolParams: any): Promise<QuoteListResponse>;
    getDropoffQuote(toolParams: any): Promise<QuoteListResponse>;
    getCharterQuote(toolParams: any): Promise<QuoteListResponse>;
    createPickupOrder(toolParams: any): Promise<OrderResponse>;
    createDropoffOrder(toolParams: any): Promise<OrderResponse>;
    createCharterOrder(toolParams: any): Promise<OrderResponse>;
    getOrderList(toolParams: any): Promise<OrderListResponse>;
    getOrderDetail(toolParams: any): Promise<OrderResponse>;
    cancelOrder(toolParams: any): Promise<{
        success: boolean;
        message?: string;
    }>;
}
//# sourceMappingURL=client.d.ts.map