import { HitripConfig } from '../types/config.js';
import { CityInfo, AirportInfo, VehicleType } from '../types/hitrip-api/common.js';
import { CharterQuoteRequest, QuoteListResponse } from '../types/hitrip-api/quote.js';
import { PickupOrderRequest, DropoffOrderRequest, CharterOrderRequest, OrderResponse, OrderListRequest, OrderListResponse, OrderDetailRequest, CancelOrderRequest } from '../types/hitrip-api/order.js';
export declare class HitripApiClient {
    private config;
    private httpClient;
    private crypto;
    private cache;
    constructor(config: HitripConfig);
    private setupInterceptors;
    private generateTimestamp;
    private createSignedParams;
    getToken(): Promise<string>;
    getCities(params?: {
        keyword?: string;
    }): Promise<CityInfo[]>;
    getAirports(params?: {
        keyword?: string;
    }): Promise<AirportInfo[]>;
    getVehicleTypes(params?: {
        countryId?: number;
    }): Promise<VehicleType[]>;
    getPickupQuote(toolParams: any): Promise<QuoteListResponse>;
    getDropoffQuote(toolParams: any): Promise<QuoteListResponse>;
    getCharterQuote(params: CharterQuoteRequest): Promise<QuoteListResponse>;
    createPickupOrder(params: PickupOrderRequest): Promise<OrderResponse>;
    createDropoffOrder(params: DropoffOrderRequest): Promise<OrderResponse>;
    createCharterOrder(params: CharterOrderRequest): Promise<OrderResponse>;
    getOrderList(params: OrderListRequest): Promise<OrderListResponse>;
    getOrderDetail(params: OrderDetailRequest): Promise<OrderResponse>;
    cancelOrder(params: CancelOrderRequest): Promise<{
        success: boolean;
        message?: string;
    }>;
}
//# sourceMappingURL=client.d.ts.map