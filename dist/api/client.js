import axios from 'axios';
import NodeCache from 'node-cache';
import { CryptoUtils } from '../utils/crypto.js';
import { logger } from '../utils/logger.js';
export class HitripApiClient {
    config;
    httpClient;
    crypto;
    cache;
    constructor(config) {
        this.config = config;
        this.crypto = new CryptoUtils(config.secretKey);
        // 初始化缓存
        this.cache = new NodeCache({
            stdTTL: config.cache.tokenTtl,
            checkperiod: 600, // 10分钟检查一次过期
            useClones: false,
        });
        // 初始化HTTP客户端
        this.httpClient = axios.create({
            baseURL: config.baseUrl,
            timeout: 30000,
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'Hitrip-MCP-Server/1.0.0',
            },
        });
        this.setupInterceptors();
    }
    setupInterceptors() {
        // 请求拦截器
        this.httpClient.interceptors.request.use((config) => {
            logger.debug('API Request', {
                method: config.method?.toUpperCase(),
                url: config.url,
                params: config.params,
            });
            return config;
        }, (error) => {
            logger.error('Request interceptor error', { error });
            return Promise.reject(error);
        });
        // 响应拦截器
        this.httpClient.interceptors.response.use((response) => {
            logger.debug('API Response', {
                status: response.status,
                url: response.config.url,
                success: response.data?.success,
            });
            return response;
        }, (error) => {
            logger.error('API Error', {
                status: error.response?.status,
                url: error.config?.url,
                message: error.response?.data?.message || error.message,
            });
            return Promise.reject(error);
        });
    }
    generateTimestamp() {
        return new Date().toISOString().replace('T', ' ').substring(0, 19);
    }
    createSignedParams(params) {
        const timestamp = this.generateTimestamp();
        const signedParams = {
            ...params,
            cid: this.config.cid,
            timestamp,
        };
        signedParams.sign = this.crypto.generateSignature(signedParams);
        return signedParams;
    }
    // Token管理
    async getToken() {
        const cacheKey = `token_${this.config.cid}`;
        if (this.config.cache.enabled) {
            const cached = this.cache.get(cacheKey);
            if (cached && cached.expiresAt > Date.now()) {
                logger.debug('Using cached token');
                return cached.token;
            }
        }
        logger.info('Fetching new token');
        try {
            const params = this.createSignedParams({});
            const response = await this.httpClient.post('/getToken', params);
            if (!response.data.success) {
                throw new Error(`Token request failed: ${response.data.status}`);
            }
            const token = response.data.data;
            if (this.config.cache.enabled) {
                const cachedToken = {
                    token,
                    expiresAt: Date.now() + (this.config.cache.tokenTtl * 1000),
                    createdAt: Date.now(),
                };
                this.cache.set(cacheKey, cachedToken);
            }
            logger.info('Token obtained successfully');
            return token;
        }
        catch (error) {
            logger.error('Failed to get token', { error });
            throw error;
        }
    }
    // 基础数据查询
    async getCities(params = {}) {
        const signedParams = this.createSignedParams(params);
        const response = await this.httpClient.post('/getCityList', signedParams);
        if (!response.data.success) {
            throw new Error(`Get cities failed: ${response.data.message}`);
        }
        return response.data.data;
    }
    async getAirports(params = {}) {
        const signedParams = this.createSignedParams(params);
        const response = await this.httpClient.post('/getAirportList', signedParams);
        if (!response.data.success) {
            throw new Error(`Get airports failed: ${response.data.message}`);
        }
        return response.data.data;
    }
    async getVehicleTypes(params = {}) {
        const signedParams = this.createSignedParams(params);
        const response = await this.httpClient.post('/getVehicleTypeList', signedParams);
        if (!response.data.success) {
            throw new Error(`Get vehicle types failed: ${response.data.message}`);
        }
        return response.data.data;
    }
    // 报价相关方法
    async getPickupQuote(params) {
        const signedParams = this.createSignedParams(params);
        const response = await this.httpClient.post('/open/getPickupQuote', signedParams);
        if (!response.data.success) {
            throw new Error(`Get pickup quote failed: ${response.data.message}`);
        }
        return response.data.data;
    }
    async getDropoffQuote(params) {
        const signedParams = this.createSignedParams(params);
        const response = await this.httpClient.post('/open/getDropoffQuote', signedParams);
        if (!response.data.success) {
            throw new Error(`Get dropoff quote failed: ${response.data.message}`);
        }
        return response.data.data;
    }
    async getCharterQuote(params) {
        const signedParams = this.createSignedParams(params);
        const response = await this.httpClient.post('/open/getCharterQuote', signedParams);
        if (!response.data.success) {
            throw new Error(`Get charter quote failed: ${response.data.message}`);
        }
        return response.data.data;
    }
    // 订单相关方法
    async createPickupOrder(params) {
        const signedParams = this.createSignedParams(params);
        const response = await this.httpClient.post('/open/createPickupOrder', signedParams);
        if (!response.data.success) {
            throw new Error(`Create pickup order failed: ${response.data.message}`);
        }
        return response.data.data;
    }
    async createDropoffOrder(params) {
        const signedParams = this.createSignedParams(params);
        const response = await this.httpClient.post('/open/createDropoffOrder', signedParams);
        if (!response.data.success) {
            throw new Error(`Create dropoff order failed: ${response.data.message}`);
        }
        return response.data.data;
    }
    async createCharterOrder(params) {
        const signedParams = this.createSignedParams(params);
        const response = await this.httpClient.post('/open/createCharterOrder', signedParams);
        if (!response.data.success) {
            throw new Error(`Create charter order failed: ${response.data.message}`);
        }
        return response.data.data;
    }
    async getOrderList(params = {}) {
        const signedParams = this.createSignedParams({
            pageNum: 1,
            pageSize: 10,
            ...params,
        });
        const response = await this.httpClient.post('/open/getOrderList', signedParams);
        if (!response.data.success) {
            throw new Error(`Get order list failed: ${response.data.message}`);
        }
        return response.data.data;
    }
    async getOrderDetail(params) {
        const signedParams = this.createSignedParams(params);
        const response = await this.httpClient.post('/open/getOrderDetail', signedParams);
        if (!response.data.success) {
            throw new Error(`Get order detail failed: ${response.data.message}`);
        }
        return response.data.data;
    }
    async cancelOrder(params) {
        const signedParams = this.createSignedParams(params);
        const response = await this.httpClient.post('/open/cancelOrder', signedParams);
        if (!response.data.success) {
            throw new Error(`Cancel order failed: ${response.data.message}`);
        }
        return response.data.data;
    }
}
//# sourceMappingURL=client.js.map