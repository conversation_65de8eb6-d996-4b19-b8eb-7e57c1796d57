import axios from 'axios';
import NodeCache from 'node-cache';
import { CryptoUtils } from '../utils/crypto.js';
import { logger } from '../utils/logger.js';
export class HitripApiClient {
    config;
    httpClient;
    crypto;
    cache;
    constructor(config) {
        this.config = config;
        this.crypto = new CryptoUtils(config.secretKey);
        // 初始化缓存
        this.cache = new NodeCache({
            stdTTL: config.cache.tokenTtl,
            checkperiod: 600, // 10分钟检查一次过期
            useClones: false,
        });
        // 初始化HTTP客户端
        this.httpClient = axios.create({
            baseURL: config.baseUrl,
            timeout: 30000,
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'Hitrip-MCP-Server/1.0.0',
            },
        });
        this.setupInterceptors();
    }
    setupInterceptors() {
        // 请求拦截器
        this.httpClient.interceptors.request.use((config) => {
            logger.debug('API Request', {
                method: config.method?.toUpperCase(),
                url: config.url,
                params: config.params,
            });
            return config;
        }, (error) => {
            logger.error('Request interceptor error', { error });
            return Promise.reject(error);
        });
        // 响应拦截器
        this.httpClient.interceptors.response.use((response) => {
            logger.debug('API Response', {
                status: response.status,
                url: response.config.url,
                success: response.data?.success,
            });
            return response;
        }, (error) => {
            logger.error('API Error', {
                status: error.response?.status,
                url: error.config?.url,
                message: error.response?.data?.message || error.message,
            });
            return Promise.reject(error);
        });
    }
    generateTimestamp() {
        return new Date().toISOString().replace('T', ' ').substring(0, 19);
    }
    createSignedParams(params) {
        const timestamp = this.generateTimestamp();
        const signedParams = {
            ...params,
            cid: this.config.cid,
            timestamp,
        };
        signedParams.sign = this.crypto.generateSignature(signedParams);
        return signedParams;
    }
    // Token管理
    async getToken() {
        const cacheKey = `token_${this.config.cid}`;
        if (this.config.cache.enabled) {
            const cached = this.cache.get(cacheKey);
            if (cached && cached.expiresAt > Date.now()) {
                logger.debug('Using cached token');
                return cached.token;
            }
        }
        logger.info('Fetching new token');
        try {
            // 黄包车getToken使用GET请求，参数在URL中
            const url = `/otaorder-api/access/getAccessToken?cid=${this.config.cid}&secretKey=${this.config.secretKey}`;
            const response = await this.httpClient.get(url);
            if (!response.data.success) {
                throw new Error(`Token request failed: ${response.data.status}`);
            }
            const token = response.data.data;
            if (this.config.cache.enabled) {
                const cachedToken = {
                    token,
                    expiresAt: Date.now() + (this.config.cache.tokenTtl * 1000),
                    createdAt: Date.now(),
                };
                this.cache.set(cacheKey, cachedToken);
            }
            logger.info('Token obtained successfully');
            return token;
        }
        catch (error) {
            logger.error('Failed to get token', { error });
            throw error;
        }
    }
    // 基础数据查询
    async getCities(params = {}) {
        const token = await this.getToken();
        const signedParams = this.createSignedParams(params);
        const response = await this.httpClient.post(`/otaorder-api/common/v1.0/cities/listByNameAndSpell?token=${token}`, signedParams);
        if (!response.data.success) {
            throw new Error(`Get cities failed: ${response.data.message}`);
        }
        return response.data.data;
    }
    async getAirports(params = {}) {
        const token = await this.getToken();
        const signedParams = this.createSignedParams(params);
        const response = await this.httpClient.post(`/otaorder-api/common/v1.0/airport/listByNameAndSpell?token=${token}`, signedParams);
        if (!response.data.success) {
            throw new Error(`Get airports failed: ${response.data.message}`);
        }
        return response.data.data;
    }
    async getVehicleTypes(params = {}) {
        const token = await this.getToken();
        const signedParams = this.createSignedParams(params);
        const response = await this.httpClient.post(`/otaorder-api/common/v1.0/vehicleType/queryAllValid?token=${token}`, signedParams);
        if (!response.data.success) {
            throw new Error(`Get vehicle types failed: ${response.data.message}`);
        }
        return response.data.data;
    }
    // 报价相关方法
    async getPickupQuote(params) {
        const token = await this.getToken();
        // 构造接机报价请求参数，直接使用传入的参数
        const requestBody = {
            airportCode: params.airportCode,
            airportName: params.airportName,
            serviceTime: params.serviceTime,
            serviceCityId: params.serviceCityId,
            serviceCityName: params.serviceCityName,
            startAddress: params.startAddress,
            startDetailAddress: params.startDetailAddress,
            startLocation: params.startLocation,
            endAddress: params.endAddress,
            endDetailAddress: params.endDetailAddress,
            endLocation: params.endLocation,
            carModelId: params.carModelId
        };
        const signedParams = this.createSignedParams(requestBody);
        const response = await this.httpClient.post(`/otaorder-api/common/v1.0/quoteprice/pickup?token=${token}`, signedParams);
        if (!response.data.success) {
            throw new Error(`Get pickup quote failed: ${response.data.message}`);
        }
        return response.data.data;
    }
    async getDropoffQuote(params) {
        const token = await this.getToken();
        // 构造送机报价请求参数，直接使用传入的参数
        const requestBody = {
            airportCode: params.airportCode,
            airportName: params.airportName,
            serviceTime: params.serviceTime,
            serviceCityId: params.serviceCityId,
            serviceCityName: params.serviceCityName,
            startAddress: params.startAddress,
            startDetailAddress: params.startDetailAddress,
            startLocation: params.startLocation,
            endAddress: params.endAddress,
            endDetailAddress: params.endDetailAddress,
            endLocation: params.endLocation,
            carModelId: params.carModelId
        };
        const signedParams = this.createSignedParams(requestBody);
        const response = await this.httpClient.post(`/otaorder-api/common/v1.0/quoteprice/transfer?token=${token}`, signedParams);
        if (!response.data.success) {
            throw new Error(`Get dropoff quote failed: ${response.data.message}`);
        }
        return response.data.data;
    }
    async getCharterQuote(params) {
        const token = await this.getToken();
        // 构造包车报价请求参数
        const requestBody = {
            channelId: parseInt(this.config.cid), // 使用 CID 作为 channelId
            serviceTime: params.serviceTime,
            singleDailyParamList: [
                {
                    startAddress: params.origin.address,
                    startDetailAddress: params.origin.addressDetail || params.origin.address,
                    startLocation: params.origin.location,
                    startServiceCityId: params.origin.cityId,
                    startServiceCityName: params.origin.cityName,
                    startServiceTime: params.serviceTime,
                    endAddress: params.destination.address,
                    endDetailAddress: params.destination.addressDetail || params.destination.address,
                    endLocation: params.destination.location,
                    endServiceCityId: params.destination.cityId,
                    endServiceCityName: params.destination.cityName,
                    tourType: params.tourType || 1,
                    halfDay: params.serviceHours && params.serviceHours <= 4 ? 1 : 0,
                    distance: params.distance || 0,
                    duration: params.duration || 0,
                    passPoiList: [] // 暂时为空，后续可扩展
                }
            ]
        };
        const signedParams = this.createSignedParams(requestBody);
        const response = await this.httpClient.post(`/otaorder-api/common/v1.0/quoteprice/daily?token=${token}`, signedParams);
        if (!response.data.success) {
            throw new Error(`Get charter quote failed: ${response.data.message}`);
        }
        return response.data.data;
    }
    // 订单相关方法
    async createPickupOrder(params) {
        const token = await this.getToken();
        // 接机订单需要DES加密
        const encryptedParams = this.crypto.desEncrypt(JSON.stringify(params));
        const signedParams = this.createSignedParams({ encryptBody: encryptedParams });
        const response = await this.httpClient.post(`/otaorder-api/common/v1.0/order/pickup?token=${token}`, signedParams);
        if (!response.data.success) {
            throw new Error(`Create pickup order failed: ${response.data.message}`);
        }
        return response.data.data;
    }
    async createDropoffOrder(params) {
        const token = await this.getToken();
        // 送机订单需要DES加密
        const encryptedParams = this.crypto.desEncrypt(JSON.stringify(params));
        const signedParams = this.createSignedParams({ encryptBody: encryptedParams });
        const response = await this.httpClient.post(`/otaorder-api/common/v1.0/order/transfer?token=${token}`, signedParams);
        if (!response.data.success) {
            throw new Error(`Create dropoff order failed: ${response.data.message}`);
        }
        return response.data.data;
    }
    async createCharterOrder(params) {
        const token = await this.getToken();
        // 包车订单需要DES加密
        const encryptedParams = this.crypto.desEncrypt(JSON.stringify(params));
        const signedParams = this.createSignedParams({ encryptBody: encryptedParams });
        const response = await this.httpClient.post(`/otaorder-api/common/v1.0/order/car?token=${token}`, signedParams);
        if (!response.data.success) {
            throw new Error(`Create charter order failed: ${response.data.message}`);
        }
        return response.data.data;
    }
    async getOrderList(params) {
        const token = await this.getToken();
        const signedParams = this.createSignedParams({
            limit: params.limit,
            offset: params.offset,
            listType: params.listType,
            userId: params.userId,
        });
        const response = await this.httpClient.post(`/otaorder-api/common/v1.0/order/orderList?token=${token}`, signedParams);
        if (!response.data.success) {
            throw new Error(`Get order list failed: ${response.data.message}`);
        }
        return response.data.data;
    }
    async getOrderDetail(params) {
        const token = await this.getToken();
        const requestBody = {};
        // 添加订单号参数（二选一）
        if (params.orderTravelNo) {
            requestBody.orderTravelNo = params.orderTravelNo;
        }
        if (params.orderNoThird) {
            requestBody.orderNoThird = params.orderNoThird;
        }
        const signedParams = this.createSignedParams(requestBody);
        const response = await this.httpClient.post(`/otaorder-api/common/v1.0/order/orderDetail?token=${token}`, signedParams);
        if (!response.data.success) {
            throw new Error(`Get order detail failed: ${response.data.message}`);
        }
        return response.data.data;
    }
    async cancelOrder(params) {
        const token = await this.getToken();
        const requestBody = {
            orderTravelNo: params.orderTravelNo,
            cancelReason: params.cancelReason,
            cancelType: params.cancelType,
        };
        // 添加可选参数
        if (params.cancelRemark) {
            requestBody.cancelRemark = params.cancelRemark;
        }
        if (params.actuallyPaidPrice) {
            requestBody.actuallyPaidPrice = params.actuallyPaidPrice;
        }
        const signedParams = this.createSignedParams(requestBody);
        const response = await this.httpClient.post(`/otaorder-api/common/v1.0/order/cancel?token=${token}`, signedParams);
        if (!response.data.success) {
            throw new Error(`Cancel order failed: ${response.data.message}`);
        }
        return response.data.data;
    }
}
//# sourceMappingURL=client.js.map