{"version": 3, "file": "client.d.ts", "sourceRoot": "", "sources": ["../../src/api/client.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AAGlD,OAAO,EAEL,QAAQ,EACR,WAAW,EACX,WAAW,EACZ,MAAM,+BAA+B,CAAC;AAKvC,OAAO,EAEL,iBAAiB,EAClB,MAAM,8BAA8B,CAAC;AACtC,OAAO,EACL,aAAa,EACb,iBAAiB,EAClB,MAAM,8BAA8B,CAAC;AAEtC,qBAAa,eAAe;IAC1B,OAAO,CAAC,MAAM,CAAe;IAC7B,OAAO,CAAC,UAAU,CAAgB;IAClC,OAAO,CAAC,MAAM,CAAc;IAC5B,OAAO,CAAC,KAAK,CAAY;gBAEb,MAAM,EAAE,YAAY;IAwBhC,OAAO,CAAC,iBAAiB;IAsCzB,OAAO,CAAC,iBAAiB;IAIzB,OAAO,CAAC,kBAAkB;IAapB,QAAQ,IAAI,OAAO,CAAC,MAAM,CAAC;IA0C3B,SAAS,CAAC,MAAM,GAAE;QAAE,OAAO,CAAC,EAAE,MAAM,CAAA;KAAO,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;IAejE,WAAW,CAAC,MAAM,GAAE;QAAE,OAAO,CAAC,EAAE,MAAM,CAAA;KAAO,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;IAetE,eAAe,CAAC,MAAM,GAAE;QAAE,SAAS,CAAC,EAAE,MAAM,CAAA;KAAO,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;IAgB5E,cAAc,CAAC,UAAU,EAAE,GAAG,GAAG,OAAO,CAAC,iBAAiB,CAAC;IAgC3D,eAAe,CAAC,UAAU,EAAE,GAAG,GAAG,OAAO,CAAC,iBAAiB,CAAC;IAgC5D,eAAe,CAAC,UAAU,EAAE,GAAG,GAAG,OAAO,CAAC,iBAAiB,CAAC;IA2C5D,iBAAiB,CAAC,UAAU,EAAE,GAAG,GAAG,OAAO,CAAC,aAAa,CAAC;IAiB1D,kBAAkB,CAAC,UAAU,EAAE,GAAG,GAAG,OAAO,CAAC,aAAa,CAAC;IAiB3D,kBAAkB,CAAC,UAAU,EAAE,GAAG,GAAG,OAAO,CAAC,aAAa,CAAC;IAiB3D,YAAY,CAAC,UAAU,EAAE,GAAG,GAAG,OAAO,CAAC,iBAAiB,CAAC;IAoBzD,cAAc,CAAC,UAAU,EAAE,GAAG,GAAG,OAAO,CAAC,aAAa,CAAC;IAyBvD,WAAW,CAAC,UAAU,EAAE,GAAG,GAAG,OAAO,CAAC;QAAE,OAAO,EAAE,OAAO,CAAC;QAAC,OAAO,CAAC,EAAE,MAAM,CAAA;KAAE,CAAC;CA4BpF"}