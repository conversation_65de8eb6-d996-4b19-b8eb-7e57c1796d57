{"version": 3, "file": "client.d.ts", "sourceRoot": "", "sources": ["../../src/api/client.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AAGlD,OAAO,EAEL,QAAQ,EACR,WAAW,EACX,WAAW,EACZ,MAAM,+BAA+B,CAAC;AAKvC,OAAO,EACL,kBAAkB,EAClB,mBAAmB,EACnB,mBAAmB,EAEnB,iBAAiB,EAClB,MAAM,8BAA8B,CAAC;AACtC,OAAO,EACL,kBAAkB,EAClB,mBAAmB,EACnB,mBAAmB,EACnB,aAAa,EACb,gBAAgB,EAChB,iBAAiB,EACjB,kBAAkB,EAClB,kBAAkB,EACnB,MAAM,8BAA8B,CAAC;AAEtC,qBAAa,eAAe;IAC1B,OAAO,CAAC,MAAM,CAAe;IAC7B,OAAO,CAAC,UAAU,CAAgB;IAClC,OAAO,CAAC,MAAM,CAAc;IAC5B,OAAO,CAAC,KAAK,CAAY;gBAEb,MAAM,EAAE,YAAY;IAwBhC,OAAO,CAAC,iBAAiB;IAsCzB,OAAO,CAAC,iBAAiB;IAIzB,OAAO,CAAC,kBAAkB;IAapB,QAAQ,IAAI,OAAO,CAAC,MAAM,CAAC;IA0C3B,SAAS,CAAC,MAAM,GAAE;QAAE,OAAO,CAAC,EAAE,MAAM,CAAA;KAAO,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;IAejE,WAAW,CAAC,MAAM,GAAE;QAAE,OAAO,CAAC,EAAE,MAAM,CAAA;KAAO,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;IAetE,eAAe,CAAC,MAAM,GAAE;QAAE,SAAS,CAAC,EAAE,MAAM,CAAA;KAAO,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;IAgB5E,cAAc,CAAC,MAAM,EAAE,kBAAkB,GAAG,OAAO,CAAC,iBAAiB,CAAC;IAetE,eAAe,CAAC,MAAM,EAAE,mBAAmB,GAAG,OAAO,CAAC,iBAAiB,CAAC;IAexE,eAAe,CAAC,MAAM,EAAE,mBAAmB,GAAG,OAAO,CAAC,iBAAiB,CAAC;IA2CxE,iBAAiB,CAAC,MAAM,EAAE,kBAAkB,GAAG,OAAO,CAAC,aAAa,CAAC;IAiBrE,kBAAkB,CAAC,MAAM,EAAE,mBAAmB,GAAG,OAAO,CAAC,aAAa,CAAC;IAiBvE,kBAAkB,CAAC,MAAM,EAAE,mBAAmB,GAAG,OAAO,CAAC,aAAa,CAAC;IAiBvE,YAAY,CAAC,MAAM,GAAE,gBAAqB,GAAG,OAAO,CAAC,iBAAiB,CAAC;IAqBvE,cAAc,CAAC,MAAM,EAAE,kBAAkB,GAAG,OAAO,CAAC,aAAa,CAAC;IAiBlE,WAAW,CAAC,MAAM,EAAE,kBAAkB,GAAG,OAAO,CAAC;QAAE,OAAO,EAAE,OAAO,CAAC;QAAC,OAAO,CAAC,EAAE,MAAM,CAAA;KAAE,CAAC;CAkB/F"}