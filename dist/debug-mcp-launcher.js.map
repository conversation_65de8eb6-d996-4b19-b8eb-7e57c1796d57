{"version": 3, "file": "debug-mcp-launcher.js", "sourceRoot": "", "sources": ["../src/debug-mcp-launcher.ts"], "names": [], "mappings": ";AAEA;;GAEG;AAEH,OAAO,EAAE,MAAM,IAAI,CAAC;AACpB,OAAO,IAAI,MAAM,MAAM,CAAC;AACxB,OAAO,EAAU,cAAc,EAAE,MAAM,mBAAmB,CAAC;AAC3D,OAAO,EAAE,eAAe,EAAE,MAAM,wBAAwB,CAAC;AAEzD,SAAS;AACT,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,MAAM,CAAC,CAAC;AAChD,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;IAC3B,EAAE,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;AAC5C,CAAC;AAED,SAAS;AACT,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,aAAa,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC;AAC5F,MAAM,SAAS,GAAG,EAAE,CAAC,iBAAiB,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;AAEhE,OAAO;AACP,SAAS,QAAQ,CAAC,KAAa,EAAE,OAAe,EAAE,IAAU;IAC1D,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;IAC3C,MAAM,QAAQ,GAAG;QACf,SAAS;QACT,KAAK;QACL,OAAO;QACP,IAAI;QACJ,GAAG,EAAE,OAAO,CAAC,GAAG;KACjB,CAAC;IAEF,OAAO;IACP,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IAEjD,qBAAqB;IACrB,OAAO,CAAC,KAAK,CAAC,IAAI,SAAS,KAAK,KAAK,KAAK,OAAO,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;AACzF,CAAC;AAED,SAAS;AACT,OAAO,CAAC,KAAK,GAAG,kBAAkB,CAAC;AAEnC,SAAS;AACT,QAAQ,CAAC,MAAM,EAAE,yCAAyC,CAAC,CAAC;AAC5D,QAAQ,CAAC,MAAM,EAAE,iBAAiB,EAAE,EAAE,OAAO,EAAE,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;AAClE,QAAQ,CAAC,MAAM,EAAE,mBAAmB,EAAE,EAAE,GAAG,EAAE,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;AAC9D,QAAQ,CAAC,MAAM,EAAE,UAAU,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;AAEhD,WAAW;AACX,OAAO,CAAC,EAAE,CAAC,mBAAmB,EAAE,CAAC,KAAK,EAAE,EAAE;IACxC,QAAQ,CAAC,OAAO,EAAE,oBAAoB,EAAE;QACtC,OAAO,EAAE,KAAK,CAAC,OAAO;QACtB,KAAK,EAAE,KAAK,CAAC,KAAK;KACnB,CAAC,CAAC;IACH,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC,CAAC;AAEH,OAAO,CAAC,EAAE,CAAC,oBAAoB,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE;IACnD,QAAQ,CAAC,OAAO,EAAE,qBAAqB,EAAE;QACvC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC;QACtB,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC;KACzB,CAAC,CAAC;IACH,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC,CAAC;AAEH,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;IACxB,QAAQ,CAAC,MAAM,EAAE,gCAAgC,CAAC,CAAC;IACnD,SAAS,CAAC,GAAG,EAAE,CAAC;IAChB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC,CAAC;AAEH,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE;IACzB,QAAQ,CAAC,MAAM,EAAE,iCAAiC,CAAC,CAAC;IACpD,SAAS,CAAC,GAAG,EAAE,CAAC;IAChB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC,CAAC;AAEH,mBAAmB;AACnB,IAAI,YAAY,GAAG,CAAC,CAAC;AACrB,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;IAChC,YAAY,EAAE,CAAC;IACf,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC;IACvC,QAAQ,CAAC,OAAO,EAAE,sBAAsB,EAAE;QACxC,SAAS,EAAE,YAAY;QACvB,MAAM,EAAE,OAAO,CAAC,MAAM;QACtB,OAAO,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,YAAY;KAChD,CAAC,CAAC;IAEH,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACnC,QAAQ,CAAC,OAAO,EAAE,oBAAoB,EAAE;YACtC,SAAS,EAAE,YAAY;YACvB,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,EAAE,EAAE,MAAM,CAAC,EAAE;YACb,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC,MAAM;SAC3B,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,QAAQ,CAAC,MAAM,EAAE,6BAA6B,EAAE;YAC9C,SAAS,EAAE,YAAY;YACvB,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;SAC9D,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,oBAAoB;AACpB,MAAM,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC;AAC3C,OAAO,CAAC,MAAM,CAAC,KAAK,GAAG,UAAS,KAAU,EAAE,QAAc,EAAE,QAAc;IACxE,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC9B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACjC,QAAQ,CAAC,OAAO,EAAE,sBAAsB,EAAE;gBACxC,EAAE,EAAE,MAAM,CAAC,EAAE;gBACb,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC,MAAM;gBAC1B,QAAQ,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK;gBACxB,MAAM,EAAE,MAAM,CAAC,MAAM;aACtB,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,QAAQ,CAAC,OAAO,EAAE,2BAA2B,EAAE;gBAC7C,MAAM,EAAE,KAAK,CAAC,MAAM;gBACpB,OAAO,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC;aACjC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED,OAAO,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;AAC7D,CAAC,CAAC;AAEF,KAAK,UAAU,IAAI;IACjB,IAAI,CAAC;QACH,QAAQ,CAAC,MAAM,EAAE,0BAA0B,CAAC,CAAC;QAE7C,kBAAkB;QAClB,QAAQ,CAAC,OAAO,EAAE,uBAAuB,EAAE;YACzC,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ;YAC9B,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS;YAChC,eAAe,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe;YAC5C,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;YACtD,iBAAiB,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;SACrE,CAAC,CAAC;QAEH,cAAc,EAAE,CAAC;QACjB,QAAQ,CAAC,MAAM,EAAE,sCAAsC,CAAC,CAAC;QAEzD,QAAQ,CAAC,MAAM,EAAE,8BAA8B,CAAC,CAAC;QACjD,MAAM,MAAM,GAAG,IAAI,eAAe,EAAE,CAAC;QAErC,QAAQ,CAAC,MAAM,EAAE,mCAAmC,CAAC,CAAC;QACtD,MAAM,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAE5B,QAAQ,CAAC,MAAM,EAAE,wCAAwC,CAAC,CAAC;IAE7D,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,QAAQ,CAAC,OAAO,EAAE,mCAAmC,EAAE;YACrD,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;YAC/D,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;SACxD,CAAC,CAAC;QACH,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;AACH,CAAC;AAED,QAAQ;AACR,IAAI,EAAE,CAAC"}