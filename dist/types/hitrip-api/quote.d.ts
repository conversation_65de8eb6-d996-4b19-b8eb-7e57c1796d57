import { AddressInfo } from './common.js';
export interface BaseQuoteRequest {
    vehicleTypeId: number;
    guestNum?: number;
    luggageNum?: number;
}
export interface PickupQuoteRequest extends BaseQuoteRequest {
    flightNo: string;
    flightDate: string;
    airportCode: string;
    destination: AddressInfo;
}
export interface DropoffQuoteRequest extends BaseQuoteRequest {
    flightNo: string;
    flightDate: string;
    airportCode: string;
    origin: AddressInfo;
}
export interface CharterQuoteRequest extends BaseQuoteRequest {
    serviceDate: string;
    origin: AddressInfo;
    destination: AddressInfo;
    serviceHours?: number;
}
export interface QuoteResponse {
    quoteId: string;
    vehicleTypeId: number;
    vehicleTypeName: string;
    totalPrice: number;
    currency: string;
    priceDetails: PriceDetail[];
    validUntil: string;
    estimatedDuration?: number;
    estimatedDistance?: number;
    remarks?: string;
}
export interface PriceDetail {
    itemName: string;
    itemType: 'base' | 'extra' | 'tax' | 'fee';
    amount: number;
    currency: string;
    description?: string;
}
export interface QuoteListResponse {
    quotes: QuoteResponse[];
    totalCount: number;
    recommendedQuoteId?: string;
}
//# sourceMappingURL=quote.d.ts.map