import { AddressInfo } from './common.js';
export interface BaseQuoteRequest {
    vehicleTypeId: number;
    guestNum?: number;
    luggageNum?: number;
}
export interface PickupQuoteRequest {
    airportCode: string;
    airportName: string;
    serviceTime: string;
    serviceCityId: number;
    serviceCityName: string;
    startAddress: string;
    startDetailAddress: string;
    startLocation: string;
    endAddress: string;
    endDetailAddress: string;
    endLocation: string;
    carModelId?: string;
}
export interface DropoffQuoteRequest {
    airportCode: string;
    airportName: string;
    serviceTime: string;
    serviceCityId: number;
    serviceCityName: string;
    startAddress: string;
    startDetailAddress: string;
    startLocation: string;
    endAddress: string;
    endDetailAddress: string;
    endLocation: string;
    carModelId?: string;
}
export interface CharterQuoteRequest extends BaseQuoteRequest {
    serviceTime: string;
    origin: CharterAddressInfo;
    destination: CharterAddressInfo;
    serviceHours?: number;
    tourType?: number;
    distance?: number;
    duration?: number;
}
export interface CharterAddressInfo extends AddressInfo {
    cityId?: number;
    cityName?: string;
}
export interface QuoteResponse {
    quoteId: string;
    vehicleTypeId: number;
    vehicleTypeName: string;
    totalPrice: number;
    currency: string;
    priceDetails: PriceDetail[];
    validUntil: string;
    estimatedDuration?: number;
    estimatedDistance?: number;
    remarks?: string;
}
export interface PriceDetail {
    itemName: string;
    itemType: 'base' | 'extra' | 'tax' | 'fee';
    amount: number;
    currency: string;
    description?: string;
}
export interface QuoteListResponse {
    quotes: QuoteResponse[];
    totalCount: number;
    recommendedQuoteId?: string;
}
//# sourceMappingURL=quote.d.ts.map