import { AddressInfo } from './common.js';
export declare enum OrderStatus {
    PENDING = "pending",// 待确认
    CONFIRMED = "confirmed",// 已确认
    ASSIGNED = "assigned",// 已派单
    IN_PROGRESS = "in_progress",// 进行中
    COMPLETED = "completed",// 已完成
    CANCELLED = "cancelled",// 已取消
    REFUNDED = "refunded"
}
export declare enum OrderType {
    PICKUP = "pickup",// 接机
    DROPOFF = "dropoff",// 送机
    CHARTER = "charter"
}
export interface BaseOrderRequest {
    vehicleTypeId: number;
    contactName: string;
    contactPhone: string;
    guestNum?: number;
    luggageNum?: number;
    remark?: string;
    specialRequirements?: string;
}
export interface PickupOrderRequest extends BaseOrderRequest {
    flightNo: string;
    flightDate: string;
    airportCode: string;
    destination: AddressInfo;
}
export interface DropoffOrderRequest extends BaseOrderRequest {
    flightNo: string;
    flightDate: string;
    airportCode: string;
    origin: AddressInfo;
}
export interface CharterOrderRequest extends BaseOrderRequest {
    serviceDate: string;
    origin: AddressInfo;
    destination: AddressInfo;
    serviceHours?: number;
}
export interface OrderResponse {
    orderId: string;
    orderType: OrderType;
    orderStatus: OrderStatus;
    vehicleTypeId: number;
    vehicleTypeName: string;
    contactName: string;
    contactPhone: string;
    guestNum: number;
    luggageNum?: number;
    serviceDate: string;
    flightNo?: string;
    flightDate?: string;
    airportCode?: string;
    origin?: AddressInfo;
    destination?: AddressInfo;
    serviceHours?: number;
    totalPrice: number;
    currency: string;
    driver?: DriverInfo;
    vehicle?: VehicleInfo;
    createdAt: string;
    updatedAt: string;
    confirmedAt?: string;
    completedAt?: string;
    cancelledAt?: string;
    remark?: string;
    specialRequirements?: string;
    cancelReason?: string;
}
export interface DriverInfo {
    driverId: string;
    driverName: string;
    driverPhone: string;
    driverPhoto?: string;
    rating?: number;
    vehiclePlateNumber?: string;
}
export interface VehicleInfo {
    vehicleId: string;
    plateNumber: string;
    brand: string;
    model: string;
    color: string;
    year?: number;
    photos?: string[];
}
export interface OrderListRequest {
    pageNum?: number;
    pageSize?: number;
    orderStatus?: OrderStatus;
    orderType?: OrderType;
    startDate?: string;
    endDate?: string;
}
export interface OrderListResponse {
    orders: OrderResponse[];
    totalCount: number;
    pageNum: number;
    pageSize: number;
    totalPages: number;
}
export interface CancelOrderRequest {
    orderId: string;
    cancelReason: string;
}
export interface OrderDetailRequest {
    orderId: string;
}
//# sourceMappingURL=order.d.ts.map