import { AddressInfo } from './common.js';
export declare enum OrderStatus {
    PENDING = "pending",// 待确认
    CONFIRMED = "confirmed",// 已确认
    ASSIGNED = "assigned",// 已派单
    IN_PROGRESS = "in_progress",// 进行中
    COMPLETED = "completed",// 已完成
    CANCELLED = "cancelled",// 已取消
    REFUNDED = "refunded"
}
export declare enum OrderType {
    PICKUP = "pickup",// 接机
    DROPOFF = "dropoff",// 送机
    CHARTER = "charter"
}
export interface BaseOrderRequest {
    vehicleTypeId: number;
    contactName: string;
    contactPhone: string;
    guestNum?: number;
    luggageNum?: number;
    remark?: string;
    specialRequirements?: string;
}
export interface PickupOrderRequest {
    flightNo: string;
    flightDestCode: string;
    flightDestAirportName: string;
    flightDestBuilding?: string;
    flightArriveTime?: string;
    flightFlyTime?: string;
    flightAirportCode?: string;
    flightAirportName?: string;
    orderThirdNo: string;
    priceActual: number;
    priceChannel: number;
    serviceCarModel: string;
    serviceCarModelName: string;
    serviceCityId: number;
    serviceCityName: string;
    serviceTime: string;
    serviceStartAddress: string;
    serviceStartAddressDetail: string;
    serviceStartPoint: string;
    serviceDestAddress: string;
    serviceDestAddressDetail: string;
    serviceDestPoint: string;
    servicePassagerName: string;
    servicePassagerMobile: string;
    servicePassagerAreacode: string;
    orderChannel: string;
    orderChannelName: string;
    airportPriceQuestParam: {
        airportCode: string;
        airportName: string;
        channelId: number;
        endAddress: string;
        endDetailAddress: string;
        endLocation: string;
        serviceCityId: number;
        serviceCityName: string;
        serviceTime: string;
        startAddress: string;
        startCityId: number;
        startCityName: string;
        startDetailAddress: string;
        startLocation: string;
    };
    userRemark?: string;
    serviceStartPoi?: string;
    serviceDestPoi?: string;
    serviceEndCityId?: number;
    serviceEndCityName?: string;
    serviceContinentId?: number;
    serviceContinentName?: string;
    serviceCountryId?: number;
    serviceCountryName?: string;
}
export interface DropoffOrderRequest {
    flightNo?: string;
    flightDestCode: string;
    flightDestAirportName: string;
    flightDestBuilding?: string;
    flightArriveTime?: string;
    flightFlyTime?: string;
    flightAirportCode?: string;
    flightAirportName?: string;
    orderNo: string;
    orderThirdNo: string;
    priceActual: number;
    priceChannel: number;
    serviceCarModel: string;
    serviceCarModelName: string;
    serviceCityId: number;
    serviceCityName: string;
    serviceTime: string;
    serviceStartAddress: string;
    serviceStartAddressDetail: string;
    serviceStartPoint: string;
    serviceDestAddress: string;
    serviceDestAddressDetail: string;
    serviceDestPoint: string;
    servicePassagerName: string;
    servicePassagerMobile: string;
    servicePassagerAreacode: string;
    orderChannel: string;
    orderChannelName: string;
    airportPriceQuestParam: {
        airportCode: string;
        airportName: string;
        channelId: number;
        endAddress: string;
        endDetailAddress: string;
        endLocation: string;
        serviceCityId: number;
        serviceCityName: string;
        serviceTime: string;
        startAddress: string;
        startCityId: number;
        startCityName: string;
        startDetailAddress: string;
        startLocation: string;
    };
    userRemark?: string;
    serviceStartPoi?: string;
    serviceDestPoi?: string;
    serviceEndCityId?: number;
    serviceEndCityName?: string;
    serviceContinentId?: number;
    serviceContinentName?: string;
    serviceCountryId?: number;
    serviceCountryName?: string;
}
export interface CharterOrderRequest {
    orderNoThird: string;
    travelStartTimeLocal: string;
    travelEndTimeLocal: string;
    travelStartTimeBeijing: string;
    travelEndTimeBeijing: string;
    totalDay: number;
    startCityId: number;
    startCityName?: string;
    endCityId: number;
    endCityName?: string;
    amountChannelSys: number;
    amountShouldPay: number;
    amountShouldReceive: number;
    amountActualPay: number;
    amountActualReceive: number;
    amountCouponTotal: number;
    customerName: string;
    customerAreaCode: string;
    customerMobile: string;
    customerWechat?: string;
    payTime: string;
    thirdPayNo?: string;
    priceMarkCity: number;
    remark?: string;
    travelDailyBaseReq: {
        dayIndex: number;
        goodsType: number;
        goodsTypeName: string;
        serviceStartTimeLocal: string;
        serviceEndTimeLocal: string;
        serviceStartTimeBeijing: string;
        serviceEndTimeBeijing: string;
        carModelId: number;
        carModelName: string;
        startCityId: number;
        startCityName: string;
        endCityId: number;
        endCityName: string;
        serviceMaxTime: number;
        serviceMaxDistance: number;
    };
}
export interface OrderResponse {
    orderId: string;
    orderType: OrderType;
    orderStatus: OrderStatus;
    vehicleTypeId: number;
    vehicleTypeName: string;
    contactName: string;
    contactPhone: string;
    guestNum: number;
    luggageNum?: number;
    serviceDate: string;
    flightNo?: string;
    flightDate?: string;
    airportCode?: string;
    origin?: AddressInfo;
    destination?: AddressInfo;
    serviceHours?: number;
    totalPrice: number;
    currency: string;
    driver?: DriverInfo;
    vehicle?: VehicleInfo;
    createdAt: string;
    updatedAt: string;
    confirmedAt?: string;
    completedAt?: string;
    cancelledAt?: string;
    remark?: string;
    specialRequirements?: string;
    cancelReason?: string;
}
export interface DriverInfo {
    driverId: string;
    driverName: string;
    driverPhone: string;
    driverPhoto?: string;
    rating?: number;
    vehiclePlateNumber?: string;
}
export interface VehicleInfo {
    vehicleId: string;
    plateNumber: string;
    brand: string;
    model: string;
    color: string;
    year?: number;
    photos?: string[];
}
export interface OrderListRequest {
    limit: number;
    offset: number;
    listType: number;
    userId: string;
}
export interface OrderListResponse {
    orders: OrderResponse[];
    totalCount: number;
    pageNum: number;
    pageSize: number;
    totalPages: number;
}
export interface CancelOrderRequest {
    orderTravelNo: string;
    cancelReason: string;
    cancelType: number;
    cancelRemark?: string;
    actuallyPaidPrice?: number;
}
export interface OrderDetailRequest {
    orderTravelNo?: string;
    orderNoThird?: string;
}
//# sourceMappingURL=order.d.ts.map