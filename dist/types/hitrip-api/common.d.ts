export interface HitripApiResponse<T = any> {
    status: number;
    success: boolean;
    data: T;
    message?: string;
    traceId?: string;
}
export interface CityInfo {
    cityId: number;
    cityName: string;
    countryId: number;
    countryName: string;
    pinyin?: string;
    pinyinShort?: string;
}
export interface AirportInfo {
    airportCode: string;
    airportName: string;
    cityId: number;
    cityName: string;
    countryId: number;
    countryName: string;
}
export interface VehicleType {
    id: number;
    vehicleTypeName: string;
    vehicleTypeFullName: string;
    vehicleTypeRank: number;
    vehicleTypeRankName: string;
    seatNum: number;
    guestNum: number;
    luggageNum: number;
    pics?: string;
    countryVos: Array<{
        countryId: number;
        countryName: string;
    }>;
}
export interface AddressInfo {
    address: string;
    addressDetail?: string;
    location?: string;
    poiId?: string;
}
export interface CommonRequestParams {
    cid: string;
    timestamp: string;
    sign: string;
}
//# sourceMappingURL=common.d.ts.map