export interface HitripConfig {
    baseUrl: string;
    cid: string;
    secretKey: string;
    environment: 'development' | 'test' | 'production';
    serverMode: 'stdio' | 'sse';
    port?: number;
    cache: {
        enabled: boolean;
        tokenTtl: number;
    };
    logging: {
        level: 'error' | 'warn' | 'info' | 'debug';
        file?: string;
    };
    webhook?: {
        domain: string;
        endpoints: {
            confirmOrder?: string;
            pushDriver?: string;
            orderComplete?: string;
        };
    };
}
//# sourceMappingURL=config.d.ts.map