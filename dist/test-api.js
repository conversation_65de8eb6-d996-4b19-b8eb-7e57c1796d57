#!/usr/bin/env node
/**
 * 测试脚本：验证黄包车API连接和基础功能
 */
import { HitripApiClient } from './api/client.js';
import { CryptoUtils } from './utils/crypto.js';
// 测试配置
const testConfig = {
    baseUrl: 'https://api-gw.test.huangbaoche.com/',
    cid: '1086910840',
    secretKey: '12345678',
    environment: 'test',
    serverMode: 'stdio',
    cache: {
        enabled: true,
        tokenTtl: 3600,
    },
    logging: {
        level: 'info',
    },
};
async function testCrypto() {
    console.log('\n=== 测试加密功能 ===');
    const crypto = new CryptoUtils(testConfig.secretKey);
    // 测试签名生成
    const params = {
        cid: testConfig.cid,
        timestamp: '2020-01-08 10:57:00',
        flightNo: 'CA181',
        flightDate: '2020-04-16',
    };
    const signature = crypto.generateSignature(params);
    console.log('生成的签名:', signature);
    // 测试加密解密
    const testData = '{"test": "data"}';
    const encrypted = crypto.desEncrypt(testData);
    const decrypted = crypto.desDecrypt(encrypted);
    console.log('原始数据:', testData);
    console.log('加密后:', encrypted);
    console.log('解密后:', decrypted);
    console.log('加密解密测试:', decrypted === testData ? '✓ 通过' : '✗ 失败');
}
async function testApiConnection() {
    console.log('\n=== 测试API连接 ===');
    const client = new HitripApiClient(testConfig);
    try {
        // 测试获取Token
        console.log('正在获取Token...');
        const token = await client.getToken();
        console.log('Token获取成功:', token ? '✓' : '✗');
        // 测试获取城市列表
        console.log('正在获取城市列表...');
        const cities = await client.getCities();
        console.log(`城市列表获取成功: ${cities.length} 个城市`);
        if (cities.length > 0) {
            console.log('示例城市:', cities[0]);
        }
        // 测试获取机场列表
        console.log('正在获取机场列表...');
        const airports = await client.getAirports();
        console.log(`机场列表获取成功: ${airports.length} 个机场`);
        if (airports.length > 0) {
            console.log('示例机场:', airports[0]);
        }
        // 测试获取车型列表
        console.log('正在获取车型列表...');
        const vehicleTypes = await client.getVehicleTypes();
        console.log(`车型列表获取成功: ${vehicleTypes.length} 个车型`);
        if (vehicleTypes.length > 0) {
            console.log('示例车型:', vehicleTypes[0]);
        }
    }
    catch (error) {
        console.error('API测试失败:', error);
    }
}
async function testQuoteApi() {
    console.log('\n=== 测试报价API ===');
    const client = new HitripApiClient(testConfig);
    try {
        // 测试接机报价
        const pickupQuoteParams = {
            flightNo: 'CA181',
            flightDate: '2024-12-25',
            airportCode: 'PEK',
            destination: {
                address: '北京市朝阳区国贸CBD',
            },
            vehicleTypeId: 1,
            guestNum: 2,
            luggageNum: 2,
        };
        console.log('正在测试接机报价...');
        const pickupQuote = await client.getPickupQuote(pickupQuoteParams);
        console.log('接机报价测试:', pickupQuote ? '✓ 成功' : '✗ 失败');
    }
    catch (error) {
        console.log('报价API测试 (预期可能失败):', error instanceof Error ? error.message : String(error));
    }
}
async function main() {
    console.log('🚗 黄包车 MCP Server API 测试');
    console.log('================================');
    try {
        await testCrypto();
        await testApiConnection();
        await testQuoteApi();
        console.log('\n=== 测试完成 ===');
        console.log('✓ 基础功能测试完成');
    }
    catch (error) {
        console.error('测试过程中发生错误:', error);
        process.exit(1);
    }
}
// 运行测试
main();
//# sourceMappingURL=test-api.js.map