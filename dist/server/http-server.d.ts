import { HitripConfig } from '../types/config.js';
export declare class HitripHttpServer {
    private app;
    private server;
    private wss;
    private apiClient;
    private tools;
    private port;
    constructor(config: HitripConfig, port?: number);
    private setupMiddleware;
    private setupRoutes;
    private setupWebSocket;
    private handleMcpRequest;
    private callTool;
    start(): Promise<void>;
    stop(): Promise<void>;
}
//# sourceMappingURL=http-server.d.ts.map