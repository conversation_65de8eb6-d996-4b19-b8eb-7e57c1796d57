import express from 'express';
import cors from 'cors';
import { createServer } from 'http';
import { WebSocketServer } from 'ws';
import { HitripApiClient } from '../api/client.js';
import { createHitripTools } from '../tools/index.js';
export class HitripHttpServer {
    app;
    server;
    wss = null;
    apiClient;
    tools;
    port;
    constructor(config, port = 3000) {
        this.port = port;
        this.app = express();
        this.apiClient = new HitripApiClient(config);
        this.tools = createHitripTools(this.apiClient);
        this.setupMiddleware();
        this.setupRoutes();
        this.server = createServer(this.app);
        this.setupWebSocket();
    }
    setupMiddleware() {
        // 启用 CORS
        this.app.use(cors({
            origin: '*',
            methods: ['GET', 'POST', 'OPTIONS'],
            allowedHeaders: ['Content-Type', 'Authorization']
        }));
        // 解析 JSON
        this.app.use(express.json({ limit: '10mb' }));
        // 请求日志
        this.app.use((req, res, next) => {
            console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
            next();
        });
    }
    setupRoutes() {
        // 健康检查
        this.app.get('/health', (req, res) => {
            res.json({
                status: 'healthy',
                timestamp: new Date().toISOString(),
                service: 'Hitrip MCP Server'
            });
        });
        // MCP 协议端点 - HTTP POST
        this.app.post('/mcp', async (req, res) => {
            try {
                const request = req.body;
                console.log('收到 MCP 请求:', JSON.stringify(request, null, 2));
                const response = await this.handleMcpRequest(request);
                console.log('发送 MCP 响应:', JSON.stringify(response, null, 2));
                res.json(response);
            }
            catch (error) {
                console.error('MCP 请求处理错误:', error);
                res.status(500).json({
                    jsonrpc: '2.0',
                    id: req.body?.id || null,
                    error: {
                        code: -32603,
                        message: 'Internal error',
                        data: error instanceof Error ? error.message : String(error)
                    }
                });
            }
        });
        // 获取工具列表 - 便捷端点
        this.app.get('/tools', async (req, res) => {
            try {
                const request = {
                    jsonrpc: '2.0',
                    id: 1,
                    method: 'tools/list',
                    params: {}
                };
                const response = await this.handleMcpRequest(request);
                res.json(response);
            }
            catch (error) {
                console.error('获取工具列表错误:', error);
                res.status(500).json({ error: 'Failed to get tools list' });
            }
        });
        // 调用工具 - 便捷端点
        this.app.post('/tools/:toolName', async (req, res) => {
            try {
                const { toolName } = req.params;
                const arguments_ = req.body;
                const request = {
                    jsonrpc: '2.0',
                    id: Date.now(),
                    method: 'tools/call',
                    params: {
                        name: toolName,
                        arguments: arguments_
                    }
                };
                const response = await this.handleMcpRequest(request);
                res.json(response);
            }
            catch (error) {
                console.error(`调用工具 ${req.params.toolName} 错误:`, error);
                res.status(500).json({ error: 'Failed to call tool' });
            }
        });
        // 根路径 - 服务信息
        this.app.get('/', (req, res) => {
            res.json({
                name: 'Hitrip MCP Server',
                version: '1.0.0',
                description: '黄包车 MCP 服务器 - 提供接机、送机、包车等服务的 API 工具',
                endpoints: {
                    health: 'GET /health',
                    mcp: 'POST /mcp',
                    tools: 'GET /tools',
                    callTool: 'POST /tools/:toolName',
                    websocket: 'WS /ws'
                },
                documentation: 'https://github.com/your-repo/hitrip-mcp-server'
            });
        });
    }
    setupWebSocket() {
        this.wss = new WebSocketServer({ server: this.server, path: '/ws' });
        this.wss.on('connection', (ws, req) => {
            console.log(`WebSocket 连接建立: ${req.socket.remoteAddress}`);
            ws.on('message', async (data) => {
                try {
                    const request = JSON.parse(data.toString());
                    console.log('收到 WebSocket MCP 请求:', JSON.stringify(request, null, 2));
                    const response = await this.handleMcpRequest(request);
                    console.log('发送 WebSocket MCP 响应:', JSON.stringify(response, null, 2));
                    ws.send(JSON.stringify(response));
                }
                catch (error) {
                    console.error('WebSocket MCP 请求处理错误:', error);
                    const errorResponse = {
                        jsonrpc: '2.0',
                        id: null,
                        error: {
                            code: -32603,
                            message: 'Internal error',
                            data: error instanceof Error ? error.message : String(error)
                        }
                    };
                    ws.send(JSON.stringify(errorResponse));
                }
            });
            ws.on('close', () => {
                console.log('WebSocket 连接关闭');
            });
            ws.on('error', (error) => {
                console.error('WebSocket 错误:', error);
            });
        });
    }
    async handleMcpRequest(request) {
        const { method, params, id } = request;
        try {
            switch (method) {
                case 'tools/list':
                    return {
                        jsonrpc: '2.0',
                        id,
                        result: {
                            tools: this.tools
                        }
                    };
                case 'tools/call':
                    const { name, arguments: args } = params;
                    const result = await this.callTool(name, args);
                    return {
                        jsonrpc: '2.0',
                        id,
                        result: {
                            content: [
                                {
                                    type: 'text',
                                    text: typeof result === 'string' ? result : JSON.stringify(result, null, 2)
                                }
                            ]
                        }
                    };
                default:
                    return {
                        jsonrpc: '2.0',
                        id,
                        error: {
                            code: -32601,
                            message: 'Method not found',
                            data: `Unknown method: ${method}`
                        }
                    };
            }
        }
        catch (error) {
            return {
                jsonrpc: '2.0',
                id,
                error: {
                    code: -32603,
                    message: 'Internal error',
                    data: error instanceof Error ? error.message : String(error)
                }
            };
        }
    }
    async callTool(name, args) {
        switch (name) {
            // 基础信息工具
            case 'hitrip_get_token':
                return await this.apiClient.getToken();
            case 'hitrip_get_cities':
                return await this.apiClient.getCities(args);
            case 'hitrip_get_airports':
                return await this.apiClient.getAirports(args);
            case 'hitrip_get_vehicle_types':
                return await this.apiClient.getVehicleTypes(args);
            // 报价工具
            case 'hitrip_pickup_quote':
                return await this.apiClient.getPickupQuote(args);
            case 'hitrip_dropoff_quote':
                return await this.apiClient.getDropoffQuote(args);
            case 'hitrip_charter_quote':
                return await this.apiClient.getCharterQuote(args);
            // 订单创建工具
            case 'hitrip_create_pickup_order':
                return await this.apiClient.createPickupOrder(args);
            case 'hitrip_create_dropoff_order':
                return await this.apiClient.createDropoffOrder(args);
            case 'hitrip_create_charter_order':
                return await this.apiClient.createCharterOrder(args);
            // 订单管理工具
            case 'hitrip_get_order_list':
                return await this.apiClient.getOrderList(args);
            case 'hitrip_get_order_detail':
                return await this.apiClient.getOrderDetail(args);
            case 'hitrip_cancel_order':
                return await this.apiClient.cancelOrder(args);
            default:
                throw new Error(`Tool not implemented: ${name}`);
        }
    }
    async start() {
        return new Promise((resolve, reject) => {
            this.server.listen(this.port, (error) => {
                if (error) {
                    reject(error);
                }
                else {
                    console.log(`🚀 Hitrip MCP HTTP 服务器启动成功!`);
                    console.log(`📍 服务地址: http://localhost:${this.port}`);
                    console.log(`🔗 WebSocket: ws://localhost:${this.port}/ws`);
                    console.log(`📋 工具列表: http://localhost:${this.port}/tools`);
                    console.log(`💚 健康检查: http://localhost:${this.port}/health`);
                    resolve();
                }
            });
        });
    }
    async stop() {
        return new Promise((resolve) => {
            if (this.wss) {
                this.wss.close(() => {
                    this.server.close(() => {
                        console.log('🛑 Hitrip MCP HTTP 服务器已关闭');
                        resolve();
                    });
                });
            }
            else {
                this.server.close(() => {
                    console.log('🛑 Hitrip MCP HTTP 服务器已关闭');
                    resolve();
                });
            }
        });
    }
}
//# sourceMappingURL=http-server.js.map