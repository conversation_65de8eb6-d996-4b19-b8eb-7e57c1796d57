{"version": 3, "file": "sse-server.js", "sourceRoot": "", "sources": ["../../src/server/sse-server.ts"], "names": [], "mappings": "AAAA,OAAO,OAAO,MAAM,SAAS,CAAC;AAC9B,OAAO,IAAI,MAAM,MAAM,CAAC;AACxB,OAAO,EAAE,YAAY,EAAE,MAAM,MAAM,CAAC;AACpC,OAAO,EAAE,eAAe,EAAE,MAAM,kBAAkB,CAAC;AACnD,OAAO,EAAE,iBAAiB,EAAE,MAAM,mBAAmB,CAAC;AAItD,MAAM,OAAO,eAAe;IAClB,GAAG,CAAsB;IACzB,MAAM,CAAM;IACZ,SAAS,CAAkB;IAC3B,KAAK,CAAS;IACd,IAAI,CAAS;IACb,iBAAiB,GAAkC,IAAI,GAAG,EAAE,CAAC;IAErE,YAAY,MAAoB,EAAE,OAAe,IAAI;QACnD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,GAAG,GAAG,OAAO,EAAE,CAAC;QACrB,IAAI,CAAC,SAAS,GAAG,IAAI,eAAe,CAAC,MAAM,CAAC,CAAC;QAC7C,IAAI,CAAC,KAAK,GAAG,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAE/C,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACvC,CAAC;IAEO,eAAe;QACrB,UAAU;QACV,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC;YAChB,MAAM,EAAE,GAAG;YACX,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC;YACnC,cAAc,EAAE,CAAC,cAAc,EAAE,eAAe,EAAE,eAAe,EAAE,QAAQ,CAAC;SAC7E,CAAC,CAAC,CAAC;QAEJ,UAAU;QACV,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;QAE9C,OAAO;QACP,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;YAC9B,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,MAAM,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;YACvE,IAAI,EAAE,CAAC;QACT,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,WAAW;QACjB,OAAO;QACP,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YACnC,GAAG,CAAC,IAAI,CAAC;gBACP,MAAM,EAAE,SAAS;gBACjB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,OAAO,EAAE,uBAAuB;gBAChC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI;aAC/C,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,kBAAkB;QAClB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YAChC,MAAM,YAAY,GAAG,QAAQ,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;YAErF,YAAY;YACZ,GAAG,CAAC,SAAS,CAAC,GAAG,EAAE;gBACjB,cAAc,EAAE,mBAAmB;gBACnC,eAAe,EAAE,UAAU;gBAC3B,YAAY,EAAE,YAAY;gBAC1B,6BAA6B,EAAE,GAAG;gBAClC,8BAA8B,EAAE,eAAe;aAChD,CAAC,CAAC;YAEH,SAAS;YACT,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,WAAW,EAAE;gBACpC,YAAY;gBACZ,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,OAAO,EAAE,4BAA4B;aACtC,CAAC,CAAC;YAEH,OAAO;YACP,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;YAC9C,OAAO,CAAC,GAAG,CAAC,aAAa,YAAY,YAAY,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,CAAC;YAEhF,SAAS;YACT,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,OAAO,EAAE;gBAChC,KAAK,EAAE,IAAI,CAAC,KAAK;aAClB,CAAC,CAAC;YAEH,SAAS;YACT,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;gBACnB,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;gBAC5C,OAAO,CAAC,GAAG,CAAC,aAAa,YAAY,YAAY,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,CAAC;YAClF,CAAC,CAAC,CAAC;YAEH,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;gBACxB,OAAO,CAAC,KAAK,CAAC,YAAY,YAAY,GAAG,EAAE,KAAK,CAAC,CAAC;gBAClD,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC;YAEH,SAAS;YACT,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE;gBACjC,IAAI,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC;oBAC7C,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;gBAC5E,CAAC;qBAAM,CAAC;oBACN,aAAa,CAAC,SAAS,CAAC,CAAC;gBAC3B,CAAC;YACH,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,aAAa;QAC1B,CAAC,CAAC,CAAC;QAEH,aAAa;QACb,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;YACvC,IAAI,CAAC;gBACH,MAAM,OAAO,GAAG,GAAG,CAAC,IAAI,CAAC;gBACzB,MAAM,YAAY,GAAG,GAAG,CAAC,OAAO,CAAC,iBAAiB,CAAW,CAAC;gBAE9D,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;gBAE5D,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;gBACtD,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;gBAE7D,oBAAoB;gBACpB,IAAI,YAAY,IAAI,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC;oBAC7D,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,YAAY,CAAE,CAAC;oBAChE,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;oBACzD,GAAG,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,cAAc,EAAE,CAAC,CAAC;gBACvC,CAAC;qBAAM,CAAC;oBACN,WAAW;oBACX,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACrB,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;gBACpC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,OAAO,EAAE,KAAK;oBACd,EAAE,EAAE,GAAG,CAAC,IAAI,EAAE,EAAE,IAAI,IAAI;oBACxB,KAAK,EAAE;wBACL,IAAI,EAAE,CAAC,KAAK;wBACZ,OAAO,EAAE,gBAAgB;wBACzB,IAAI,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;qBAC7D;iBACF,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,gBAAgB;QAChB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;YACxC,IAAI,CAAC;gBACH,GAAG,CAAC,IAAI,CAAC;oBACP,OAAO,EAAE,KAAK;oBACd,EAAE,EAAE,CAAC;oBACL,MAAM,EAAE;wBACN,KAAK,EAAE,IAAI,CAAC,KAAK;qBAClB;iBACF,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;gBAClC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,0BAA0B,EAAE,CAAC,CAAC;YAC9D,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,cAAc;QACd,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,kBAAkB,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;YACnD,IAAI,CAAC;gBACH,MAAM,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;gBAChC,MAAM,UAAU,GAAG,GAAG,CAAC,IAAI,CAAC;gBAE5B,MAAM,OAAO,GAAG;oBACd,OAAO,EAAE,KAAK;oBACd,EAAE,EAAE,IAAI,CAAC,GAAG,EAAE;oBACd,MAAM,EAAE,YAAY;oBACpB,MAAM,EAAE;wBACN,IAAI,EAAE,QAAQ;wBACd,SAAS,EAAE,UAAU;qBACtB;iBACF,CAAC;gBAEF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;gBACtD,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACrB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,QAAQ,GAAG,CAAC,MAAM,CAAC,QAAQ,MAAM,EAAE,KAAK,CAAC,CAAC;gBACxD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,qBAAqB,EAAE,CAAC,CAAC;YACzD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,YAAY;QACZ,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YACvC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;YAEhC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,YAAY,EAAE,EAAE;gBAC1D,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,IAAI,IAAI,WAAW,EAAE,IAAI,CAAC,CAAC;YAC7D,CAAC,CAAC,CAAC;YAEH,GAAG,CAAC,IAAI,CAAC;gBACP,MAAM,EAAE,aAAa;gBACrB,WAAW,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI;aACzC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,aAAa;QACb,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YAC7B,GAAG,CAAC,IAAI,CAAC;gBACP,IAAI,EAAE,uBAAuB;gBAC7B,OAAO,EAAE,OAAO;gBAChB,WAAW,EAAE,iDAAiD;gBAC9D,SAAS,EAAE;oBACT,MAAM,EAAE,aAAa;oBACrB,GAAG,EAAE,UAAU;oBACf,GAAG,EAAE,WAAW;oBAChB,KAAK,EAAE,YAAY;oBACnB,QAAQ,EAAE,uBAAuB;oBACjC,SAAS,EAAE,iBAAiB;iBAC7B;gBACD,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI;gBAC9C,aAAa,EAAE,gDAAgD;aAChE,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,cAAc,CAAC,GAAqB,EAAE,IAAY,EAAE,IAAS;QACnE,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,UAAU,IAAI,WAAW,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC;YACpE,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACrB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;QACvC,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,OAAY;QACzC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,OAAO,CAAC;QAEvC,IAAI,CAAC;YACH,QAAQ,MAAM,EAAE,CAAC;gBACf,KAAK,YAAY;oBACf,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,EAAE;wBACF,MAAM,EAAE;4BACN,KAAK,EAAE,IAAI,CAAC,KAAK;yBAClB;qBACF,CAAC;gBAEJ,KAAK,YAAY;oBACf,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC;oBACzC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;oBAC/C,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,EAAE;wBACF,MAAM,EAAE;4BACN,OAAO,EAAE;gCACP;oCACE,IAAI,EAAE,MAAM;oCACZ,IAAI,EAAE,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;iCAC5E;6BACF;yBACF;qBACF,CAAC;gBAEJ;oBACE,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,EAAE;wBACF,KAAK,EAAE;4BACL,IAAI,EAAE,CAAC,KAAK;4BACZ,OAAO,EAAE,kBAAkB;4BAC3B,IAAI,EAAE,mBAAmB,MAAM,EAAE;yBAClC;qBACF,CAAC;YACN,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,EAAE;gBACF,KAAK,EAAE;oBACL,IAAI,EAAE,CAAC,KAAK;oBACZ,OAAO,EAAE,gBAAgB;oBACzB,IAAI,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;iBAC7D;aACF,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,QAAQ,CAAC,IAAY,EAAE,IAAS;QAC5C,QAAQ,IAAI,EAAE,CAAC;YACb,SAAS;YACT,KAAK,kBAAkB;gBACrB,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;YAEzC,KAAK,mBAAmB;gBACtB,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAE9C,KAAK,qBAAqB;gBACxB,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YAEhD,KAAK,0BAA0B;gBAC7B,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAEpD,OAAO;YACP,KAAK,qBAAqB;gBACxB,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAEnD,KAAK,sBAAsB;gBACzB,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAEpD,KAAK,sBAAsB;gBACzB,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAEpD,SAAS;YACT,KAAK,4BAA4B;gBAC/B,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAEtD,KAAK,6BAA6B;gBAChC,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YAEvD,KAAK,6BAA6B;gBAChC,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YAEvD,SAAS;YACT,KAAK,uBAAuB;gBAC1B,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YAEjD,KAAK,yBAAyB;gBAC5B,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAEnD,KAAK,qBAAqB;gBACxB,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YAEhD;gBACE,MAAM,IAAI,KAAK,CAAC,yBAAyB,IAAI,EAAE,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,KAAK;QAChB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,KAAU,EAAE,EAAE;gBAC3C,IAAI,KAAK,EAAE,CAAC;oBACV,MAAM,CAAC,KAAK,CAAC,CAAC;gBAChB,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;oBAC1C,OAAO,CAAC,GAAG,CAAC,6BAA6B,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;oBACtD,OAAO,CAAC,GAAG,CAAC,+BAA+B,IAAI,CAAC,IAAI,MAAM,CAAC,CAAC;oBAC5D,OAAO,CAAC,GAAG,CAAC,6BAA6B,IAAI,CAAC,IAAI,QAAQ,CAAC,CAAC;oBAC5D,OAAO,CAAC,GAAG,CAAC,6BAA6B,IAAI,CAAC,IAAI,SAAS,CAAC,CAAC;oBAC7D,OAAO,EAAE,CAAC;gBACZ,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,IAAI;QACf,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,cAAc;YACd,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,YAAY,EAAE,EAAE;gBAC1D,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,YAAY,EAAE;oBAC5C,OAAO,EAAE,sBAAsB;iBAChC,CAAC,CAAC;gBACH,UAAU,CAAC,GAAG,EAAE,CAAC;YACnB,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;YAE/B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE;gBACrB,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;gBACxC,OAAO,EAAE,CAAC;YACZ,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;CACF"}