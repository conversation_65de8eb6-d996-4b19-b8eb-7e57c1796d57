import { HitripConfig } from '../types/config.js';
export declare class HitripSseServer {
    private app;
    private server;
    private apiClient;
    private tools;
    private port;
    private activeConnections;
    constructor(config: HitripConfig, port?: number);
    private setupMiddleware;
    private setupRoutes;
    private sendSSEMessage;
    private handleMcpRequest;
    private callTool;
    start(): Promise<void>;
    stop(): Promise<void>;
}
//# sourceMappingURL=sse-server.d.ts.map