import express from 'express';
import cors from 'cors';
import { createServer } from 'http';
import { HitripApiClient } from '../api/client.js';
import { createHitripTools } from '../tools/index.js';
export class HitripSseServer {
    app;
    server;
    apiClient;
    tools;
    port;
    activeConnections = new Map();
    constructor(config, port = 3000) {
        this.port = port;
        this.app = express();
        this.apiClient = new HitripApiClient(config);
        this.tools = createHitripTools(this.apiClient);
        this.setupMiddleware();
        this.setupRoutes();
        this.server = createServer(this.app);
    }
    setupMiddleware() {
        // 启用 CORS
        this.app.use(cors({
            origin: '*',
            methods: ['GET', 'POST', 'OPTIONS'],
            allowedHeaders: ['Content-Type', 'Authorization', 'Cache-Control', 'Accept']
        }));
        // 解析 JSON
        this.app.use(express.json({ limit: '10mb' }));
        // 请求日志
        this.app.use((req, res, next) => {
            console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
            next();
        });
    }
    setupRoutes() {
        // 健康检查
        this.app.get('/health', (req, res) => {
            res.json({
                status: 'healthy',
                timestamp: new Date().toISOString(),
                service: 'Hitrip MCP SSE Server',
                activeConnections: this.activeConnections.size
            });
        });
        // SSE 端点 - MCP 协议
        this.app.get('/sse', (req, res) => {
            const connectionId = `conn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            // 设置 SSE 头部
            res.writeHead(200, {
                'Content-Type': 'text/event-stream',
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Cache-Control'
            });
            // 发送连接确认
            this.sendSSEMessage(res, 'connected', {
                connectionId,
                timestamp: new Date().toISOString(),
                message: 'SSE connection established'
            });
            // 存储连接
            this.activeConnections.set(connectionId, res);
            console.log(`SSE 连接建立: ${connectionId}, 当前连接数: ${this.activeConnections.size}`);
            // 发送工具列表
            this.sendSSEMessage(res, 'tools', {
                tools: this.tools
            });
            // 处理连接关闭
            req.on('close', () => {
                this.activeConnections.delete(connectionId);
                console.log(`SSE 连接关闭: ${connectionId}, 当前连接数: ${this.activeConnections.size}`);
            });
            req.on('error', (error) => {
                console.error(`SSE 连接错误 ${connectionId}:`, error);
                this.activeConnections.delete(connectionId);
            });
            // 保持连接活跃
            const keepAlive = setInterval(() => {
                if (this.activeConnections.has(connectionId)) {
                    this.sendSSEMessage(res, 'ping', { timestamp: new Date().toISOString() });
                }
                else {
                    clearInterval(keepAlive);
                }
            }, 30000); // 每30秒发送一次心跳
        });
        // MCP 请求处理端点
        this.app.post('/mcp', async (req, res) => {
            try {
                const request = req.body;
                const connectionId = req.headers['x-connection-id'];
                console.log('收到 MCP 请求:', JSON.stringify(request, null, 2));
                const response = await this.handleMcpRequest(request);
                console.log('发送 MCP 响应:', JSON.stringify(response, null, 2));
                // 如果有连接ID，通过SSE发送响应
                if (connectionId && this.activeConnections.has(connectionId)) {
                    const sseConnection = this.activeConnections.get(connectionId);
                    this.sendSSEMessage(sseConnection, 'response', response);
                    res.json({ status: 'sent_via_sse' });
                }
                else {
                    // 否则直接返回响应
                    res.json(response);
                }
            }
            catch (error) {
                console.error('MCP 请求处理错误:', error);
                res.status(500).json({
                    jsonrpc: '2.0',
                    id: req.body?.id || null,
                    error: {
                        code: -32603,
                        message: 'Internal error',
                        data: error instanceof Error ? error.message : String(error)
                    }
                });
            }
        });
        // 获取工具列表 - 便捷端点
        this.app.get('/tools', async (req, res) => {
            try {
                res.json({
                    jsonrpc: '2.0',
                    id: 1,
                    result: {
                        tools: this.tools
                    }
                });
            }
            catch (error) {
                console.error('获取工具列表错误:', error);
                res.status(500).json({ error: 'Failed to get tools list' });
            }
        });
        // 调用工具 - 便捷端点
        this.app.post('/tools/:toolName', async (req, res) => {
            try {
                const { toolName } = req.params;
                const arguments_ = req.body;
                const request = {
                    jsonrpc: '2.0',
                    id: Date.now(),
                    method: 'tools/call',
                    params: {
                        name: toolName,
                        arguments: arguments_
                    }
                };
                const response = await this.handleMcpRequest(request);
                res.json(response);
            }
            catch (error) {
                console.error(`调用工具 ${req.params.toolName} 错误:`, error);
                res.status(500).json({ error: 'Failed to call tool' });
            }
        });
        // 广播消息到所有连接
        this.app.post('/broadcast', (req, res) => {
            const { type, data } = req.body;
            this.activeConnections.forEach((connection, connectionId) => {
                this.sendSSEMessage(connection, type || 'broadcast', data);
            });
            res.json({
                status: 'broadcasted',
                connections: this.activeConnections.size
            });
        });
        // 根路径 - 服务信息
        this.app.get('/', (req, res) => {
            res.json({
                name: 'Hitrip MCP SSE Server',
                version: '1.0.0',
                description: '黄包车 MCP 服务器 - SSE 模式，支持 Server-Sent Events 实时通信',
                endpoints: {
                    health: 'GET /health',
                    sse: 'GET /sse',
                    mcp: 'POST /mcp',
                    tools: 'GET /tools',
                    callTool: 'POST /tools/:toolName',
                    broadcast: 'POST /broadcast'
                },
                activeConnections: this.activeConnections.size,
                documentation: 'https://github.com/your-repo/hitrip-mcp-server'
            });
        });
    }
    sendSSEMessage(res, type, data) {
        try {
            const message = `event: ${type}\ndata: ${JSON.stringify(data)}\n\n`;
            res.write(message);
        }
        catch (error) {
            console.error('发送 SSE 消息失败:', error);
        }
    }
    async handleMcpRequest(request) {
        const { method, params, id } = request;
        try {
            switch (method) {
                case 'tools/list':
                    return {
                        jsonrpc: '2.0',
                        id,
                        result: {
                            tools: this.tools
                        }
                    };
                case 'tools/call':
                    const { name, arguments: args } = params;
                    const result = await this.callTool(name, args);
                    return {
                        jsonrpc: '2.0',
                        id,
                        result: {
                            content: [
                                {
                                    type: 'text',
                                    text: typeof result === 'string' ? result : JSON.stringify(result, null, 2)
                                }
                            ]
                        }
                    };
                default:
                    return {
                        jsonrpc: '2.0',
                        id,
                        error: {
                            code: -32601,
                            message: 'Method not found',
                            data: `Unknown method: ${method}`
                        }
                    };
            }
        }
        catch (error) {
            return {
                jsonrpc: '2.0',
                id,
                error: {
                    code: -32603,
                    message: 'Internal error',
                    data: error instanceof Error ? error.message : String(error)
                }
            };
        }
    }
    async callTool(name, args) {
        switch (name) {
            // 基础信息工具
            case 'hitrip_get_token':
                return await this.apiClient.getToken();
            case 'hitrip_get_cities':
                return await this.apiClient.getCities(args);
            case 'hitrip_get_airports':
                return await this.apiClient.getAirports(args);
            case 'hitrip_get_vehicle_types':
                return await this.apiClient.getVehicleTypes(args);
            // 报价工具
            case 'hitrip_pickup_quote':
                return await this.apiClient.getPickupQuote(args);
            case 'hitrip_dropoff_quote':
                return await this.apiClient.getDropoffQuote(args);
            case 'hitrip_charter_quote':
                return await this.apiClient.getCharterQuote(args);
            // 订单创建工具
            case 'hitrip_create_pickup_order':
                return await this.apiClient.createPickupOrder(args);
            case 'hitrip_create_dropoff_order':
                return await this.apiClient.createDropoffOrder(args);
            case 'hitrip_create_charter_order':
                return await this.apiClient.createCharterOrder(args);
            // 订单管理工具
            case 'hitrip_get_order_list':
                return await this.apiClient.getOrderList(args);
            case 'hitrip_get_order_detail':
                return await this.apiClient.getOrderDetail(args);
            case 'hitrip_cancel_order':
                return await this.apiClient.cancelOrder(args);
            default:
                throw new Error(`Tool not implemented: ${name}`);
        }
    }
    async start() {
        return new Promise((resolve, reject) => {
            this.server.listen(this.port, (error) => {
                if (error) {
                    reject(error);
                }
                else {
                    console.log(`🚀 Hitrip MCP SSE 服务器启动成功!`);
                    console.log(`📍 服务地址: http://localhost:${this.port}`);
                    console.log(`🔗 SSE 端点: http://localhost:${this.port}/sse`);
                    console.log(`📋 工具列表: http://localhost:${this.port}/tools`);
                    console.log(`💚 健康检查: http://localhost:${this.port}/health`);
                    resolve();
                }
            });
        });
    }
    async stop() {
        return new Promise((resolve) => {
            // 关闭所有 SSE 连接
            this.activeConnections.forEach((connection, connectionId) => {
                this.sendSSEMessage(connection, 'disconnect', {
                    message: 'Server shutting down'
                });
                connection.end();
            });
            this.activeConnections.clear();
            this.server.close(() => {
                console.log('🛑 Hitrip MCP SSE 服务器已关闭');
                resolve();
            });
        });
    }
}
//# sourceMappingURL=sse-server.js.map