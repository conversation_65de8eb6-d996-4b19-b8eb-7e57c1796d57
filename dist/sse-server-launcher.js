#!/usr/bin/env node
import { HitripSseServer } from './server/sse-server.js';
import { config } from './config/index.js';
async function main() {
    try {
        console.log('🚀 启动 Hitrip MCP SSE 服务器...\n');
        // 显示运行环境信息
        console.log(`Node.js 版本: ${process.version}`);
        console.log(`工作目录: ${process.cwd()}`);
        console.log('配置加载完成\n');
        // 获取端口配置
        const port = parseInt(process.env.PORT || '3000', 10);
        // 创建并启动 SSE 服务器
        const sseServer = new HitripSseServer(config, port);
        // 优雅关闭处理
        const gracefulShutdown = async (signal) => {
            console.log(`\n收到 ${signal} 信号，正在优雅关闭服务器...`);
            try {
                await sseServer.stop();
                process.exit(0);
            }
            catch (error) {
                console.error('关闭服务器时出错:', error);
                process.exit(1);
            }
        };
        process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
        process.on('SIGINT', () => gracefulShutdown('SIGINT'));
        // 启动服务器
        await sseServer.start();
    }
    catch (error) {
        console.error('❌ 启动 SSE 服务器失败:', error);
        process.exit(1);
    }
}
// 处理未捕获的异常
process.on('uncaughtException', (error) => {
    console.error('未捕获的异常:', error);
    process.exit(1);
});
process.on('unhandledRejection', (reason, promise) => {
    console.error('未处理的 Promise 拒绝:', reason);
    process.exit(1);
});
main().catch((error) => {
    console.error('主函数执行失败:', error);
    process.exit(1);
});
//# sourceMappingURL=sse-server-launcher.js.map